"use client";

import { motion } from "framer-motion";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Dialog, DialogContent, DialogTrigger } from "@/components/ui/dialog";
import { Camera } from "lucide-react";
import { featuredImages } from "@/data/gallery";
import Link from "next/link";

export function GalleryPreview() {

  return (
    <section className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="font-serif text-4xl md:text-5xl font-bold text-cafe-dark-brown mb-6">
            Gallery
          </h2>
          <p className="text-xl text-cafe-brown max-w-3xl mx-auto leading-relaxed">
            Take a visual journey through our cozy café, delicious food, and the warm moments shared by our community.
          </p>
        </motion.div>

        {/* Gallery Grid */}
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 mb-12">
          {featuredImages.map((image, index) => (
            <motion.div
              key={image.id}
              initial={{ opacity: 0, scale: 0.9 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              viewport={{ once: true }}
              className={`relative group cursor-pointer overflow-hidden rounded-lg ${
                index === 0 ? 'col-span-2 row-span-2' : 
                index === 3 ? 'col-span-2' : ''
              }`}
            >
              <Dialog>
                <DialogTrigger asChild>
                  <div className="relative aspect-square overflow-hidden rounded-lg">
                    <img
                      src={image.src}
                      alt={image.alt}
                      className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"
                    />
                    
                    {/* Overlay */}
                    <div className="absolute inset-0 bg-black/0 group-hover:bg-black/40 transition-colors duration-300 flex items-center justify-center">
                      <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                        <div className="w-12 h-12 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center">
                          <Camera className="h-6 w-6 text-white" />
                        </div>
                      </div>
                    </div>

                    {/* Category Badge */}
                    <div className="absolute top-2 left-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                      <span className="bg-cafe-brown/80 text-cafe-cream text-xs px-2 py-1 rounded-full backdrop-blur-sm">
                        {image.category}
                      </span>
                    </div>
                  </div>
                </DialogTrigger>

                <DialogContent className="max-w-4xl w-full p-0 border-0">
                  <div className="relative">
                    <img
                      src={image.src}
                      alt={image.alt}
                      className="w-full h-auto max-h-[80vh] object-contain"
                    />
                    <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/60 to-transparent p-6">
                      <h3 className="text-white font-serif text-xl font-semibold mb-2">
                        {image.title}
                      </h3>
                      {image.description && (
                        <p className="text-white/90 text-sm">
                          {image.description}
                        </p>
                      )}
                    </div>
                  </div>
                </DialogContent>
              </Dialog>
            </motion.div>
          ))}
        </div>

        {/* View All Button */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
          viewport={{ once: true }}
          className="text-center"
        >
          <Link href="/gallery">
            <Button 
              size="lg" 
              className="bg-cafe-brown hover:bg-cafe-dark-brown text-cafe-cream px-8 py-3 text-lg font-semibold shadow-cafe-lg"
            >
              <Camera className="h-5 w-5 mr-2" />
              View Full Gallery
            </Button>
          </Link>
        </motion.div>
      </div>
    </section>
  );
}
