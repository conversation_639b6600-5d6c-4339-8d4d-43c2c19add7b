"use client";

import { motion } from "framer-motion";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Check, Star, Gift } from "lucide-react";
import { popularPackages, calculateDiscountPercentage } from "@/data/packages";

export function PackagesSection() {
  return (
    <section className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="font-serif text-4xl md:text-5xl font-bold text-cafe-dark-brown mb-6">
            Special Packages
          </h2>
          <p className="text-xl text-cafe-brown max-w-3xl mx-auto leading-relaxed">
            Enjoy great savings with our carefully curated packages, perfect for every occasion and craving.
          </p>
        </motion.div>

        {/* Packages Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {popularPackages.map((pkg, index) => {
            const discountPercentage = calculateDiscountPercentage(pkg.originalPrice, pkg.discountedPrice);
            
            return (
              <motion.div
                key={pkg.id}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <Card className="h-full border-cafe-beige hover:shadow-cafe-lg transition-all duration-300 relative overflow-hidden">
                  {/* Popular Badge */}
                  {pkg.isPopular && (
                    <div className="absolute top-4 right-4 z-10">
                      <Badge className="bg-cafe-gold text-cafe-dark-brown font-semibold">
                        <Star className="h-3 w-3 mr-1" />
                        Popular
                      </Badge>
                    </div>
                  )}

                  {/* Discount Badge */}
                  <div className="absolute top-4 left-4 z-10">
                    <Badge className="bg-cafe-green text-white font-semibold">
                      {discountPercentage}% OFF
                    </Badge>
                  </div>

                  {/* Package Image */}
                  <div className="aspect-[4/3] overflow-hidden">
                    <img
                      src={pkg.image}
                      alt={pkg.name}
                      className="w-full h-full object-cover"
                    />
                  </div>

                  <CardHeader className="pb-4">
                    <CardTitle className="font-serif text-xl text-cafe-dark-brown flex items-center gap-2">
                      <Gift className="h-5 w-5 text-cafe-brown" />
                      {pkg.name}
                    </CardTitle>
                    <p className="text-cafe-brown text-sm leading-relaxed">
                      {pkg.description}
                    </p>
                  </CardHeader>

                  <CardContent className="space-y-4">
                    {/* Pricing */}
                    <div className="flex items-center gap-3">
                      <span className="text-2xl font-bold text-cafe-dark-brown">
                        Rs. {pkg.discountedPrice}
                      </span>
                      <span className="text-lg text-cafe-brown line-through">
                        Rs. {pkg.originalPrice}
                      </span>
                      <Badge variant="secondary" className="bg-cafe-beige text-cafe-brown">
                        Save Rs. {pkg.savings}
                      </Badge>
                    </div>

                    {/* Items Included */}
                    <div className="space-y-2">
                      <h4 className="font-semibold text-cafe-dark-brown text-sm">
                        What&apos;s Included:
                      </h4>
                      <ul className="space-y-1">
                        {pkg.items.map((item, itemIndex) => (
                          <li key={itemIndex} className="flex items-start gap-2 text-sm text-cafe-brown">
                            <Check className="h-4 w-4 text-cafe-green mt-0.5 flex-shrink-0" />
                            <span>{item}</span>
                          </li>
                        ))}
                      </ul>
                    </div>

                    {/* Valid Until */}
                    <div className="text-xs text-cafe-brown">
                      Valid until: {new Date(pkg.validUntil).toLocaleDateString()}
                    </div>

                    {/* CTA Button */}
                    <Button 
                      className="w-full bg-cafe-brown hover:bg-cafe-dark-brown text-cafe-cream font-semibold"
                      size="sm"
                    >
                      Order Now
                    </Button>
                  </CardContent>
                </Card>
              </motion.div>
            );
          })}
        </div>

        {/* View All Packages Button */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
          viewport={{ once: true }}
          className="text-center mt-12"
        >
          <Button 
            variant="outline"
            size="lg" 
            className="border-cafe-brown text-cafe-brown hover:bg-cafe-brown hover:text-cafe-cream px-8 py-3 text-lg font-semibold"
          >
            View All Packages
          </Button>
        </motion.div>
      </div>
    </section>
  );
}
