@import "tailwindcss/preflight";
@import "tailwindcss/utilities";

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Playfair+Display:wght@400;500;600;700&display=swap');

:root {
  --radius: 0.5rem;

  /* Café-themed color palette */
  --background: 245 241 235; /* #F5F1EB - Cream */
  --foreground: 93 78 55; /* #5D4E37 - Dark coffee brown */
  --card: 255 255 255; /* White cards for contrast */
  --card-foreground: 93 78 55; /* #5D4E37 - Dark coffee brown */
  --popover: 255 255 255;
  --popover-foreground: 93 78 55;
  --primary: 139 111 71; /* #8B6F47 - Medium brown */
  --primary-foreground: 245 241 235; /* #F5F1EB - Cream */
  --secondary: 232 221 212; /* #E8DDD4 - Warm beige */
  --secondary-foreground: 93 78 55; /* #5D4E37 - Dark coffee brown */
  --muted: 212 196 176; /* #D4C4B0 - Sandy beige */
  --muted-foreground: 122 132 113; /* #7A8471 - Muted sage green */
  --accent: 212 175 55; /* #D4AF37 - Warm gold accent */
  --accent-foreground: 93 78 55; /* #5D4E37 - Dark coffee brown */
  --destructive: 220 38 38;
  --destructive-foreground: 248 250 252;
  --border: 212 196 176; /* #D4C4B0 - Sandy beige */
  --input: 232 221 212; /* #E8DDD4 - Warm beige */
  --ring: 139 111 71; /* #8B6F47 - Medium brown */
}

@layer base {
  * {
    @apply border-cafe-beige;
  }

  body {
    @apply bg-cafe-cream text-cafe-dark-brown font-sans;
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-serif text-cafe-dark-brown;
  }

  h1 {
    @apply text-4xl md:text-5xl lg:text-6xl font-bold leading-tight;
  }

  h2 {
    @apply text-3xl md:text-4xl font-semibold leading-tight;
  }

  h3 {
    @apply text-2xl md:text-3xl font-medium leading-snug;
  }

  p {
    @apply text-cafe-brown leading-relaxed;
  }

  .text-gradient {
    @apply bg-gradient-to-r from-cafe-brown to-cafe-gold bg-clip-text text-transparent;
  }

  .bg-cafe-pattern {
    background-image:
      radial-gradient(circle at 25% 25%, rgba(139, 111, 71, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 75% 75%, rgba(122, 132, 113, 0.1) 0%, transparent 50%);
  }

  .shadow-cafe {
    box-shadow: 0 4px 6px -1px rgba(139, 111, 71, 0.1), 0 2px 4px -1px rgba(139, 111, 71, 0.06);
  }

  .shadow-cafe-lg {
    box-shadow: 0 10px 15px -3px rgba(139, 111, 71, 0.1), 0 4px 6px -2px rgba(139, 111, 71, 0.05);
  }
}
