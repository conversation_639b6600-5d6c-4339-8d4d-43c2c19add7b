@import "tailwindcss";

@theme {
  --color-cafe-cream: #F5F1EB;
  --color-cafe-beige: #E8DDD4;
  --color-cafe-sand: #D4C4B0;
  --color-cafe-brown: #8B6F47;
  --color-cafe-dark-brown: #5D4E37;
  --color-cafe-green: #7A8471;
  --color-cafe-dark-green: #5F6B56;
  --color-cafe-gold: #D4AF37;

  --radius: 0.5rem;

  /* ShadCN color palette */
  --color-background: #F5F1EB;
  --color-foreground: #5D4E37;
  --color-card: #FFFFFF;
  --color-card-foreground: #5D4E37;
  --color-popover: #FFFFFF;
  --color-popover-foreground: #5D4E37;
  --color-primary: #8B6F47;
  --color-primary-foreground: #F5F1EB;
  --color-secondary: #E8DDD4;
  --color-secondary-foreground: #5D4E37;
  --color-muted: #D4C4B0;
  --color-muted-foreground: #7A8471;
  --color-accent: #D4AF37;
  --color-accent-foreground: #5D4E37;
  --color-destructive: #dc2626;
  --color-destructive-foreground: #f8fafc;
  --color-border: #D4C4B0;
  --color-input: #E8DDD4;
  --color-ring: #8B6F47;
}

body {
  background-color: var(--color-cafe-cream);
  color: var(--color-cafe-dark-brown);
  font-family: var(--font-inter), system-ui, sans-serif;
  font-feature-settings: "rlig" 1, "calt" 1;
}

h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-playfair), Georgia, serif;
  color: var(--color-cafe-dark-brown);
}

h1 {
  font-size: 2.25rem;
  font-weight: 700;
  line-height: 1.2;
}

@media (min-width: 768px) {
  h1 {
    font-size: 3rem;
  }
}

@media (min-width: 1024px) {
  h1 {
    font-size: 3.75rem;
  }
}

h2 {
  font-size: 1.875rem;
  font-weight: 600;
  line-height: 1.3;
}

@media (min-width: 768px) {
  h2 {
    font-size: 2.25rem;
  }
}

h3 {
  font-size: 1.5rem;
  font-weight: 500;
  line-height: 1.4;
}

@media (min-width: 768px) {
  h3 {
    font-size: 1.875rem;
  }
}

p {
  color: var(--color-cafe-brown);
  line-height: 1.625;
}

.text-gradient {
  background: linear-gradient(to right, var(--color-cafe-brown), var(--color-cafe-gold));
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
}

.bg-cafe-pattern {
  background-image:
    radial-gradient(circle at 25% 25%, rgba(139, 111, 71, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(122, 132, 113, 0.1) 0%, transparent 50%);
}

.shadow-cafe {
  box-shadow: 0 4px 6px -1px rgba(139, 111, 71, 0.1), 0 2px 4px -1px rgba(139, 111, 71, 0.06);
}

.shadow-cafe-lg {
  box-shadow: 0 10px 15px -3px rgba(139, 111, 71, 0.1), 0 4px 6px -2px rgba(139, 111, 71, 0.05);
}
