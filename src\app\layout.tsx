import type { Metadata } from "next";
import { Inter, Playfair_Display } from "next/font/google";
import "./globals.css";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
});

const playfair = Playfair_Display({
  subsets: ["latin"],
  variable: "--font-playfair",
});

export const metadata: Metadata = {
  title: "Himalayan Brew Café - Best Café in Biratnagar | Cozy Coffee Shop Nepal",
  description: "Experience the finest coffee and authentic Nepali hospitality at Himalayan Brew Café in Biratnagar. Serving premium coffee, traditional momos, and creating memorable moments since 2020.",
  keywords: "café in Biratnagar, best café Biratnagar, cozy coffee shop Biratnagar, Himalayan Brew, Nepal coffee, momos Biratnagar, coffee shop Nepal",
  authors: [{ name: "Himalayan Brew Café" }],
  openGraph: {
    title: "Himalayan Brew Café - Where Mountains Meet Coffee",
    description: "A cozy corner in the heart of Biratnagar, serving authentic Nepali hospitality with the finest coffee and local delicacies.",
    url: "https://himalayanbrew.com.np",
    siteName: "Himalayan Brew Café",
    locale: "en_US",
    type: "website",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${inter.variable} ${playfair.variable} font-sans antialiased`}
      >
        {children}
      </body>
    </html>
  );
}
