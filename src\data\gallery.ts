export interface GalleryImage {
  id: string;
  src: string;
  alt: string;
  title: string;
  category: 'interior' | 'food' | 'drinks' | 'people' | 'exterior';
  description?: string;
}

export const galleryImages: GalleryImage[] = [
  // Interior
  {
    id: "g1",
    src: "https://images.unsplash.com/photo-1554118811-1e0d58224f24?w=600&h=400&fit=crop",
    alt: "Main seating area with cozy wooden furniture",
    title: "Cozy Main Seating Area",
    category: "interior",
    description: "Our warm and inviting main seating area with handcrafted wooden furniture"
  },
  {
    id: "g2",
    src: "https://images.unsplash.com/photo-1559925393-8be0ec4767c8?w=600&h=400&fit=crop",
    alt: "Corner reading nook with books and plants",
    title: "Reading Corner",
    category: "interior",
    description: "Perfect corner for book lovers with natural lighting and plants"
  },
  {
    id: "g3",
    src: "https://images.unsplash.com/photo-1442975631115-c4f7b05b8a2c?w=600&h=400&fit=crop",
    alt: "Coffee counter with barista equipment",
    title: "Coffee Counter",
    category: "interior",
    description: "Our professional coffee counter where magic happens"
  },
  {
    id: "g4",
    src: "https://images.unsplash.com/photo-1521017432531-fbd92d768814?w=600&h=400&fit=crop",
    alt: "Upstairs seating area with mountain views",
    title: "Upstairs Seating",
    category: "interior",
    description: "Second floor seating with beautiful mountain views"
  },

  // Food
  {
    id: "g5",
    src: "https://images.unsplash.com/photo-1496116218417-1a781b1c416c?w=600&h=400&fit=crop",
    alt: "Fresh steamed momos with sauce",
    title: "Traditional Momos",
    category: "food",
    description: "Our signature momos, handmade fresh daily"
  },
  {
    id: "g6",
    src: "https://images.unsplash.com/photo-1585937421612-70a008356fbe?w=600&h=400&fit=crop",
    alt: "Traditional dal bhat set meal",
    title: "Dal Bhat Set",
    category: "food",
    description: "Authentic Nepali dal bhat with fresh vegetables"
  },
  {
    id: "g7",
    src: "https://images.unsplash.com/photo-1555507036-ab794f4afe5e?w=600&h=400&fit=crop",
    alt: "Fresh baked pastries and bread",
    title: "Fresh Pastries",
    category: "food",
    description: "Daily baked pastries and bread made in-house"
  },
  {
    id: "g8",
    src: "https://images.unsplash.com/photo-1525351484163-7529414344d8?w=600&h=400&fit=crop",
    alt: "English breakfast plate",
    title: "Hearty Breakfast",
    category: "food",
    description: "Start your day with our hearty breakfast options"
  },

  // Drinks
  {
    id: "g9",
    src: "https://images.unsplash.com/photo-1514432324607-a09d9b4aefdd?w=600&h=400&fit=crop",
    alt: "Latte with beautiful coffee art",
    title: "Coffee Art",
    category: "drinks",
    description: "Our baristas create beautiful latte art with every cup"
  },
  {
    id: "g10",
    src: "https://images.unsplash.com/photo-1571934811356-5cc061b6821f?w=600&h=400&fit=crop",
    alt: "Traditional masala chai in glass",
    title: "Masala Chai",
    category: "drinks",
    description: "Traditional spiced chai served the authentic way"
  },
  {
    id: "g11",
    src: "https://images.unsplash.com/photo-1461023058943-07fcbe16d735?w=600&h=400&fit=crop",
    alt: "Iced coffee with condensed milk",
    title: "Iced Himalayan Coffee",
    category: "drinks",
    description: "Our signature coffee served cold and refreshing"
  },
  {
    id: "g12",
    src: "https://images.unsplash.com/photo-1495474472287-4d71bcdd2085?w=600&h=400&fit=crop",
    alt: "Various drinks on wooden table",
    title: "Drink Selection",
    category: "drinks",
    description: "Wide variety of hot and cold beverages"
  },

  // People
  {
    id: "g13",
    src: "/images/gallery/people-friends.jpg",
    alt: "Friends enjoying coffee together",
    title: "Friends Gathering",
    category: "people",
    description: "Friends enjoying quality time over coffee"
  },
  {
    id: "g14",
    src: "/images/gallery/people-study.jpg",
    alt: "Students studying with laptops",
    title: "Study Session",
    category: "people",
    description: "Students finding the perfect study environment"
  },
  {
    id: "g15",
    src: "/images/gallery/people-family.jpg",
    alt: "Family enjoying meal together",
    title: "Family Time",
    category: "people",
    description: "Families creating memories over delicious food"
  },
  {
    id: "g16",
    src: "/images/gallery/people-barista.jpg",
    alt: "Barista preparing coffee",
    title: "Our Barista",
    category: "people",
    description: "Skilled baristas crafting the perfect cup"
  },

  // Exterior
  {
    id: "g17",
    src: "/images/gallery/exterior-front.jpg",
    alt: "Café exterior with signage",
    title: "Café Exterior",
    category: "exterior",
    description: "Welcome to Himalayan Brew Café on Main Road"
  },
  {
    id: "g18",
    src: "/images/gallery/exterior-terrace.jpg",
    alt: "Outdoor terrace seating",
    title: "Outdoor Terrace",
    category: "exterior",
    description: "Enjoy your coffee in our outdoor terrace area"
  },
  {
    id: "g19",
    src: "/images/gallery/exterior-evening.jpg",
    alt: "Café in the evening with warm lighting",
    title: "Evening Ambiance",
    category: "exterior",
    description: "Cozy evening atmosphere with warm lighting"
  },
  {
    id: "g20",
    src: "/images/gallery/exterior-street.jpg",
    alt: "Street view of the café location",
    title: "Street View",
    category: "exterior",
    description: "Located in the heart of Biratnagar's main road"
  }
];

export const getImagesByCategory = (category: GalleryImage['category']) => {
  return galleryImages.filter(image => image.category === category);
};

export const featuredImages = galleryImages.slice(0, 8);

export const galleryCategories = [
  { key: 'all', label: 'All Photos' },
  { key: 'interior', label: 'Interior' },
  { key: 'food', label: 'Food' },
  { key: 'drinks', label: 'Drinks' },
  { key: 'people', label: 'People' },
  { key: 'exterior', label: 'Exterior' }
] as const;
