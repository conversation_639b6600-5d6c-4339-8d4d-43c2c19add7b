export interface GalleryImage {
  id: string;
  src: string;
  alt: string;
  title: string;
  category: 'interior' | 'food' | 'drinks' | 'people' | 'exterior';
  description?: string;
}

export const galleryImages: GalleryImage[] = [
  // Interior
  {
    id: "g1",
    src: "/images/gallery/interior-main.jpg",
    alt: "Main seating area with cozy wooden furniture",
    title: "Cozy Main Seating Area",
    category: "interior",
    description: "Our warm and inviting main seating area with handcrafted wooden furniture"
  },
  {
    id: "g2", 
    src: "/images/gallery/interior-corner.jpg",
    alt: "Corner reading nook with books and plants",
    title: "Reading Corner",
    category: "interior",
    description: "Perfect corner for book lovers with natural lighting and plants"
  },
  {
    id: "g3",
    src: "/images/gallery/interior-counter.jpg", 
    alt: "Coffee counter with barista equipment",
    title: "Coffee Counter",
    category: "interior",
    description: "Our professional coffee counter where magic happens"
  },
  {
    id: "g4",
    src: "/images/gallery/interior-upstairs.jpg",
    alt: "Upstairs seating area with mountain views",
    title: "Upstairs Seating",
    category: "interior", 
    description: "Second floor seating with beautiful mountain views"
  },

  // Food
  {
    id: "g5",
    src: "/images/gallery/food-momos.jpg",
    alt: "Fresh steamed momos with sauce",
    title: "Traditional Momos",
    category: "food",
    description: "Our signature momos, handmade fresh daily"
  },
  {
    id: "g6",
    src: "/images/gallery/food-dal-bhat.jpg",
    alt: "Traditional dal bhat set meal",
    title: "Dal Bhat Set",
    category: "food",
    description: "Authentic Nepali dal bhat with fresh vegetables"
  },
  {
    id: "g7",
    src: "/images/gallery/food-pastries.jpg",
    alt: "Fresh baked pastries and bread",
    title: "Fresh Pastries",
    category: "food",
    description: "Daily baked pastries and bread made in-house"
  },
  {
    id: "g8",
    src: "/images/gallery/food-breakfast.jpg",
    alt: "English breakfast plate",
    title: "Hearty Breakfast",
    category: "food",
    description: "Start your day with our hearty breakfast options"
  },

  // Drinks
  {
    id: "g9",
    src: "/images/gallery/drinks-coffee-art.jpg",
    alt: "Latte with beautiful coffee art",
    title: "Coffee Art",
    category: "drinks",
    description: "Our baristas create beautiful latte art with every cup"
  },
  {
    id: "g10",
    src: "/images/gallery/drinks-chai.jpg",
    alt: "Traditional masala chai in glass",
    title: "Masala Chai",
    category: "drinks",
    description: "Traditional spiced chai served the authentic way"
  },
  {
    id: "g11",
    src: "/images/gallery/drinks-cold-brew.jpg",
    alt: "Iced coffee with condensed milk",
    title: "Iced Himalayan Coffee",
    category: "drinks",
    description: "Our signature coffee served cold and refreshing"
  },
  {
    id: "g12",
    src: "/images/gallery/drinks-variety.jpg",
    alt: "Various drinks on wooden table",
    title: "Drink Selection",
    category: "drinks",
    description: "Wide variety of hot and cold beverages"
  },

  // People
  {
    id: "g13",
    src: "/images/gallery/people-friends.jpg",
    alt: "Friends enjoying coffee together",
    title: "Friends Gathering",
    category: "people",
    description: "Friends enjoying quality time over coffee"
  },
  {
    id: "g14",
    src: "/images/gallery/people-study.jpg",
    alt: "Students studying with laptops",
    title: "Study Session",
    category: "people",
    description: "Students finding the perfect study environment"
  },
  {
    id: "g15",
    src: "/images/gallery/people-family.jpg",
    alt: "Family enjoying meal together",
    title: "Family Time",
    category: "people",
    description: "Families creating memories over delicious food"
  },
  {
    id: "g16",
    src: "/images/gallery/people-barista.jpg",
    alt: "Barista preparing coffee",
    title: "Our Barista",
    category: "people",
    description: "Skilled baristas crafting the perfect cup"
  },

  // Exterior
  {
    id: "g17",
    src: "/images/gallery/exterior-front.jpg",
    alt: "Café exterior with signage",
    title: "Café Exterior",
    category: "exterior",
    description: "Welcome to Himalayan Brew Café on Main Road"
  },
  {
    id: "g18",
    src: "/images/gallery/exterior-terrace.jpg",
    alt: "Outdoor terrace seating",
    title: "Outdoor Terrace",
    category: "exterior",
    description: "Enjoy your coffee in our outdoor terrace area"
  },
  {
    id: "g19",
    src: "/images/gallery/exterior-evening.jpg",
    alt: "Café in the evening with warm lighting",
    title: "Evening Ambiance",
    category: "exterior",
    description: "Cozy evening atmosphere with warm lighting"
  },
  {
    id: "g20",
    src: "/images/gallery/exterior-street.jpg",
    alt: "Street view of the café location",
    title: "Street View",
    category: "exterior",
    description: "Located in the heart of Biratnagar's main road"
  }
];

export const getImagesByCategory = (category: GalleryImage['category']) => {
  return galleryImages.filter(image => image.category === category);
};

export const featuredImages = galleryImages.slice(0, 8);

export const galleryCategories = [
  { key: 'all', label: 'All Photos' },
  { key: 'interior', label: 'Interior' },
  { key: 'food', label: 'Food' },
  { key: 'drinks', label: 'Drinks' },
  { key: 'people', label: 'People' },
  { key: 'exterior', label: 'Exterior' }
] as const;
