(function(){"use strict";var e={879:function(e,t){const{hasOwnProperty:n}=Object.prototype;const r=configure();r.configure=configure;r.stringify=r;r.default=r;t.stringify=r;t.configure=configure;e.exports=r;const i=/[\u0000-\u001f\u0022\u005c\ud800-\udfff]/;function strEscape(e){if(e.length<5e3&&!i.test(e)){return`"${e}"`}return JSON.stringify(e)}function sort(e,t){if(e.length>200||t){return e.sort(t)}for(let t=1;t<e.length;t++){const n=e[t];let r=t;while(r!==0&&e[r-1]>n){e[r]=e[r-1];r--}e[r]=n}return e}const f=Object.getOwnPropertyDescriptor(Object.getPrototypeOf(Object.getPrototypeOf(new Int8Array)),Symbol.toStringTag).get;function isTypedArrayWithEntries(e){return f.call(e)!==undefined&&e.length!==0}function stringifyTypedArray(e,t,n){if(e.length<n){n=e.length}const r=t===","?"":" ";let i=`"0":${r}${e[0]}`;for(let f=1;f<n;f++){i+=`${t}"${f}":${r}${e[f]}`}return i}function getCircularValueOption(e){if(n.call(e,"circularValue")){const t=e.circularValue;if(typeof t==="string"){return`"${t}"`}if(t==null){return t}if(t===Error||t===TypeError){return{toString(){throw new TypeError("Converting circular structure to JSON")}}}throw new TypeError('The "circularValue" argument must be of type string or the value null or undefined')}return'"[Circular]"'}function getDeterministicOption(e){let t;if(n.call(e,"deterministic")){t=e.deterministic;if(typeof t!=="boolean"&&typeof t!=="function"){throw new TypeError('The "deterministic" argument must be of type boolean or comparator function')}}return t===undefined?true:t}function getBooleanOption(e,t){let r;if(n.call(e,t)){r=e[t];if(typeof r!=="boolean"){throw new TypeError(`The "${t}" argument must be of type boolean`)}}return r===undefined?true:r}function getPositiveIntegerOption(e,t){let r;if(n.call(e,t)){r=e[t];if(typeof r!=="number"){throw new TypeError(`The "${t}" argument must be of type number`)}if(!Number.isInteger(r)){throw new TypeError(`The "${t}" argument must be an integer`)}if(r<1){throw new RangeError(`The "${t}" argument must be >= 1`)}}return r===undefined?Infinity:r}function getItemCount(e){if(e===1){return"1 item"}return`${e} items`}function getUniqueReplacerSet(e){const t=new Set;for(const n of e){if(typeof n==="string"||typeof n==="number"){t.add(String(n))}}return t}function getStrictOption(e){if(n.call(e,"strict")){const t=e.strict;if(typeof t!=="boolean"){throw new TypeError('The "strict" argument must be of type boolean')}if(t){return e=>{let t=`Object can not safely be stringified. Received type ${typeof e}`;if(typeof e!=="function")t+=` (${e.toString()})`;throw new Error(t)}}}}function configure(e){e={...e};const t=getStrictOption(e);if(t){if(e.bigint===undefined){e.bigint=false}if(!("circularValue"in e)){e.circularValue=Error}}const n=getCircularValueOption(e);const r=getBooleanOption(e,"bigint");const i=getDeterministicOption(e);const f=typeof i==="function"?i:undefined;const u=getPositiveIntegerOption(e,"maximumDepth");const o=getPositiveIntegerOption(e,"maximumBreadth");function stringifyFnReplacer(e,s,l,c,a,g){let p=s[e];if(typeof p==="object"&&p!==null&&typeof p.toJSON==="function"){p=p.toJSON(e)}p=c.call(s,e,p);switch(typeof p){case"string":return strEscape(p);case"object":{if(p===null){return"null"}if(l.indexOf(p)!==-1){return n}let e="";let t=",";const r=g;if(Array.isArray(p)){if(p.length===0){return"[]"}if(u<l.length+1){return'"[Array]"'}l.push(p);if(a!==""){g+=a;e+=`\n${g}`;t=`,\n${g}`}const n=Math.min(p.length,o);let i=0;for(;i<n-1;i++){const n=stringifyFnReplacer(String(i),p,l,c,a,g);e+=n!==undefined?n:"null";e+=t}const f=stringifyFnReplacer(String(i),p,l,c,a,g);e+=f!==undefined?f:"null";if(p.length-1>o){const n=p.length-o-1;e+=`${t}"... ${getItemCount(n)} not stringified"`}if(a!==""){e+=`\n${r}`}l.pop();return`[${e}]`}let s=Object.keys(p);const y=s.length;if(y===0){return"{}"}if(u<l.length+1){return'"[Object]"'}let d="";let h="";if(a!==""){g+=a;t=`,\n${g}`;d=" "}const $=Math.min(y,o);if(i&&!isTypedArrayWithEntries(p)){s=sort(s,f)}l.push(p);for(let n=0;n<$;n++){const r=s[n];const i=stringifyFnReplacer(r,p,l,c,a,g);if(i!==undefined){e+=`${h}${strEscape(r)}:${d}${i}`;h=t}}if(y>o){const n=y-o;e+=`${h}"...":${d}"${getItemCount(n)} not stringified"`;h=t}if(a!==""&&h.length>1){e=`\n${g}${e}\n${r}`}l.pop();return`{${e}}`}case"number":return isFinite(p)?String(p):t?t(p):"null";case"boolean":return p===true?"true":"false";case"undefined":return undefined;case"bigint":if(r){return String(p)}default:return t?t(p):undefined}}function stringifyArrayReplacer(e,i,f,s,l,c){if(typeof i==="object"&&i!==null&&typeof i.toJSON==="function"){i=i.toJSON(e)}switch(typeof i){case"string":return strEscape(i);case"object":{if(i===null){return"null"}if(f.indexOf(i)!==-1){return n}const e=c;let t="";let r=",";if(Array.isArray(i)){if(i.length===0){return"[]"}if(u<f.length+1){return'"[Array]"'}f.push(i);if(l!==""){c+=l;t+=`\n${c}`;r=`,\n${c}`}const n=Math.min(i.length,o);let a=0;for(;a<n-1;a++){const e=stringifyArrayReplacer(String(a),i[a],f,s,l,c);t+=e!==undefined?e:"null";t+=r}const g=stringifyArrayReplacer(String(a),i[a],f,s,l,c);t+=g!==undefined?g:"null";if(i.length-1>o){const e=i.length-o-1;t+=`${r}"... ${getItemCount(e)} not stringified"`}if(l!==""){t+=`\n${e}`}f.pop();return`[${t}]`}f.push(i);let a="";if(l!==""){c+=l;r=`,\n${c}`;a=" "}let g="";for(const e of s){const n=stringifyArrayReplacer(e,i[e],f,s,l,c);if(n!==undefined){t+=`${g}${strEscape(e)}:${a}${n}`;g=r}}if(l!==""&&g.length>1){t=`\n${c}${t}\n${e}`}f.pop();return`{${t}}`}case"number":return isFinite(i)?String(i):t?t(i):"null";case"boolean":return i===true?"true":"false";case"undefined":return undefined;case"bigint":if(r){return String(i)}default:return t?t(i):undefined}}function stringifyIndent(e,s,l,c,a){switch(typeof s){case"string":return strEscape(s);case"object":{if(s===null){return"null"}if(typeof s.toJSON==="function"){s=s.toJSON(e);if(typeof s!=="object"){return stringifyIndent(e,s,l,c,a)}if(s===null){return"null"}}if(l.indexOf(s)!==-1){return n}const t=a;if(Array.isArray(s)){if(s.length===0){return"[]"}if(u<l.length+1){return'"[Array]"'}l.push(s);a+=c;let e=`\n${a}`;const n=`,\n${a}`;const r=Math.min(s.length,o);let i=0;for(;i<r-1;i++){const t=stringifyIndent(String(i),s[i],l,c,a);e+=t!==undefined?t:"null";e+=n}const f=stringifyIndent(String(i),s[i],l,c,a);e+=f!==undefined?f:"null";if(s.length-1>o){const t=s.length-o-1;e+=`${n}"... ${getItemCount(t)} not stringified"`}e+=`\n${t}`;l.pop();return`[${e}]`}let r=Object.keys(s);const g=r.length;if(g===0){return"{}"}if(u<l.length+1){return'"[Object]"'}a+=c;const p=`,\n${a}`;let y="";let d="";let h=Math.min(g,o);if(isTypedArrayWithEntries(s)){y+=stringifyTypedArray(s,p,o);r=r.slice(s.length);h-=s.length;d=p}if(i){r=sort(r,f)}l.push(s);for(let e=0;e<h;e++){const t=r[e];const n=stringifyIndent(t,s[t],l,c,a);if(n!==undefined){y+=`${d}${strEscape(t)}: ${n}`;d=p}}if(g>o){const e=g-o;y+=`${d}"...": "${getItemCount(e)} not stringified"`;d=p}if(d!==""){y=`\n${a}${y}\n${t}`}l.pop();return`{${y}}`}case"number":return isFinite(s)?String(s):t?t(s):"null";case"boolean":return s===true?"true":"false";case"undefined":return undefined;case"bigint":if(r){return String(s)}default:return t?t(s):undefined}}function stringifySimple(e,s,l){switch(typeof s){case"string":return strEscape(s);case"object":{if(s===null){return"null"}if(typeof s.toJSON==="function"){s=s.toJSON(e);if(typeof s!=="object"){return stringifySimple(e,s,l)}if(s===null){return"null"}}if(l.indexOf(s)!==-1){return n}let t="";const r=s.length!==undefined;if(r&&Array.isArray(s)){if(s.length===0){return"[]"}if(u<l.length+1){return'"[Array]"'}l.push(s);const e=Math.min(s.length,o);let n=0;for(;n<e-1;n++){const e=stringifySimple(String(n),s[n],l);t+=e!==undefined?e:"null";t+=","}const r=stringifySimple(String(n),s[n],l);t+=r!==undefined?r:"null";if(s.length-1>o){const e=s.length-o-1;t+=`,"... ${getItemCount(e)} not stringified"`}l.pop();return`[${t}]`}let c=Object.keys(s);const a=c.length;if(a===0){return"{}"}if(u<l.length+1){return'"[Object]"'}let g="";let p=Math.min(a,o);if(r&&isTypedArrayWithEntries(s)){t+=stringifyTypedArray(s,",",o);c=c.slice(s.length);p-=s.length;g=","}if(i){c=sort(c,f)}l.push(s);for(let e=0;e<p;e++){const n=c[e];const r=stringifySimple(n,s[n],l);if(r!==undefined){t+=`${g}${strEscape(n)}:${r}`;g=","}}if(a>o){const e=a-o;t+=`${g}"...":"${getItemCount(e)} not stringified"`}l.pop();return`{${t}}`}case"number":return isFinite(s)?String(s):t?t(s):"null";case"boolean":return s===true?"true":"false";case"undefined":return undefined;case"bigint":if(r){return String(s)}default:return t?t(s):undefined}}function stringify(e,t,n){if(arguments.length>1){let r="";if(typeof n==="number"){r=" ".repeat(Math.min(n,10))}else if(typeof n==="string"){r=n.slice(0,10)}if(t!=null){if(typeof t==="function"){return stringifyFnReplacer("",{"":e},[],t,r,"")}if(Array.isArray(t)){return stringifyArrayReplacer("",e,[],getUniqueReplacerSet(t),r,"")}}if(r.length!==0){return stringifyIndent("",e,[],r,"")}}return stringifySimple("",e,[])}return stringify}}};var t={};function __nccwpck_require__(n){var r=t[n];if(r!==undefined){return r.exports}var i=t[n]={exports:{}};var f=true;try{e[n](i,i.exports,__nccwpck_require__);f=false}finally{if(f)delete t[n]}return i.exports}if(typeof __nccwpck_require__!=="undefined")__nccwpck_require__.ab=__dirname+"/";var n=__nccwpck_require__(879);module.exports=n})();