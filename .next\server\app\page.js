(()=>{var a={};a.id=974,a.ids=[974],a.modules={195:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{formatUrl:function(){return f},formatWithValidation:function(){return h},urlObjectKeys:function(){return g}});let d=c(740)._(c(6715)),e=/https?|ftp|gopher|file/;function f(a){let{auth:b,hostname:c}=a,f=a.protocol||"",g=a.pathname||"",h=a.hash||"",i=a.query||"",j=!1;b=b?encodeURIComponent(b).replace(/%3A/i,":")+"@":"",a.host?j=b+a.host:c&&(j=b+(~c.indexOf(":")?"["+c+"]":c),a.port&&(j+=":"+a.port)),i&&"object"==typeof i&&(i=String(d.urlQueryToSearchParams(i)));let k=a.search||i&&"?"+i||"";return f&&!f.endsWith(":")&&(f+=":"),a.slashes||(!f||e.test(f))&&!1!==j?(j="//"+(j||""),g&&"/"!==g[0]&&(g="/"+g)):j||(j=""),h&&"#"!==h[0]&&(h="#"+h),k&&"?"!==k[0]&&(k="?"+k),""+f+j+(g=g.replace(/[?#]/g,encodeURIComponent))+(k=k.replace("#","%23"))+h}let g=["auth","hash","host","hostname","href","path","pathname","port","protocol","query","search","slashes"];function h(a){return f(a)}},261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},440:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>e});var d=c(1658);let e=async a=>[{type:"image/x-icon",sizes:"16x16",url:(0,d.fillMetadataSegment)(".",await a.params,"favicon.ico")+""}]},552:(a,b,c)=>{"use strict";c.d(b,{s:()=>e});var d=c(4479);function e(a){return(0,d.G)(a)&&"offsetHeight"in a}},593:(a,b)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{NavigationResultTag:function(){return m},PrefetchPriority:function(){return n},cancelPrefetchTask:function(){return i},createCacheKey:function(){return l},getCurrentCacheVersion:function(){return g},isPrefetchTaskDirty:function(){return k},navigate:function(){return e},prefetch:function(){return d},reschedulePrefetchTask:function(){return j},revalidateEntireCache:function(){return f},schedulePrefetchTask:function(){return h}});let c=()=>{throw Object.defineProperty(Error("Segment Cache experiment is not enabled. This is a bug in Next.js."),"__NEXT_ERROR_CODE",{value:"E654",enumerable:!1,configurable:!0})},d=c,e=c,f=c,g=c,h=c,i=c,j=c,k=c,l=c;var m=function(a){return a[a.MPA=0]="MPA",a[a.Success=1]="Success",a[a.NoOp=2]="NoOp",a[a.Async=3]="Async",a}({}),n=function(a){return a[a.Intent=2]="Intent",a[a.Default=1]="Default",a[a.Background=0]="Background",a}({});("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},625:(a,b,c)=>{"use strict";c.d(b,{PackagesSection:()=>n});var d=c(687),e=c(4124),f=c(4493),g=c(6834),h=c(9523),i=c(4398),j=c(2688);let k=(0,j.A)("gift",[["rect",{x:"3",y:"8",width:"18",height:"4",rx:"1",key:"bkv52"}],["path",{d:"M12 8v13",key:"1c76mn"}],["path",{d:"M19 12v7a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2v-7",key:"6wjy6b"}],["path",{d:"M7.5 8a2.5 2.5 0 0 1 0-5A4.8 8 0 0 1 12 8a4.8 8 0 0 1 4.5-5 2.5 2.5 0 0 1 0 5",key:"1ihvrl"}]]),l=(0,j.A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]]),m=[{id:"p1",name:"Morning Bliss Combo",description:"Perfect start to your day with our signature coffee and fresh pastry",originalPrice:350,discountedPrice:280,savings:70,items:["Himalayan Coffee (Regular)","Choice of Fresh Pastry","Free WiFi Access"],validUntil:"2024-12-31",isPopular:!0,image:"/images/packages/morning-bliss.jpg"},{id:"p2",name:"Study Buddy Package",description:"Ideal for students - unlimited coffee refills and snacks for productive study sessions",originalPrice:600,discountedPrice:450,savings:150,items:["Unlimited Coffee Refills (4 hours)","Choice of Snack","Reserved Study Table","Free WiFi & Power Outlet"],validUntil:"2024-12-31",isPopular:!0,image:"/images/packages/study-buddy.jpg"},{id:"p3",name:"Momo Mania",description:"For momo lovers - try both chicken and vegetable momos with drinks",originalPrice:640,discountedPrice:520,savings:120,items:["Chicken Momo (Half Plate)","Vegetable Momo (Half Plate)","2 Masala Chai","Complimentary Pickle"],validUntil:"2024-12-31",image:"/images/packages/momo-mania.jpg"},{id:"p4",name:"Date Night Special",description:"Romantic evening package for couples with premium coffee and desserts",originalPrice:800,discountedPrice:650,savings:150,items:["2 Premium Coffee (Cappuccino/Latte)","Chocolate Croissant to Share","Reserved Corner Table","Complimentary Dessert"],validUntil:"2024-12-31",image:"/images/packages/date-night.jpg"},{id:"p5",name:"Family Feast",description:"Perfect for family gatherings with traditional Nepali meal and drinks for 4",originalPrice:1400,discountedPrice:1100,savings:300,items:["2 Dal Bhat Sets","1 Chicken Momo","1 Vegetable Momo","4 Drinks (Tea/Coffee/Lassi)","Family Table Reservation"],validUntil:"2024-12-31",isPopular:!0,image:"/images/packages/family-feast.jpg"},{id:"p6",name:"Business Meeting Package",description:"Professional setting with premium coffee and light snacks for your meetings",originalPrice:1200,discountedPrice:950,savings:250,items:["Reserved Meeting Table (2 hours)","Premium Coffee for 4 people","Assorted Pastries","Notepad & Pen","Free WiFi & Presentation Setup"],validUntil:"2024-12-31",image:"/images/packages/business-meeting.jpg"},{id:"p7",name:"Weekend Brunch",description:"Leisurely weekend brunch with hearty breakfast and unlimited coffee",originalPrice:750,discountedPrice:600,savings:150,items:["English Breakfast OR Pancakes","Unlimited Coffee (2 hours)","Fresh Fruit Juice","Weekend Newspaper","Relaxed Atmosphere"],validUntil:"2024-12-31",image:"/images/packages/weekend-brunch.jpg"},{id:"p8",name:"Cultural Experience",description:"Taste authentic Nepal with traditional foods and drinks",originalPrice:550,discountedPrice:450,savings:100,items:["Butter Tea (Po Cha)","Sel Roti (2 pieces)","Traditional Pickle","Cultural Story Session","Photo with Traditional Dress"],validUntil:"2024-12-31",image:"/images/packages/cultural-experience.jpg"}].filter(a=>a.isPopular);function n(){return(0,d.jsx)("section",{className:"py-20 bg-white",children:(0,d.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,d.jsxs)(e.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.6},viewport:{once:!0},className:"text-center mb-16",children:[(0,d.jsx)("h2",{className:"font-serif text-4xl md:text-5xl font-bold text-cafe-dark-brown mb-6",children:"Special Packages"}),(0,d.jsx)("p",{className:"text-xl text-cafe-brown max-w-3xl mx-auto leading-relaxed",children:"Enjoy great savings with our carefully curated packages, perfect for every occasion and craving."})]}),(0,d.jsx)("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-8",children:m.map((a,b)=>{let c,j=Math.round(((c=a.originalPrice)-a.discountedPrice)/c*100);return(0,d.jsx)(e.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.1*b},viewport:{once:!0},children:(0,d.jsxs)(f.Zp,{className:"h-full border-cafe-beige hover:shadow-cafe-lg transition-all duration-300 relative overflow-hidden",children:[a.isPopular&&(0,d.jsx)("div",{className:"absolute top-4 right-4 z-10",children:(0,d.jsxs)(g.E,{className:"bg-cafe-gold text-cafe-dark-brown font-semibold",children:[(0,d.jsx)(i.A,{className:"h-3 w-3 mr-1"}),"Popular"]})}),(0,d.jsx)("div",{className:"absolute top-4 left-4 z-10",children:(0,d.jsxs)(g.E,{className:"bg-cafe-green text-white font-semibold",children:[j,"% OFF"]})}),(0,d.jsx)("div",{className:"aspect-[4/3] overflow-hidden",children:(0,d.jsx)("img",{src:a.image,alt:a.name,className:"w-full h-full object-cover"})}),(0,d.jsxs)(f.aR,{className:"pb-4",children:[(0,d.jsxs)(f.ZB,{className:"font-serif text-xl text-cafe-dark-brown flex items-center gap-2",children:[(0,d.jsx)(k,{className:"h-5 w-5 text-cafe-brown"}),a.name]}),(0,d.jsx)("p",{className:"text-cafe-brown text-sm leading-relaxed",children:a.description})]}),(0,d.jsxs)(f.Wu,{className:"space-y-4",children:[(0,d.jsxs)("div",{className:"flex items-center gap-3",children:[(0,d.jsxs)("span",{className:"text-2xl font-bold text-cafe-dark-brown",children:["Rs. ",a.discountedPrice]}),(0,d.jsxs)("span",{className:"text-lg text-cafe-brown line-through",children:["Rs. ",a.originalPrice]}),(0,d.jsxs)(g.E,{variant:"secondary",className:"bg-cafe-beige text-cafe-brown",children:["Save Rs. ",a.savings]})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)("h4",{className:"font-semibold text-cafe-dark-brown text-sm",children:"What's Included:"}),(0,d.jsx)("ul",{className:"space-y-1",children:a.items.map((a,b)=>(0,d.jsxs)("li",{className:"flex items-start gap-2 text-sm text-cafe-brown",children:[(0,d.jsx)(l,{className:"h-4 w-4 text-cafe-green mt-0.5 flex-shrink-0"}),(0,d.jsx)("span",{children:a})]},b))})]}),(0,d.jsxs)("div",{className:"text-xs text-cafe-brown",children:["Valid until: ",new Date(a.validUntil).toLocaleDateString()]}),(0,d.jsx)(h.$,{className:"w-full bg-cafe-brown hover:bg-cafe-dark-brown text-cafe-cream font-semibold",size:"sm",children:"Order Now"})]})]})},a.id)})}),(0,d.jsx)(e.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.3},viewport:{once:!0},className:"text-center mt-12",children:(0,d.jsx)(h.$,{variant:"outline",size:"lg",className:"border-cafe-brown text-cafe-brown hover:bg-cafe-brown hover:text-cafe-cream px-8 py-3 text-lg font-semibold",children:"View All Packages"})})]})})}},642:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{computeChangedPath:function(){return j},extractPathFromFlightRouterState:function(){return i},getSelectedParams:function(){return function a(b,c){for(let d of(void 0===c&&(c={}),Object.values(b[1]))){let b=d[0],f=Array.isArray(b),g=f?b[1]:b;!g||g.startsWith(e.PAGE_SEGMENT_KEY)||(f&&("c"===b[2]||"oc"===b[2])?c[b[0]]=b[1].split("/"):f&&(c[b[0]]=b[1]),c=a(d,c))}return c}}});let d=c(2859),e=c(3913),f=c(4077),g=a=>"string"==typeof a?"children"===a?"":a:a[1];function h(a){return a.reduce((a,b)=>{let c;return""===(b="/"===(c=b)[0]?c.slice(1):c)||(0,e.isGroupSegment)(b)?a:a+"/"+b},"")||"/"}function i(a){var b;let c=Array.isArray(a[0])?a[0][1]:a[0];if(c===e.DEFAULT_SEGMENT_KEY||d.INTERCEPTION_ROUTE_MARKERS.some(a=>c.startsWith(a)))return;if(c.startsWith(e.PAGE_SEGMENT_KEY))return"";let f=[g(c)],j=null!=(b=a[1])?b:{},k=j.children?i(j.children):void 0;if(void 0!==k)f.push(k);else for(let[a,b]of Object.entries(j)){if("children"===a)continue;let c=i(b);void 0!==c&&f.push(c)}return h(f)}function j(a,b){let c=function a(b,c){let[e,h]=b,[j,k]=c,l=g(e),m=g(j);if(d.INTERCEPTION_ROUTE_MARKERS.some(a=>l.startsWith(a)||m.startsWith(a)))return"";if(!(0,f.matchSegment)(e,j)){var n;return null!=(n=i(c))?n:""}for(let b in h)if(k[b]){let c=a(h[b],k[b]);if(null!==c)return g(j)+"/"+c}return null}(a,b);return null==c||"/"===c?c:h(c.split("/"))}("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},1079:(a,b,c)=>{"use strict";c.d(b,{AboutSection:()=>n});var d=c(687),e=c(4124),f=c(4493),g=c(2688);let h=(0,g.A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]]),i=(0,g.A)("award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]]);var j=c(4082);let k=(0,g.A)("heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]]);var l=c(8465);let m={"Community First":h,"Quality & Authenticity":i,Sustainability:j.A,"Warm Hospitality":k};function n(){return(0,d.jsx)("section",{className:"py-20 bg-white",children:(0,d.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,d.jsxs)(e.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.6},viewport:{once:!0},className:"text-center mb-16",children:[(0,d.jsx)("h2",{className:"font-serif text-4xl md:text-5xl font-bold text-cafe-dark-brown mb-6",children:"About Our Story"}),(0,d.jsx)("p",{className:"text-xl text-cafe-brown max-w-3xl mx-auto leading-relaxed",children:"Discover the heart and soul behind Himalayan Brew Caf\xe9, where every cup tells a story of passion, community, and authentic Nepali hospitality."})]}),(0,d.jsxs)("div",{className:"grid lg:grid-cols-2 gap-12 items-center mb-20",children:[(0,d.jsxs)(e.P.div,{initial:{opacity:0,x:-30},whileInView:{opacity:1,x:0},transition:{duration:.6},viewport:{once:!0},className:"space-y-6",children:[(0,d.jsx)("h3",{className:"font-serif text-3xl font-semibold text-cafe-dark-brown",children:"Our Journey"}),(0,d.jsxs)("div",{className:"prose prose-lg text-cafe-brown",children:[(0,d.jsx)("p",{className:"leading-relaxed",children:"Nestled in the vibrant city of Biratnagar, Himalayan Brew Caf\xe9 was born from a passion for bringing people together over exceptional coffee and warm conversations."}),(0,d.jsx)("p",{className:"leading-relaxed",children:"Our journey began in 2020 when our founder, inspired by the rich coffee culture of Nepal's hills and the bustling energy of Biratnagar, decided to create a space that celebrates both tradition and modernity."}),(0,d.jsx)("p",{className:"leading-relaxed",children:"We source our coffee beans directly from the hills of Ilam and Gulmi, supporting local farmers and ensuring every cup tells a story of Nepal's coffee heritage."})]})]}),(0,d.jsxs)(e.P.div,{initial:{opacity:0,x:30},whileInView:{opacity:1,x:0},transition:{duration:.6},viewport:{once:!0},className:"relative",children:[(0,d.jsx)("div",{className:"aspect-square rounded-2xl overflow-hidden shadow-cafe-lg",children:(0,d.jsx)("img",{src:"/images/about-story.jpg",alt:"Himalayan Brew Caf\xe9 story",className:"w-full h-full object-cover"})}),(0,d.jsx)("div",{className:"absolute -bottom-6 -right-6 w-24 h-24 bg-cafe-gold rounded-full flex items-center justify-center shadow-cafe-lg",children:(0,d.jsx)(k,{className:"h-12 w-12 text-cafe-dark-brown"})})]})]}),(0,d.jsxs)(e.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.6},viewport:{once:!0},className:"text-center mb-12",children:[(0,d.jsx)("h3",{className:"font-serif text-3xl font-semibold text-cafe-dark-brown mb-4",children:"Our Values"}),(0,d.jsx)("p",{className:"text-lg text-cafe-brown max-w-2xl mx-auto",children:"These core values guide everything we do, from sourcing our beans to serving our community."})]}),(0,d.jsx)("div",{className:"grid md:grid-cols-2 lg:grid-cols-4 gap-6",children:l.I.values.map((a,b)=>{let c=m[a.title];return(0,d.jsx)(e.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.1*b},viewport:{once:!0},children:(0,d.jsx)(f.Zp,{className:"h-full border-cafe-beige hover:shadow-cafe-lg transition-shadow duration-300",children:(0,d.jsxs)(f.Wu,{className:"p-6 text-center space-y-4",children:[(0,d.jsx)("div",{className:"w-16 h-16 bg-cafe-beige rounded-full flex items-center justify-center mx-auto",children:(0,d.jsx)(c,{className:"h-8 w-8 text-cafe-brown"})}),(0,d.jsx)("h4",{className:"font-serif text-xl font-semibold text-cafe-dark-brown",children:a.title}),(0,d.jsx)("p",{className:"text-cafe-brown leading-relaxed",children:a.description})]})})},a.title)})})]})})}},1110:(a,b,c)=>{"use strict";c.d(b,{HeroSection:()=>k});var d=c(687),e=c(9523),f=c(3166),g=c(7992),h=c(6349),i=c(4124),j=c(8465);function k(){return(0,d.jsxs)("section",{className:"relative min-h-screen flex items-center justify-center bg-cafe-pattern",children:[(0,d.jsx)("div",{className:"absolute inset-0 bg-cover bg-center bg-no-repeat",style:{backgroundImage:"url('/images/hero-bg.jpg')",backgroundBlendMode:"overlay"},children:(0,d.jsx)("div",{className:"absolute inset-0 bg-cafe-cream/80"})}),(0,d.jsx)("div",{className:"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center",children:(0,d.jsxs)(i.P.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8},className:"space-y-8",children:[(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsx)(i.P.div,{initial:{scale:.8,opacity:0},animate:{scale:1,opacity:1},transition:{duration:.6,delay:.2},className:"flex justify-center",children:(0,d.jsx)(f.A,{className:"h-16 w-16 text-cafe-brown"})}),(0,d.jsx)("h1",{className:"font-serif text-5xl md:text-7xl font-bold text-cafe-dark-brown",children:j.I.name}),(0,d.jsx)("p",{className:"text-xl md:text-2xl text-cafe-brown font-medium",children:j.I.tagline})]}),(0,d.jsx)(i.P.p,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.4},className:"max-w-3xl mx-auto text-lg text-cafe-brown leading-relaxed",children:j.I.description}),(0,d.jsxs)(i.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.6},className:"flex flex-col sm:flex-row gap-4 justify-center items-center",children:[(0,d.jsx)(e.$,{size:"lg",className:"bg-cafe-brown hover:bg-cafe-dark-brown text-cafe-cream px-8 py-3 text-lg font-semibold shadow-cafe-lg",children:"View Our Menu"}),(0,d.jsx)(e.$,{variant:"outline",size:"lg",className:"border-cafe-brown text-cafe-brown hover:bg-cafe-brown hover:text-cafe-cream px-8 py-3 text-lg font-semibold",children:"Visit Gallery"})]}),(0,d.jsxs)(i.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.8},className:"flex flex-col md:flex-row gap-8 justify-center items-center text-cafe-brown",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)(g.A,{className:"h-5 w-5"}),(0,d.jsx)("span",{className:"font-medium",children:"Main Road, Biratnagar"})]}),(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)(h.A,{className:"h-5 w-5"}),(0,d.jsx)("span",{className:"font-medium",children:"6:00 AM - 10:00 PM"})]}),(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)(f.A,{className:"h-5 w-5"}),(0,d.jsx)("span",{className:"font-medium",children:"Fresh Coffee Daily"})]})]})]})}),(0,d.jsx)(i.P.div,{initial:{opacity:0},animate:{opacity:1},transition:{duration:.6,delay:1},className:"absolute bottom-8 left-1/2 transform -translate-x-1/2",children:(0,d.jsx)(i.P.div,{animate:{y:[0,10,0]},transition:{duration:2,repeat:1/0},className:"w-6 h-10 border-2 border-cafe-brown rounded-full flex justify-center",children:(0,d.jsx)(i.P.div,{animate:{y:[0,12,0]},transition:{duration:2,repeat:1/0},className:"w-1 h-3 bg-cafe-brown rounded-full mt-2"})})})]})}},1135:()=>{},1160:(a,b,c)=>{Promise.resolve().then(c.bind(c,9253)),Promise.resolve().then(c.bind(c,3802)),Promise.resolve().then(c.bind(c,4947)),Promise.resolve().then(c.bind(c,6229)),Promise.resolve().then(c.bind(c,8300)),Promise.resolve().then(c.bind(c,6849)),Promise.resolve().then(c.bind(c,4544)),Promise.resolve().then(c.bind(c,6443)),Promise.resolve().then(c.bind(c,7028))},1204:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>n});var d=c(7413),e=c(4544),f=c(8300),g=c(9253),h=c(6849),i=c(6443),j=c(6229),k=c(7028),l=c(3802),m=c(4947);function n(){return(0,d.jsxs)("div",{className:"min-h-screen",children:[(0,d.jsx)(e.Navigation,{}),(0,d.jsxs)("main",{children:[(0,d.jsx)(f.HeroSection,{}),(0,d.jsx)(g.AboutSection,{}),(0,d.jsx)(h.MenuHighlights,{}),(0,d.jsx)(i.PackagesSection,{}),(0,d.jsx)(j.GalleryPreview,{}),(0,d.jsx)(k.TestimonialsSection,{}),(0,d.jsx)(l.ContactSection,{})]}),(0,d.jsx)(m.Footer,{})]})}},1279:(a,b,c)=>{"use strict";c.d(b,{t:()=>d});let d=(0,c(3210).createContext)(null)},1500:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"fillLazyItemsTillLeafWithHead",{enumerable:!0,get:function(){return function a(b,c,f,g,h,i,j){if(0===Object.keys(g[1]).length){c.head=i;return}for(let k in g[1]){let l,m=g[1][k],n=m[0],o=(0,d.createRouterCacheKey)(n),p=null!==h&&void 0!==h[2][k]?h[2][k]:null;if(f){let d=f.parallelRoutes.get(k);if(d){let f,g=(null==j?void 0:j.kind)==="auto"&&j.status===e.PrefetchCacheEntryStatus.reusable,h=new Map(d),l=h.get(o);f=null!==p?{lazyData:null,rsc:p[1],prefetchRsc:null,head:null,prefetchHead:null,loading:p[3],parallelRoutes:new Map(null==l?void 0:l.parallelRoutes),navigatedAt:b}:g&&l?{lazyData:l.lazyData,rsc:l.rsc,prefetchRsc:l.prefetchRsc,head:l.head,prefetchHead:l.prefetchHead,parallelRoutes:new Map(l.parallelRoutes),loading:l.loading}:{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map(null==l?void 0:l.parallelRoutes),loading:null,navigatedAt:b},h.set(o,f),a(b,f,l,m,p||null,i,j),c.parallelRoutes.set(k,h);continue}}if(null!==p){let a=p[1],c=p[3];l={lazyData:null,rsc:a,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:c,navigatedAt:b}}else l={lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:b};let q=c.parallelRoutes.get(k);q?q.set(o,l):c.parallelRoutes.set(k,new Map([[o,l]])),a(b,l,void 0,m,p,i,j)}}}});let d=c(3123),e=c(9154);("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},1550:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(2688).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},1743:(a,b,c)=>{"use strict";c.d(b,{cn:()=>ac});var d=c(9384);let e=(a,b)=>{if(0===a.length)return b.classGroupId;let c=a[0],d=b.nextPart.get(c),f=d?e(a.slice(1),d):void 0;if(f)return f;if(0===b.validators.length)return;let g=a.join("-");return b.validators.find(({validator:a})=>a(g))?.classGroupId},f=/^\[(.+)\]$/,g=(a,b,c,d)=>{a.forEach(a=>{if("string"==typeof a){(""===a?b:h(b,a)).classGroupId=c;return}if("function"==typeof a)return i(a)?void g(a(d),b,c,d):void b.validators.push({validator:a,classGroupId:c});Object.entries(a).forEach(([a,e])=>{g(e,h(b,a),c,d)})})},h=(a,b)=>{let c=a;return b.split("-").forEach(a=>{c.nextPart.has(a)||c.nextPart.set(a,{nextPart:new Map,validators:[]}),c=c.nextPart.get(a)}),c},i=a=>a.isThemeGetter,j=/\s+/;function k(){let a,b,c=0,d="";for(;c<arguments.length;)(a=arguments[c++])&&(b=l(a))&&(d&&(d+=" "),d+=b);return d}let l=a=>{let b;if("string"==typeof a)return a;let c="";for(let d=0;d<a.length;d++)a[d]&&(b=l(a[d]))&&(c&&(c+=" "),c+=b);return c},m=a=>{let b=b=>b[a]||[];return b.isThemeGetter=!0,b},n=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,o=/^\((?:(\w[\w-]*):)?(.+)\)$/i,p=/^\d+\/\d+$/,q=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,r=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,s=/^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\(.+\)$/,t=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,u=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,v=a=>p.test(a),w=a=>!!a&&!Number.isNaN(Number(a)),x=a=>!!a&&Number.isInteger(Number(a)),y=a=>a.endsWith("%")&&w(a.slice(0,-1)),z=a=>q.test(a),A=()=>!0,B=a=>r.test(a)&&!s.test(a),C=()=>!1,D=a=>t.test(a),E=a=>u.test(a),F=a=>!H(a)&&!N(a),G=a=>U(a,Y,C),H=a=>n.test(a),I=a=>U(a,Z,B),J=a=>U(a,$,w),K=a=>U(a,W,C),L=a=>U(a,X,E),M=a=>U(a,aa,D),N=a=>o.test(a),O=a=>V(a,Z),P=a=>V(a,_),Q=a=>V(a,W),R=a=>V(a,Y),S=a=>V(a,X),T=a=>V(a,aa,!0),U=(a,b,c)=>{let d=n.exec(a);return!!d&&(d[1]?b(d[1]):c(d[2]))},V=(a,b,c=!1)=>{let d=o.exec(a);return!!d&&(d[1]?b(d[1]):c)},W=a=>"position"===a||"percentage"===a,X=a=>"image"===a||"url"===a,Y=a=>"length"===a||"size"===a||"bg-size"===a,Z=a=>"length"===a,$=a=>"number"===a,_=a=>"family-name"===a,aa=a=>"shadow"===a;Symbol.toStringTag;let ab=function(a,...b){let c,d,h,i=function(j){let k;return d=(c={cache:(a=>{if(a<1)return{get:()=>void 0,set:()=>{}};let b=0,c=new Map,d=new Map,e=(e,f)=>{c.set(e,f),++b>a&&(b=0,d=c,c=new Map)};return{get(a){let b=c.get(a);return void 0!==b?b:void 0!==(b=d.get(a))?(e(a,b),b):void 0},set(a,b){c.has(a)?c.set(a,b):e(a,b)}}})((k=b.reduce((a,b)=>b(a),a())).cacheSize),parseClassName:(a=>{let{prefix:b,experimentalParseClassName:c}=a,d=a=>{let b,c,d=[],e=0,f=0,g=0;for(let c=0;c<a.length;c++){let h=a[c];if(0===e&&0===f){if(":"===h){d.push(a.slice(g,c)),g=c+1;continue}if("/"===h){b=c;continue}}"["===h?e++:"]"===h?e--:"("===h?f++:")"===h&&f--}let h=0===d.length?a:a.substring(g),i=(c=h).endsWith("!")?c.substring(0,c.length-1):c.startsWith("!")?c.substring(1):c;return{modifiers:d,hasImportantModifier:i!==h,baseClassName:i,maybePostfixModifierPosition:b&&b>g?b-g:void 0}};if(b){let a=b+":",c=d;d=b=>b.startsWith(a)?c(b.substring(a.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:b,maybePostfixModifierPosition:void 0}}if(c){let a=d;d=b=>c({className:b,parseClassName:a})}return d})(k),sortModifiers:(a=>{let b=Object.fromEntries(a.orderSensitiveModifiers.map(a=>[a,!0]));return a=>{if(a.length<=1)return a;let c=[],d=[];return a.forEach(a=>{"["===a[0]||b[a]?(c.push(...d.sort(),a),d=[]):d.push(a)}),c.push(...d.sort()),c}})(k),...(a=>{let b=(a=>{let{theme:b,classGroups:c}=a,d={nextPart:new Map,validators:[]};for(let a in c)g(c[a],d,a,b);return d})(a),{conflictingClassGroups:c,conflictingClassGroupModifiers:d}=a;return{getClassGroupId:a=>{let c=a.split("-");return""===c[0]&&1!==c.length&&c.shift(),e(c,b)||(a=>{if(f.test(a)){let b=f.exec(a)[1],c=b?.substring(0,b.indexOf(":"));if(c)return"arbitrary.."+c}})(a)},getConflictingClassGroupIds:(a,b)=>{let e=c[a]||[];return b&&d[a]?[...e,...d[a]]:e}}})(k)}).cache.get,h=c.cache.set,i=l,l(j)};function l(a){let b=d(a);if(b)return b;let e=((a,b)=>{let{parseClassName:c,getClassGroupId:d,getConflictingClassGroupIds:e,sortModifiers:f}=b,g=[],h=a.trim().split(j),i="";for(let a=h.length-1;a>=0;a-=1){let b=h[a],{isExternal:j,modifiers:k,hasImportantModifier:l,baseClassName:m,maybePostfixModifierPosition:n}=c(b);if(j){i=b+(i.length>0?" "+i:i);continue}let o=!!n,p=d(o?m.substring(0,n):m);if(!p){if(!o||!(p=d(m))){i=b+(i.length>0?" "+i:i);continue}o=!1}let q=f(k).join(":"),r=l?q+"!":q,s=r+p;if(g.includes(s))continue;g.push(s);let t=e(p,o);for(let a=0;a<t.length;++a){let b=t[a];g.push(r+b)}i=b+(i.length>0?" "+i:i)}return i})(a,c);return h(a,e),e}return function(){return i(k.apply(null,arguments))}}(()=>{let a=m("color"),b=m("font"),c=m("text"),d=m("font-weight"),e=m("tracking"),f=m("leading"),g=m("breakpoint"),h=m("container"),i=m("spacing"),j=m("radius"),k=m("shadow"),l=m("inset-shadow"),n=m("text-shadow"),o=m("drop-shadow"),p=m("blur"),q=m("perspective"),r=m("aspect"),s=m("ease"),t=m("animate"),u=()=>["auto","avoid","all","avoid-page","page","left","right","column"],B=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],C=()=>[...B(),N,H],D=()=>["auto","hidden","clip","visible","scroll"],E=()=>["auto","contain","none"],U=()=>[N,H,i],V=()=>[v,"full","auto",...U()],W=()=>[x,"none","subgrid",N,H],X=()=>["auto",{span:["full",x,N,H]},x,N,H],Y=()=>[x,"auto",N,H],Z=()=>["auto","min","max","fr",N,H],$=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],_=()=>["start","end","center","stretch","center-safe","end-safe"],aa=()=>["auto",...U()],ab=()=>[v,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...U()],ac=()=>[a,N,H],ad=()=>[...B(),Q,K,{position:[N,H]}],ae=()=>["no-repeat",{repeat:["","x","y","space","round"]}],af=()=>["auto","cover","contain",R,G,{size:[N,H]}],ag=()=>[y,O,I],ah=()=>["","none","full",j,N,H],ai=()=>["",w,O,I],aj=()=>["solid","dashed","dotted","double"],ak=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],al=()=>[w,y,Q,K],am=()=>["","none",p,N,H],an=()=>["none",w,N,H],ao=()=>["none",w,N,H],ap=()=>[w,N,H],aq=()=>[v,"full",...U()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[z],breakpoint:[z],color:[A],container:[z],"drop-shadow":[z],ease:["in","out","in-out"],font:[F],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[z],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[z],shadow:[z],spacing:["px",w],text:[z],"text-shadow":[z],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",v,H,N,r]}],container:["container"],columns:[{columns:[w,H,N,h]}],"break-after":[{"break-after":u()}],"break-before":[{"break-before":u()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:C()}],overflow:[{overflow:D()}],"overflow-x":[{"overflow-x":D()}],"overflow-y":[{"overflow-y":D()}],overscroll:[{overscroll:E()}],"overscroll-x":[{"overscroll-x":E()}],"overscroll-y":[{"overscroll-y":E()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:V()}],"inset-x":[{"inset-x":V()}],"inset-y":[{"inset-y":V()}],start:[{start:V()}],end:[{end:V()}],top:[{top:V()}],right:[{right:V()}],bottom:[{bottom:V()}],left:[{left:V()}],visibility:["visible","invisible","collapse"],z:[{z:[x,"auto",N,H]}],basis:[{basis:[v,"full","auto",h,...U()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[w,v,"auto","initial","none",H]}],grow:[{grow:["",w,N,H]}],shrink:[{shrink:["",w,N,H]}],order:[{order:[x,"first","last","none",N,H]}],"grid-cols":[{"grid-cols":W()}],"col-start-end":[{col:X()}],"col-start":[{"col-start":Y()}],"col-end":[{"col-end":Y()}],"grid-rows":[{"grid-rows":W()}],"row-start-end":[{row:X()}],"row-start":[{"row-start":Y()}],"row-end":[{"row-end":Y()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":Z()}],"auto-rows":[{"auto-rows":Z()}],gap:[{gap:U()}],"gap-x":[{"gap-x":U()}],"gap-y":[{"gap-y":U()}],"justify-content":[{justify:[...$(),"normal"]}],"justify-items":[{"justify-items":[..._(),"normal"]}],"justify-self":[{"justify-self":["auto",..._()]}],"align-content":[{content:["normal",...$()]}],"align-items":[{items:[..._(),{baseline:["","last"]}]}],"align-self":[{self:["auto",..._(),{baseline:["","last"]}]}],"place-content":[{"place-content":$()}],"place-items":[{"place-items":[..._(),"baseline"]}],"place-self":[{"place-self":["auto",..._()]}],p:[{p:U()}],px:[{px:U()}],py:[{py:U()}],ps:[{ps:U()}],pe:[{pe:U()}],pt:[{pt:U()}],pr:[{pr:U()}],pb:[{pb:U()}],pl:[{pl:U()}],m:[{m:aa()}],mx:[{mx:aa()}],my:[{my:aa()}],ms:[{ms:aa()}],me:[{me:aa()}],mt:[{mt:aa()}],mr:[{mr:aa()}],mb:[{mb:aa()}],ml:[{ml:aa()}],"space-x":[{"space-x":U()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":U()}],"space-y-reverse":["space-y-reverse"],size:[{size:ab()}],w:[{w:[h,"screen",...ab()]}],"min-w":[{"min-w":[h,"screen","none",...ab()]}],"max-w":[{"max-w":[h,"screen","none","prose",{screen:[g]},...ab()]}],h:[{h:["screen","lh",...ab()]}],"min-h":[{"min-h":["screen","lh","none",...ab()]}],"max-h":[{"max-h":["screen","lh",...ab()]}],"font-size":[{text:["base",c,O,I]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[d,N,J]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",y,H]}],"font-family":[{font:[P,H,b]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[e,N,H]}],"line-clamp":[{"line-clamp":[w,"none",N,J]}],leading:[{leading:[f,...U()]}],"list-image":[{"list-image":["none",N,H]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",N,H]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:ac()}],"text-color":[{text:ac()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...aj(),"wavy"]}],"text-decoration-thickness":[{decoration:[w,"from-font","auto",N,I]}],"text-decoration-color":[{decoration:ac()}],"underline-offset":[{"underline-offset":[w,"auto",N,H]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:U()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",N,H]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",N,H]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:ad()}],"bg-repeat":[{bg:ae()}],"bg-size":[{bg:af()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},x,N,H],radial:["",N,H],conic:[x,N,H]},S,L]}],"bg-color":[{bg:ac()}],"gradient-from-pos":[{from:ag()}],"gradient-via-pos":[{via:ag()}],"gradient-to-pos":[{to:ag()}],"gradient-from":[{from:ac()}],"gradient-via":[{via:ac()}],"gradient-to":[{to:ac()}],rounded:[{rounded:ah()}],"rounded-s":[{"rounded-s":ah()}],"rounded-e":[{"rounded-e":ah()}],"rounded-t":[{"rounded-t":ah()}],"rounded-r":[{"rounded-r":ah()}],"rounded-b":[{"rounded-b":ah()}],"rounded-l":[{"rounded-l":ah()}],"rounded-ss":[{"rounded-ss":ah()}],"rounded-se":[{"rounded-se":ah()}],"rounded-ee":[{"rounded-ee":ah()}],"rounded-es":[{"rounded-es":ah()}],"rounded-tl":[{"rounded-tl":ah()}],"rounded-tr":[{"rounded-tr":ah()}],"rounded-br":[{"rounded-br":ah()}],"rounded-bl":[{"rounded-bl":ah()}],"border-w":[{border:ai()}],"border-w-x":[{"border-x":ai()}],"border-w-y":[{"border-y":ai()}],"border-w-s":[{"border-s":ai()}],"border-w-e":[{"border-e":ai()}],"border-w-t":[{"border-t":ai()}],"border-w-r":[{"border-r":ai()}],"border-w-b":[{"border-b":ai()}],"border-w-l":[{"border-l":ai()}],"divide-x":[{"divide-x":ai()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":ai()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...aj(),"hidden","none"]}],"divide-style":[{divide:[...aj(),"hidden","none"]}],"border-color":[{border:ac()}],"border-color-x":[{"border-x":ac()}],"border-color-y":[{"border-y":ac()}],"border-color-s":[{"border-s":ac()}],"border-color-e":[{"border-e":ac()}],"border-color-t":[{"border-t":ac()}],"border-color-r":[{"border-r":ac()}],"border-color-b":[{"border-b":ac()}],"border-color-l":[{"border-l":ac()}],"divide-color":[{divide:ac()}],"outline-style":[{outline:[...aj(),"none","hidden"]}],"outline-offset":[{"outline-offset":[w,N,H]}],"outline-w":[{outline:["",w,O,I]}],"outline-color":[{outline:ac()}],shadow:[{shadow:["","none",k,T,M]}],"shadow-color":[{shadow:ac()}],"inset-shadow":[{"inset-shadow":["none",l,T,M]}],"inset-shadow-color":[{"inset-shadow":ac()}],"ring-w":[{ring:ai()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:ac()}],"ring-offset-w":[{"ring-offset":[w,I]}],"ring-offset-color":[{"ring-offset":ac()}],"inset-ring-w":[{"inset-ring":ai()}],"inset-ring-color":[{"inset-ring":ac()}],"text-shadow":[{"text-shadow":["none",n,T,M]}],"text-shadow-color":[{"text-shadow":ac()}],opacity:[{opacity:[w,N,H]}],"mix-blend":[{"mix-blend":[...ak(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":ak()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[w]}],"mask-image-linear-from-pos":[{"mask-linear-from":al()}],"mask-image-linear-to-pos":[{"mask-linear-to":al()}],"mask-image-linear-from-color":[{"mask-linear-from":ac()}],"mask-image-linear-to-color":[{"mask-linear-to":ac()}],"mask-image-t-from-pos":[{"mask-t-from":al()}],"mask-image-t-to-pos":[{"mask-t-to":al()}],"mask-image-t-from-color":[{"mask-t-from":ac()}],"mask-image-t-to-color":[{"mask-t-to":ac()}],"mask-image-r-from-pos":[{"mask-r-from":al()}],"mask-image-r-to-pos":[{"mask-r-to":al()}],"mask-image-r-from-color":[{"mask-r-from":ac()}],"mask-image-r-to-color":[{"mask-r-to":ac()}],"mask-image-b-from-pos":[{"mask-b-from":al()}],"mask-image-b-to-pos":[{"mask-b-to":al()}],"mask-image-b-from-color":[{"mask-b-from":ac()}],"mask-image-b-to-color":[{"mask-b-to":ac()}],"mask-image-l-from-pos":[{"mask-l-from":al()}],"mask-image-l-to-pos":[{"mask-l-to":al()}],"mask-image-l-from-color":[{"mask-l-from":ac()}],"mask-image-l-to-color":[{"mask-l-to":ac()}],"mask-image-x-from-pos":[{"mask-x-from":al()}],"mask-image-x-to-pos":[{"mask-x-to":al()}],"mask-image-x-from-color":[{"mask-x-from":ac()}],"mask-image-x-to-color":[{"mask-x-to":ac()}],"mask-image-y-from-pos":[{"mask-y-from":al()}],"mask-image-y-to-pos":[{"mask-y-to":al()}],"mask-image-y-from-color":[{"mask-y-from":ac()}],"mask-image-y-to-color":[{"mask-y-to":ac()}],"mask-image-radial":[{"mask-radial":[N,H]}],"mask-image-radial-from-pos":[{"mask-radial-from":al()}],"mask-image-radial-to-pos":[{"mask-radial-to":al()}],"mask-image-radial-from-color":[{"mask-radial-from":ac()}],"mask-image-radial-to-color":[{"mask-radial-to":ac()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":B()}],"mask-image-conic-pos":[{"mask-conic":[w]}],"mask-image-conic-from-pos":[{"mask-conic-from":al()}],"mask-image-conic-to-pos":[{"mask-conic-to":al()}],"mask-image-conic-from-color":[{"mask-conic-from":ac()}],"mask-image-conic-to-color":[{"mask-conic-to":ac()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:ad()}],"mask-repeat":[{mask:ae()}],"mask-size":[{mask:af()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",N,H]}],filter:[{filter:["","none",N,H]}],blur:[{blur:am()}],brightness:[{brightness:[w,N,H]}],contrast:[{contrast:[w,N,H]}],"drop-shadow":[{"drop-shadow":["","none",o,T,M]}],"drop-shadow-color":[{"drop-shadow":ac()}],grayscale:[{grayscale:["",w,N,H]}],"hue-rotate":[{"hue-rotate":[w,N,H]}],invert:[{invert:["",w,N,H]}],saturate:[{saturate:[w,N,H]}],sepia:[{sepia:["",w,N,H]}],"backdrop-filter":[{"backdrop-filter":["","none",N,H]}],"backdrop-blur":[{"backdrop-blur":am()}],"backdrop-brightness":[{"backdrop-brightness":[w,N,H]}],"backdrop-contrast":[{"backdrop-contrast":[w,N,H]}],"backdrop-grayscale":[{"backdrop-grayscale":["",w,N,H]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[w,N,H]}],"backdrop-invert":[{"backdrop-invert":["",w,N,H]}],"backdrop-opacity":[{"backdrop-opacity":[w,N,H]}],"backdrop-saturate":[{"backdrop-saturate":[w,N,H]}],"backdrop-sepia":[{"backdrop-sepia":["",w,N,H]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":U()}],"border-spacing-x":[{"border-spacing-x":U()}],"border-spacing-y":[{"border-spacing-y":U()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",N,H]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[w,"initial",N,H]}],ease:[{ease:["linear","initial",s,N,H]}],delay:[{delay:[w,N,H]}],animate:[{animate:["none",t,N,H]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[q,N,H]}],"perspective-origin":[{"perspective-origin":C()}],rotate:[{rotate:an()}],"rotate-x":[{"rotate-x":an()}],"rotate-y":[{"rotate-y":an()}],"rotate-z":[{"rotate-z":an()}],scale:[{scale:ao()}],"scale-x":[{"scale-x":ao()}],"scale-y":[{"scale-y":ao()}],"scale-z":[{"scale-z":ao()}],"scale-3d":["scale-3d"],skew:[{skew:ap()}],"skew-x":[{"skew-x":ap()}],"skew-y":[{"skew-y":ap()}],transform:[{transform:[N,H,"","none","gpu","cpu"]}],"transform-origin":[{origin:C()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:aq()}],"translate-x":[{"translate-x":aq()}],"translate-y":[{"translate-y":aq()}],"translate-z":[{"translate-z":aq()}],"translate-none":["translate-none"],accent:[{accent:ac()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:ac()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",N,H]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":U()}],"scroll-mx":[{"scroll-mx":U()}],"scroll-my":[{"scroll-my":U()}],"scroll-ms":[{"scroll-ms":U()}],"scroll-me":[{"scroll-me":U()}],"scroll-mt":[{"scroll-mt":U()}],"scroll-mr":[{"scroll-mr":U()}],"scroll-mb":[{"scroll-mb":U()}],"scroll-ml":[{"scroll-ml":U()}],"scroll-p":[{"scroll-p":U()}],"scroll-px":[{"scroll-px":U()}],"scroll-py":[{"scroll-py":U()}],"scroll-ps":[{"scroll-ps":U()}],"scroll-pe":[{"scroll-pe":U()}],"scroll-pt":[{"scroll-pt":U()}],"scroll-pr":[{"scroll-pr":U()}],"scroll-pb":[{"scroll-pb":U()}],"scroll-pl":[{"scroll-pl":U()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",N,H]}],fill:[{fill:["none",...ac()]}],"stroke-w":[{stroke:[w,O,I,J]}],stroke:[{stroke:["none",...ac()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}});function ac(...a){return ab((0,d.$)(a))}},1768:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,6133,23)),Promise.resolve().then(c.t.bind(c,6444,23)),Promise.resolve().then(c.t.bind(c,6042,23)),Promise.resolve().then(c.t.bind(c,9477,23)),Promise.resolve().then(c.t.bind(c,9345,23)),Promise.resolve().then(c.t.bind(c,2089,23)),Promise.resolve().then(c.t.bind(c,6577,23)),Promise.resolve().then(c.t.bind(c,1307,23)),Promise.resolve().then(c.t.bind(c,4817,23))},1794:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"isLocalURL",{enumerable:!0,get:function(){return f}});let d=c(9289),e=c(6736);function f(a){if(!(0,d.isAbsoluteUrl)(a))return!0;try{let b=(0,d.getLocationOrigin)(),c=new URL(a,b);return c.origin===b&&(0,e.hasBasePath)(c.pathname)}catch(a){return!1}}},1832:(a,b,c)=>{Promise.resolve().then(c.bind(c,1079)),Promise.resolve().then(c.bind(c,3570)),Promise.resolve().then(c.bind(c,3620)),Promise.resolve().then(c.bind(c,2770)),Promise.resolve().then(c.bind(c,1110)),Promise.resolve().then(c.bind(c,6277)),Promise.resolve().then(c.bind(c,5037)),Promise.resolve().then(c.bind(c,625)),Promise.resolve().then(c.bind(c,9638))},1860:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(2688).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},2157:(a,b,c)=>{"use strict";c.d(b,{L:()=>d});let d=(0,c(3210).createContext)({})},2255:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"pathHasPrefix",{enumerable:!0,get:function(){return e}});let d=c(3931);function e(a,b){if("string"!=typeof a)return!1;let{pathname:c}=(0,d.parsePath)(a);return c===b||c.startsWith(b+"/")}},2308:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{addRefreshMarkerToActiveParallelSegments:function(){return function a(b,c){let[d,e,,g]=b;for(let h in d.includes(f.PAGE_SEGMENT_KEY)&&"refresh"!==g&&(b[2]=c,b[3]="refresh"),e)a(e[h],c)}},refreshInactiveParallelSegments:function(){return g}});let d=c(6928),e=c(9008),f=c(3913);async function g(a){let b=new Set;await h({...a,rootTree:a.updatedTree,fetchedSegments:b})}async function h(a){let{navigatedAt:b,state:c,updatedTree:f,updatedCache:g,includeNextUrl:i,fetchedSegments:j,rootTree:k=f,canonicalUrl:l}=a,[,m,n,o]=f,p=[];if(n&&n!==l&&"refresh"===o&&!j.has(n)){j.add(n);let a=(0,e.fetchServerResponse)(new URL(n,location.origin),{flightRouterState:[k[0],k[1],k[2],"refetch"],nextUrl:i?c.nextUrl:null}).then(a=>{let{flightData:c}=a;if("string"!=typeof c)for(let a of c)(0,d.applyFlightData)(b,g,g,a)});p.push(a)}for(let a in m){let d=h({navigatedAt:b,state:c,updatedTree:m[a],updatedCache:g,includeNextUrl:i,fetchedSegments:j,rootTree:k,canonicalUrl:l});p.push(d)}await Promise.all(p)}("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},2582:(a,b,c)=>{"use strict";c.d(b,{Q:()=>d});let d=(0,c(3210).createContext)({transformPagePoint:a=>a,isStatic:!1,reducedMotion:"never"})},2688:(a,b,c)=>{"use strict";c.d(b,{A:()=>i});var d=c(3210);let e=a=>{let b=a.replace(/^([A-Z])|[\s-_]+(\w)/g,(a,b,c)=>c?c.toUpperCase():b.toLowerCase());return b.charAt(0).toUpperCase()+b.slice(1)},f=(...a)=>a.filter((a,b,c)=>!!a&&""!==a.trim()&&c.indexOf(a)===b).join(" ").trim();var g={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let h=(0,d.forwardRef)(({color:a="currentColor",size:b=24,strokeWidth:c=2,absoluteStrokeWidth:e,className:h="",children:i,iconNode:j,...k},l)=>(0,d.createElement)("svg",{ref:l,...g,width:b,height:b,stroke:a,strokeWidth:e?24*Number(c)/Number(b):c,className:f("lucide",h),...!i&&!(a=>{for(let b in a)if(b.startsWith("aria-")||"role"===b||"title"===b)return!0})(k)&&{"aria-hidden":"true"},...k},[...j.map(([a,b])=>(0,d.createElement)(a,b)),...Array.isArray(i)?i:[i]])),i=(a,b)=>{let c=(0,d.forwardRef)(({className:c,...g},i)=>(0,d.createElement)(h,{ref:i,iconNode:b,className:f(`lucide-${e(a).replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()}`,`lucide-${a}`,c),...g}));return c.displayName=e(a),c}},2708:(a,b)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"errorOnce",{enumerable:!0,get:function(){return c}});let c=a=>{}},2743:(a,b,c)=>{"use strict";c.d(b,{E:()=>e});var d=c(3210);let e=c(7044).B?d.useLayoutEffect:d.useEffect},2770:(a,b,c)=>{"use strict";c.d(b,{GalleryPreview:()=>bm});var d,e,f,g=c(687),h=c(3210),i=c.t(h,2),j=c(4124),k=c(9523);function l(a,b,{checkForDefaultPrevented:c=!0}={}){return function(d){if(a?.(d),!1===c||!d.defaultPrevented)return b?.(d)}}var m=c(8599),n=globalThis?.document?h.useLayoutEffect:()=>{},o=i[" useId ".trim().toString()]||(()=>void 0),p=0;function q(a){let[b,c]=h.useState(o());return n(()=>{a||c(a=>a??String(p++))},[a]),a||(b?`radix-${b}`:"")}var r=i[" useInsertionEffect ".trim().toString()]||n;Symbol("RADIX:SYNC_STATE");var s=c(4163);function t(a){let b=h.useRef(a);return h.useEffect(()=>{b.current=a}),h.useMemo(()=>(...a)=>b.current?.(...a),[])}var u="dismissableLayer.update",v=h.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),w=h.forwardRef((a,b)=>{let{disableOutsidePointerEvents:c=!1,onEscapeKeyDown:d,onPointerDownOutside:f,onFocusOutside:i,onInteractOutside:j,onDismiss:k,...n}=a,o=h.useContext(v),[p,q]=h.useState(null),r=p?.ownerDocument??globalThis?.document,[,w]=h.useState({}),z=(0,m.s)(b,a=>q(a)),A=Array.from(o.layers),[B]=[...o.layersWithOutsidePointerEventsDisabled].slice(-1),C=A.indexOf(B),D=p?A.indexOf(p):-1,E=o.layersWithOutsidePointerEventsDisabled.size>0,F=D>=C,G=function(a,b=globalThis?.document){let c=t(a),d=h.useRef(!1),e=h.useRef(()=>{});return h.useEffect(()=>{let a=a=>{if(a.target&&!d.current){let d=function(){y("dismissableLayer.pointerDownOutside",c,f,{discrete:!0})},f={originalEvent:a};"touch"===a.pointerType?(b.removeEventListener("click",e.current),e.current=d,b.addEventListener("click",e.current,{once:!0})):d()}else b.removeEventListener("click",e.current);d.current=!1},f=window.setTimeout(()=>{b.addEventListener("pointerdown",a)},0);return()=>{window.clearTimeout(f),b.removeEventListener("pointerdown",a),b.removeEventListener("click",e.current)}},[b,c]),{onPointerDownCapture:()=>d.current=!0}}(a=>{let b=a.target,c=[...o.branches].some(a=>a.contains(b));F&&!c&&(f?.(a),j?.(a),a.defaultPrevented||k?.())},r),H=function(a,b=globalThis?.document){let c=t(a),d=h.useRef(!1);return h.useEffect(()=>{let a=a=>{a.target&&!d.current&&y("dismissableLayer.focusOutside",c,{originalEvent:a},{discrete:!1})};return b.addEventListener("focusin",a),()=>b.removeEventListener("focusin",a)},[b,c]),{onFocusCapture:()=>d.current=!0,onBlurCapture:()=>d.current=!1}}(a=>{let b=a.target;![...o.branches].some(a=>a.contains(b))&&(i?.(a),j?.(a),a.defaultPrevented||k?.())},r);return!function(a,b=globalThis?.document){let c=t(a);h.useEffect(()=>{let a=a=>{"Escape"===a.key&&c(a)};return b.addEventListener("keydown",a,{capture:!0}),()=>b.removeEventListener("keydown",a,{capture:!0})},[c,b])}(a=>{D===o.layers.size-1&&(d?.(a),!a.defaultPrevented&&k&&(a.preventDefault(),k()))},r),h.useEffect(()=>{if(p)return c&&(0===o.layersWithOutsidePointerEventsDisabled.size&&(e=r.body.style.pointerEvents,r.body.style.pointerEvents="none"),o.layersWithOutsidePointerEventsDisabled.add(p)),o.layers.add(p),x(),()=>{c&&1===o.layersWithOutsidePointerEventsDisabled.size&&(r.body.style.pointerEvents=e)}},[p,r,c,o]),h.useEffect(()=>()=>{p&&(o.layers.delete(p),o.layersWithOutsidePointerEventsDisabled.delete(p),x())},[p,o]),h.useEffect(()=>{let a=()=>w({});return document.addEventListener(u,a),()=>document.removeEventListener(u,a)},[]),(0,g.jsx)(s.sG.div,{...n,ref:z,style:{pointerEvents:E?F?"auto":"none":void 0,...a.style},onFocusCapture:l(a.onFocusCapture,H.onFocusCapture),onBlurCapture:l(a.onBlurCapture,H.onBlurCapture),onPointerDownCapture:l(a.onPointerDownCapture,G.onPointerDownCapture)})});function x(){let a=new CustomEvent(u);document.dispatchEvent(a)}function y(a,b,c,{discrete:d}){let e=c.originalEvent.target,f=new CustomEvent(a,{bubbles:!1,cancelable:!0,detail:c});b&&e.addEventListener(a,b,{once:!0}),d?(0,s.hO)(e,f):e.dispatchEvent(f)}w.displayName="DismissableLayer",h.forwardRef((a,b)=>{let c=h.useContext(v),d=h.useRef(null),e=(0,m.s)(b,d);return h.useEffect(()=>{let a=d.current;if(a)return c.branches.add(a),()=>{c.branches.delete(a)}},[c.branches]),(0,g.jsx)(s.sG.div,{...a,ref:e})}).displayName="DismissableLayerBranch";var z="focusScope.autoFocusOnMount",A="focusScope.autoFocusOnUnmount",B={bubbles:!1,cancelable:!0},C=h.forwardRef((a,b)=>{let{loop:c=!1,trapped:d=!1,onMountAutoFocus:e,onUnmountAutoFocus:f,...i}=a,[j,k]=h.useState(null),l=t(e),n=t(f),o=h.useRef(null),p=(0,m.s)(b,a=>k(a)),q=h.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;h.useEffect(()=>{if(d){let a=function(a){if(q.paused||!j)return;let b=a.target;j.contains(b)?o.current=b:F(o.current,{select:!0})},b=function(a){if(q.paused||!j)return;let b=a.relatedTarget;null!==b&&(j.contains(b)||F(o.current,{select:!0}))};document.addEventListener("focusin",a),document.addEventListener("focusout",b);let c=new MutationObserver(function(a){if(document.activeElement===document.body)for(let b of a)b.removedNodes.length>0&&F(j)});return j&&c.observe(j,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",a),document.removeEventListener("focusout",b),c.disconnect()}}},[d,j,q.paused]),h.useEffect(()=>{if(j){G.add(q);let a=document.activeElement;if(!j.contains(a)){let b=new CustomEvent(z,B);j.addEventListener(z,l),j.dispatchEvent(b),b.defaultPrevented||(function(a,{select:b=!1}={}){let c=document.activeElement;for(let d of a)if(F(d,{select:b}),document.activeElement!==c)return}(D(j).filter(a=>"A"!==a.tagName),{select:!0}),document.activeElement===a&&F(j))}return()=>{j.removeEventListener(z,l),setTimeout(()=>{let b=new CustomEvent(A,B);j.addEventListener(A,n),j.dispatchEvent(b),b.defaultPrevented||F(a??document.body,{select:!0}),j.removeEventListener(A,n),G.remove(q)},0)}}},[j,l,n,q]);let r=h.useCallback(a=>{if(!c&&!d||q.paused)return;let b="Tab"===a.key&&!a.altKey&&!a.ctrlKey&&!a.metaKey,e=document.activeElement;if(b&&e){let b=a.currentTarget,[d,f]=function(a){let b=D(a);return[E(b,a),E(b.reverse(),a)]}(b);d&&f?a.shiftKey||e!==f?a.shiftKey&&e===d&&(a.preventDefault(),c&&F(f,{select:!0})):(a.preventDefault(),c&&F(d,{select:!0})):e===b&&a.preventDefault()}},[c,d,q.paused]);return(0,g.jsx)(s.sG.div,{tabIndex:-1,...i,ref:p,onKeyDown:r})});function D(a){let b=[],c=document.createTreeWalker(a,NodeFilter.SHOW_ELEMENT,{acceptNode:a=>{let b="INPUT"===a.tagName&&"hidden"===a.type;return a.disabled||a.hidden||b?NodeFilter.FILTER_SKIP:a.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;c.nextNode();)b.push(c.currentNode);return b}function E(a,b){for(let c of a)if(!function(a,{upTo:b}){if("hidden"===getComputedStyle(a).visibility)return!0;for(;a&&(void 0===b||a!==b);){if("none"===getComputedStyle(a).display)return!0;a=a.parentElement}return!1}(c,{upTo:b}))return c}function F(a,{select:b=!1}={}){if(a&&a.focus){var c;let d=document.activeElement;a.focus({preventScroll:!0}),a!==d&&(c=a)instanceof HTMLInputElement&&"select"in c&&b&&a.select()}}C.displayName="FocusScope";var G=function(){let a=[];return{add(b){let c=a[0];b!==c&&c?.pause(),(a=H(a,b)).unshift(b)},remove(b){a=H(a,b),a[0]?.resume()}}}();function H(a,b){let c=[...a],d=c.indexOf(b);return -1!==d&&c.splice(d,1),c}var I=c(1215),J=h.forwardRef((a,b)=>{let{container:c,...d}=a,[e,f]=h.useState(!1);n(()=>f(!0),[]);let i=c||e&&globalThis?.document?.body;return i?I.createPortal((0,g.jsx)(s.sG.div,{...d,ref:b}),i):null});J.displayName="Portal";var K=a=>{let{present:b,children:c}=a,d=function(a){var b,c;let[d,e]=h.useState(),f=h.useRef(null),g=h.useRef(a),i=h.useRef("none"),[j,k]=(b=a?"mounted":"unmounted",c={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},h.useReducer((a,b)=>c[a][b]??a,b));return h.useEffect(()=>{let a=L(f.current);i.current="mounted"===j?a:"none"},[j]),n(()=>{let b=f.current,c=g.current;if(c!==a){let d=i.current,e=L(b);a?k("MOUNT"):"none"===e||b?.display==="none"?k("UNMOUNT"):c&&d!==e?k("ANIMATION_OUT"):k("UNMOUNT"),g.current=a}},[a,k]),n(()=>{if(d){let a,b=d.ownerDocument.defaultView??window,c=c=>{let e=L(f.current).includes(c.animationName);if(c.target===d&&e&&(k("ANIMATION_END"),!g.current)){let c=d.style.animationFillMode;d.style.animationFillMode="forwards",a=b.setTimeout(()=>{"forwards"===d.style.animationFillMode&&(d.style.animationFillMode=c)})}},e=a=>{a.target===d&&(i.current=L(f.current))};return d.addEventListener("animationstart",e),d.addEventListener("animationcancel",c),d.addEventListener("animationend",c),()=>{b.clearTimeout(a),d.removeEventListener("animationstart",e),d.removeEventListener("animationcancel",c),d.removeEventListener("animationend",c)}}k("ANIMATION_END")},[d,k]),{isPresent:["mounted","unmountSuspended"].includes(j),ref:h.useCallback(a=>{f.current=a?getComputedStyle(a):null,e(a)},[])}}(b),e="function"==typeof c?c({present:d.isPresent}):h.Children.only(c),f=(0,m.s)(d.ref,function(a){let b=Object.getOwnPropertyDescriptor(a.props,"ref")?.get,c=b&&"isReactWarning"in b&&b.isReactWarning;return c?a.ref:(c=(b=Object.getOwnPropertyDescriptor(a,"ref")?.get)&&"isReactWarning"in b&&b.isReactWarning)?a.props.ref:a.props.ref||a.ref}(e));return"function"==typeof c||d.isPresent?h.cloneElement(e,{ref:f}):null};function L(a){return a?.animationName||"none"}K.displayName="Presence";var M=0;function N(){let a=document.createElement("span");return a.setAttribute("data-radix-focus-guard",""),a.tabIndex=0,a.style.outline="none",a.style.opacity="0",a.style.position="fixed",a.style.pointerEvents="none",a}var O=function(){return(O=Object.assign||function(a){for(var b,c=1,d=arguments.length;c<d;c++)for(var e in b=arguments[c])Object.prototype.hasOwnProperty.call(b,e)&&(a[e]=b[e]);return a}).apply(this,arguments)};function P(a,b){var c={};for(var d in a)Object.prototype.hasOwnProperty.call(a,d)&&0>b.indexOf(d)&&(c[d]=a[d]);if(null!=a&&"function"==typeof Object.getOwnPropertySymbols)for(var e=0,d=Object.getOwnPropertySymbols(a);e<d.length;e++)0>b.indexOf(d[e])&&Object.prototype.propertyIsEnumerable.call(a,d[e])&&(c[d[e]]=a[d[e]]);return c}Object.create;Object.create;var Q=("function"==typeof SuppressedError&&SuppressedError,"right-scroll-bar-position"),R="width-before-scroll-bar";function S(a,b){return"function"==typeof a?a(b):a&&(a.current=b),a}var T="undefined"!=typeof window?h.useLayoutEffect:h.useEffect,U=new WeakMap;function V(a){return a}var W=function(a){void 0===a&&(a={});var b,c,d,e=(void 0===b&&(b=V),c=[],d=!1,{read:function(){if(d)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return c.length?c[c.length-1]:null},useMedium:function(a){var e=b(a,d);return c.push(e),function(){c=c.filter(function(a){return a!==e})}},assignSyncMedium:function(a){for(d=!0;c.length;){var b=c;c=[],b.forEach(a)}c={push:function(b){return a(b)},filter:function(){return c}}},assignMedium:function(a){d=!0;var b=[];if(c.length){var e=c;c=[],e.forEach(a),b=c}var f=function(){var c=b;b=[],c.forEach(a)},g=function(){return Promise.resolve().then(f)};g(),c={push:function(a){b.push(a),g()},filter:function(a){return b=b.filter(a),c}}}});return e.options=O({async:!0,ssr:!1},a),e}(),X=function(){},Y=h.forwardRef(function(a,b){var c,d,e,f,g=h.useRef(null),i=h.useState({onScrollCapture:X,onWheelCapture:X,onTouchMoveCapture:X}),j=i[0],k=i[1],l=a.forwardProps,m=a.children,n=a.className,o=a.removeScrollBar,p=a.enabled,q=a.shards,r=a.sideCar,s=a.noRelative,t=a.noIsolation,u=a.inert,v=a.allowPinchZoom,w=a.as,x=a.gapMode,y=P(a,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),z=(c=[g,b],d=function(a){return c.forEach(function(b){return S(b,a)})},(e=(0,h.useState)(function(){return{value:null,callback:d,facade:{get current(){return e.value},set current(value){var a=e.value;a!==value&&(e.value=value,e.callback(value,a))}}}})[0]).callback=d,f=e.facade,T(function(){var a=U.get(f);if(a){var b=new Set(a),d=new Set(c),e=f.current;b.forEach(function(a){d.has(a)||S(a,null)}),d.forEach(function(a){b.has(a)||S(a,e)})}U.set(f,c)},[c]),f),A=O(O({},y),j);return h.createElement(h.Fragment,null,p&&h.createElement(r,{sideCar:W,removeScrollBar:o,shards:q,noRelative:s,noIsolation:t,inert:u,setCallbacks:k,allowPinchZoom:!!v,lockRef:g,gapMode:x}),l?h.cloneElement(h.Children.only(m),O(O({},A),{ref:z})):h.createElement(void 0===w?"div":w,O({},A,{className:n,ref:z}),m))});Y.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},Y.classNames={fullWidth:R,zeroRight:Q};var Z=function(a){var b=a.sideCar,c=P(a,["sideCar"]);if(!b)throw Error("Sidecar: please provide `sideCar` property to import the right car");var d=b.read();if(!d)throw Error("Sidecar medium not found");return h.createElement(d,O({},c))};Z.isSideCarExport=!0;var $=function(){var a=0,b=null;return{add:function(d){if(0==a&&(b=function(){if(!document)return null;var a=document.createElement("style");a.type="text/css";var b=f||c.nc;return b&&a.setAttribute("nonce",b),a}())){var e,g;(e=b).styleSheet?e.styleSheet.cssText=d:e.appendChild(document.createTextNode(d)),g=b,(document.head||document.getElementsByTagName("head")[0]).appendChild(g)}a++},remove:function(){--a||!b||(b.parentNode&&b.parentNode.removeChild(b),b=null)}}},_=function(){var a=$();return function(b,c){h.useEffect(function(){return a.add(b),function(){a.remove()}},[b&&c])}},aa=function(){var a=_();return function(b){return a(b.styles,b.dynamic),null}},ab={left:0,top:0,right:0,gap:0},ac=function(a){return parseInt(a||"",10)||0},ad=function(a){var b=window.getComputedStyle(document.body),c=b["padding"===a?"paddingLeft":"marginLeft"],d=b["padding"===a?"paddingTop":"marginTop"],e=b["padding"===a?"paddingRight":"marginRight"];return[ac(c),ac(d),ac(e)]},ae=function(a){if(void 0===a&&(a="margin"),"undefined"==typeof window)return ab;var b=ad(a),c=document.documentElement.clientWidth,d=window.innerWidth;return{left:b[0],top:b[1],right:b[2],gap:Math.max(0,d-c+b[2]-b[0])}},af=aa(),ag="data-scroll-locked",ah=function(a,b,c,d){var e=a.left,f=a.top,g=a.right,h=a.gap;return void 0===c&&(c="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(d,";\n   padding-right: ").concat(h,"px ").concat(d,";\n  }\n  body[").concat(ag,"] {\n    overflow: hidden ").concat(d,";\n    overscroll-behavior: contain;\n    ").concat([b&&"position: relative ".concat(d,";"),"margin"===c&&"\n    padding-left: ".concat(e,"px;\n    padding-top: ").concat(f,"px;\n    padding-right: ").concat(g,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(h,"px ").concat(d,";\n    "),"padding"===c&&"padding-right: ".concat(h,"px ").concat(d,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(Q," {\n    right: ").concat(h,"px ").concat(d,";\n  }\n  \n  .").concat(R," {\n    margin-right: ").concat(h,"px ").concat(d,";\n  }\n  \n  .").concat(Q," .").concat(Q," {\n    right: 0 ").concat(d,";\n  }\n  \n  .").concat(R," .").concat(R," {\n    margin-right: 0 ").concat(d,";\n  }\n  \n  body[").concat(ag,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(h,"px;\n  }\n")},ai=function(){var a=parseInt(document.body.getAttribute(ag)||"0",10);return isFinite(a)?a:0},aj=function(){h.useEffect(function(){return document.body.setAttribute(ag,(ai()+1).toString()),function(){var a=ai()-1;a<=0?document.body.removeAttribute(ag):document.body.setAttribute(ag,a.toString())}},[])},ak=function(a){var b=a.noRelative,c=a.noImportant,d=a.gapMode,e=void 0===d?"margin":d;aj();var f=h.useMemo(function(){return ae(e)},[e]);return h.createElement(af,{styles:ah(f,!b,e,c?"":"!important")})},al=!1;if("undefined"!=typeof window)try{var am=Object.defineProperty({},"passive",{get:function(){return al=!0,!0}});window.addEventListener("test",am,am),window.removeEventListener("test",am,am)}catch(a){al=!1}var an=!!al&&{passive:!1},ao=function(a,b){if(!(a instanceof Element))return!1;var c=window.getComputedStyle(a);return"hidden"!==c[b]&&(c.overflowY!==c.overflowX||"TEXTAREA"===a.tagName||"visible"!==c[b])},ap=function(a,b){var c=b.ownerDocument,d=b;do{if("undefined"!=typeof ShadowRoot&&d instanceof ShadowRoot&&(d=d.host),aq(a,d)){var e=ar(a,d);if(e[1]>e[2])return!0}d=d.parentNode}while(d&&d!==c.body);return!1},aq=function(a,b){return"v"===a?ao(b,"overflowY"):ao(b,"overflowX")},ar=function(a,b){return"v"===a?[b.scrollTop,b.scrollHeight,b.clientHeight]:[b.scrollLeft,b.scrollWidth,b.clientWidth]},as=function(a,b,c,d,e){var f,g=(f=window.getComputedStyle(b).direction,"h"===a&&"rtl"===f?-1:1),h=g*d,i=c.target,j=b.contains(i),k=!1,l=h>0,m=0,n=0;do{if(!i)break;var o=ar(a,i),p=o[0],q=o[1]-o[2]-g*p;(p||q)&&aq(a,i)&&(m+=q,n+=p);var r=i.parentNode;i=r&&r.nodeType===Node.DOCUMENT_FRAGMENT_NODE?r.host:r}while(!j&&i!==document.body||j&&(b.contains(i)||b===i));return l&&(e&&1>Math.abs(m)||!e&&h>m)?k=!0:!l&&(e&&1>Math.abs(n)||!e&&-h>n)&&(k=!0),k},at=function(a){return"changedTouches"in a?[a.changedTouches[0].clientX,a.changedTouches[0].clientY]:[0,0]},au=function(a){return[a.deltaX,a.deltaY]},av=function(a){return a&&"current"in a?a.current:a},aw=0,ax=[];let ay=(d=function(a){var b=h.useRef([]),c=h.useRef([0,0]),d=h.useRef(),e=h.useState(aw++)[0],f=h.useState(aa)[0],g=h.useRef(a);h.useEffect(function(){g.current=a},[a]),h.useEffect(function(){if(a.inert){document.body.classList.add("block-interactivity-".concat(e));var b=(function(a,b,c){if(c||2==arguments.length)for(var d,e=0,f=b.length;e<f;e++)!d&&e in b||(d||(d=Array.prototype.slice.call(b,0,e)),d[e]=b[e]);return a.concat(d||Array.prototype.slice.call(b))})([a.lockRef.current],(a.shards||[]).map(av),!0).filter(Boolean);return b.forEach(function(a){return a.classList.add("allow-interactivity-".concat(e))}),function(){document.body.classList.remove("block-interactivity-".concat(e)),b.forEach(function(a){return a.classList.remove("allow-interactivity-".concat(e))})}}},[a.inert,a.lockRef.current,a.shards]);var i=h.useCallback(function(a,b){if("touches"in a&&2===a.touches.length||"wheel"===a.type&&a.ctrlKey)return!g.current.allowPinchZoom;var e,f=at(a),h=c.current,i="deltaX"in a?a.deltaX:h[0]-f[0],j="deltaY"in a?a.deltaY:h[1]-f[1],k=a.target,l=Math.abs(i)>Math.abs(j)?"h":"v";if("touches"in a&&"h"===l&&"range"===k.type)return!1;var m=ap(l,k);if(!m)return!0;if(m?e=l:(e="v"===l?"h":"v",m=ap(l,k)),!m)return!1;if(!d.current&&"changedTouches"in a&&(i||j)&&(d.current=e),!e)return!0;var n=d.current||e;return as(n,b,a,"h"===n?i:j,!0)},[]),j=h.useCallback(function(a){if(ax.length&&ax[ax.length-1]===f){var c="deltaY"in a?au(a):at(a),d=b.current.filter(function(b){var d;return b.name===a.type&&(b.target===a.target||a.target===b.shadowParent)&&(d=b.delta,d[0]===c[0]&&d[1]===c[1])})[0];if(d&&d.should){a.cancelable&&a.preventDefault();return}if(!d){var e=(g.current.shards||[]).map(av).filter(Boolean).filter(function(b){return b.contains(a.target)});(e.length>0?i(a,e[0]):!g.current.noIsolation)&&a.cancelable&&a.preventDefault()}}},[]),k=h.useCallback(function(a,c,d,e){var f={name:a,delta:c,target:d,should:e,shadowParent:function(a){for(var b=null;null!==a;)a instanceof ShadowRoot&&(b=a.host,a=a.host),a=a.parentNode;return b}(d)};b.current.push(f),setTimeout(function(){b.current=b.current.filter(function(a){return a!==f})},1)},[]),l=h.useCallback(function(a){c.current=at(a),d.current=void 0},[]),m=h.useCallback(function(b){k(b.type,au(b),b.target,i(b,a.lockRef.current))},[]),n=h.useCallback(function(b){k(b.type,at(b),b.target,i(b,a.lockRef.current))},[]);h.useEffect(function(){return ax.push(f),a.setCallbacks({onScrollCapture:m,onWheelCapture:m,onTouchMoveCapture:n}),document.addEventListener("wheel",j,an),document.addEventListener("touchmove",j,an),document.addEventListener("touchstart",l,an),function(){ax=ax.filter(function(a){return a!==f}),document.removeEventListener("wheel",j,an),document.removeEventListener("touchmove",j,an),document.removeEventListener("touchstart",l,an)}},[]);var o=a.removeScrollBar,p=a.inert;return h.createElement(h.Fragment,null,p?h.createElement(f,{styles:"\n  .block-interactivity-".concat(e," {pointer-events: none;}\n  .allow-interactivity-").concat(e," {pointer-events: all;}\n")}):null,o?h.createElement(ak,{noRelative:a.noRelative,gapMode:a.gapMode}):null)},W.useMedium(d),Z);var az=h.forwardRef(function(a,b){return h.createElement(Y,O({},a,{ref:b,sideCar:ay}))});az.classNames=Y.classNames;var aA=new WeakMap,aB=new WeakMap,aC={},aD=0,aE=function(a){return a&&(a.host||aE(a.parentNode))},aF=function(a,b,c,d){var e=(Array.isArray(a)?a:[a]).map(function(a){if(b.contains(a))return a;var c=aE(a);return c&&b.contains(c)?c:(console.error("aria-hidden",a,"in not contained inside",b,". Doing nothing"),null)}).filter(function(a){return!!a});aC[c]||(aC[c]=new WeakMap);var f=aC[c],g=[],h=new Set,i=new Set(e),j=function(a){!a||h.has(a)||(h.add(a),j(a.parentNode))};e.forEach(j);var k=function(a){!a||i.has(a)||Array.prototype.forEach.call(a.children,function(a){if(h.has(a))k(a);else try{var b=a.getAttribute(d),e=null!==b&&"false"!==b,i=(aA.get(a)||0)+1,j=(f.get(a)||0)+1;aA.set(a,i),f.set(a,j),g.push(a),1===i&&e&&aB.set(a,!0),1===j&&a.setAttribute(c,"true"),e||a.setAttribute(d,"true")}catch(b){console.error("aria-hidden: cannot operate on ",a,b)}})};return k(b),h.clear(),aD++,function(){g.forEach(function(a){var b=aA.get(a)-1,e=f.get(a)-1;aA.set(a,b),f.set(a,e),b||(aB.has(a)||a.removeAttribute(d),aB.delete(a)),e||a.removeAttribute(c)}),--aD||(aA=new WeakMap,aA=new WeakMap,aB=new WeakMap,aC={})}},aG=function(a,b,c){void 0===c&&(c="data-aria-hidden");var d=Array.from(Array.isArray(a)?a:[a]),e=b||("undefined"==typeof document?null:(Array.isArray(a)?a[0]:a).ownerDocument.body);return e?(d.push.apply(d,Array.from(e.querySelectorAll("[aria-live], script"))),aF(d,e,c,"aria-hidden")):function(){return null}},aH=c(8730),aI="Dialog",[aJ,aK]=function(a,b=[]){let c=[],d=()=>{let b=c.map(a=>h.createContext(a));return function(c){let d=c?.[a]||b;return h.useMemo(()=>({[`__scope${a}`]:{...c,[a]:d}}),[c,d])}};return d.scopeName=a,[function(b,d){let e=h.createContext(d),f=c.length;c=[...c,d];let i=b=>{let{scope:c,children:d,...i}=b,j=c?.[a]?.[f]||e,k=h.useMemo(()=>i,Object.values(i));return(0,g.jsx)(j.Provider,{value:k,children:d})};return i.displayName=b+"Provider",[i,function(c,g){let i=g?.[a]?.[f]||e,j=h.useContext(i);if(j)return j;if(void 0!==d)return d;throw Error(`\`${c}\` must be used within \`${b}\``)}]},function(...a){let b=a[0];if(1===a.length)return b;let c=()=>{let c=a.map(a=>({useScope:a(),scopeName:a.scopeName}));return function(a){let d=c.reduce((b,{useScope:c,scopeName:d})=>{let e=c(a)[`__scope${d}`];return{...b,...e}},{});return h.useMemo(()=>({[`__scope${b.scopeName}`]:d}),[d])}};return c.scopeName=b.scopeName,c}(d,...b)]}(aI),[aL,aM]=aJ(aI),aN=a=>{let{__scopeDialog:b,children:c,open:d,defaultOpen:e,onOpenChange:f,modal:i=!0}=a,j=h.useRef(null),k=h.useRef(null),[l,m]=function({prop:a,defaultProp:b,onChange:c=()=>{},caller:d}){let[e,f,g]=function({defaultProp:a,onChange:b}){let[c,d]=h.useState(a),e=h.useRef(c),f=h.useRef(b);return r(()=>{f.current=b},[b]),h.useEffect(()=>{e.current!==c&&(f.current?.(c),e.current=c)},[c,e]),[c,d,f]}({defaultProp:b,onChange:c}),i=void 0!==a,j=i?a:e;{let b=h.useRef(void 0!==a);h.useEffect(()=>{let a=b.current;if(a!==i){let b=i?"controlled":"uncontrolled";console.warn(`${d} is changing from ${a?"controlled":"uncontrolled"} to ${b}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}b.current=i},[i,d])}return[j,h.useCallback(b=>{if(i){let c="function"==typeof b?b(a):b;c!==a&&g.current?.(c)}else f(b)},[i,a,f,g])]}({prop:d,defaultProp:e??!1,onChange:f,caller:aI});return(0,g.jsx)(aL,{scope:b,triggerRef:j,contentRef:k,contentId:q(),titleId:q(),descriptionId:q(),open:l,onOpenChange:m,onOpenToggle:h.useCallback(()=>m(a=>!a),[m]),modal:i,children:c})};aN.displayName=aI;var aO="DialogTrigger",aP=h.forwardRef((a,b)=>{let{__scopeDialog:c,...d}=a,e=aM(aO,c),f=(0,m.s)(b,e.triggerRef);return(0,g.jsx)(s.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":e.open,"aria-controls":e.contentId,"data-state":a5(e.open),...d,ref:f,onClick:l(a.onClick,e.onOpenToggle)})});aP.displayName=aO;var aQ="DialogPortal",[aR,aS]=aJ(aQ,{forceMount:void 0}),aT=a=>{let{__scopeDialog:b,forceMount:c,children:d,container:e}=a,f=aM(aQ,b);return(0,g.jsx)(aR,{scope:b,forceMount:c,children:h.Children.map(d,a=>(0,g.jsx)(K,{present:c||f.open,children:(0,g.jsx)(J,{asChild:!0,container:e,children:a})}))})};aT.displayName=aQ;var aU="DialogOverlay",aV=h.forwardRef((a,b)=>{let c=aS(aU,a.__scopeDialog),{forceMount:d=c.forceMount,...e}=a,f=aM(aU,a.__scopeDialog);return f.modal?(0,g.jsx)(K,{present:d||f.open,children:(0,g.jsx)(aX,{...e,ref:b})}):null});aV.displayName=aU;var aW=(0,aH.TL)("DialogOverlay.RemoveScroll"),aX=h.forwardRef((a,b)=>{let{__scopeDialog:c,...d}=a,e=aM(aU,c);return(0,g.jsx)(az,{as:aW,allowPinchZoom:!0,shards:[e.contentRef],children:(0,g.jsx)(s.sG.div,{"data-state":a5(e.open),...d,ref:b,style:{pointerEvents:"auto",...d.style}})})}),aY="DialogContent",aZ=h.forwardRef((a,b)=>{let c=aS(aY,a.__scopeDialog),{forceMount:d=c.forceMount,...e}=a,f=aM(aY,a.__scopeDialog);return(0,g.jsx)(K,{present:d||f.open,children:f.modal?(0,g.jsx)(a$,{...e,ref:b}):(0,g.jsx)(a_,{...e,ref:b})})});aZ.displayName=aY;var a$=h.forwardRef((a,b)=>{let c=aM(aY,a.__scopeDialog),d=h.useRef(null),e=(0,m.s)(b,c.contentRef,d);return h.useEffect(()=>{let a=d.current;if(a)return aG(a)},[]),(0,g.jsx)(a0,{...a,ref:e,trapFocus:c.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:l(a.onCloseAutoFocus,a=>{a.preventDefault(),c.triggerRef.current?.focus()}),onPointerDownOutside:l(a.onPointerDownOutside,a=>{let b=a.detail.originalEvent,c=0===b.button&&!0===b.ctrlKey;(2===b.button||c)&&a.preventDefault()}),onFocusOutside:l(a.onFocusOutside,a=>a.preventDefault())})}),a_=h.forwardRef((a,b)=>{let c=aM(aY,a.__scopeDialog),d=h.useRef(!1),e=h.useRef(!1);return(0,g.jsx)(a0,{...a,ref:b,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:b=>{a.onCloseAutoFocus?.(b),b.defaultPrevented||(d.current||c.triggerRef.current?.focus(),b.preventDefault()),d.current=!1,e.current=!1},onInteractOutside:b=>{a.onInteractOutside?.(b),b.defaultPrevented||(d.current=!0,"pointerdown"===b.detail.originalEvent.type&&(e.current=!0));let f=b.target;c.triggerRef.current?.contains(f)&&b.preventDefault(),"focusin"===b.detail.originalEvent.type&&e.current&&b.preventDefault()}})}),a0=h.forwardRef((a,b)=>{let{__scopeDialog:c,trapFocus:d,onOpenAutoFocus:e,onCloseAutoFocus:f,...i}=a,j=aM(aY,c),k=h.useRef(null),l=(0,m.s)(b,k);return h.useEffect(()=>{let a=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",a[0]??N()),document.body.insertAdjacentElement("beforeend",a[1]??N()),M++,()=>{1===M&&document.querySelectorAll("[data-radix-focus-guard]").forEach(a=>a.remove()),M--}},[]),(0,g.jsxs)(g.Fragment,{children:[(0,g.jsx)(C,{asChild:!0,loop:!0,trapped:d,onMountAutoFocus:e,onUnmountAutoFocus:f,children:(0,g.jsx)(w,{role:"dialog",id:j.contentId,"aria-describedby":j.descriptionId,"aria-labelledby":j.titleId,"data-state":a5(j.open),...i,ref:l,onDismiss:()=>j.onOpenChange(!1)})}),(0,g.jsxs)(g.Fragment,{children:[(0,g.jsx)(a9,{titleId:j.titleId}),(0,g.jsx)(ba,{contentRef:k,descriptionId:j.descriptionId})]})]})}),a1="DialogTitle";h.forwardRef((a,b)=>{let{__scopeDialog:c,...d}=a,e=aM(a1,c);return(0,g.jsx)(s.sG.h2,{id:e.titleId,...d,ref:b})}).displayName=a1;var a2="DialogDescription";h.forwardRef((a,b)=>{let{__scopeDialog:c,...d}=a,e=aM(a2,c);return(0,g.jsx)(s.sG.p,{id:e.descriptionId,...d,ref:b})}).displayName=a2;var a3="DialogClose",a4=h.forwardRef((a,b)=>{let{__scopeDialog:c,...d}=a,e=aM(a3,c);return(0,g.jsx)(s.sG.button,{type:"button",...d,ref:b,onClick:l(a.onClick,()=>e.onOpenChange(!1))})});function a5(a){return a?"open":"closed"}a4.displayName=a3;var a6="DialogTitleWarning",[a7,a8]=function(a,b){let c=h.createContext(b),d=a=>{let{children:b,...d}=a,e=h.useMemo(()=>d,Object.values(d));return(0,g.jsx)(c.Provider,{value:e,children:b})};return d.displayName=a+"Provider",[d,function(d){let e=h.useContext(c);if(e)return e;if(void 0!==b)return b;throw Error(`\`${d}\` must be used within \`${a}\``)}]}(a6,{contentName:aY,titleName:a1,docsSlug:"dialog"}),a9=({titleId:a})=>{let b=a8(a6),c=`\`${b.contentName}\` requires a \`${b.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${b.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${b.docsSlug}`;return h.useEffect(()=>{a&&(document.getElementById(a)||console.error(c))},[c,a]),null},ba=({contentRef:a,descriptionId:b})=>{let c=a8("DialogDescriptionWarning"),d=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${c.contentName}}.`;return h.useEffect(()=>{let c=a.current?.getAttribute("aria-describedby");b&&c&&(document.getElementById(b)||console.warn(d))},[d,a,b]),null},bb=c(1860),bc=c(1743);function bd({...a}){return(0,g.jsx)(aN,{"data-slot":"dialog",...a})}function be({...a}){return(0,g.jsx)(aP,{"data-slot":"dialog-trigger",...a})}function bf({...a}){return(0,g.jsx)(aT,{"data-slot":"dialog-portal",...a})}function bg({className:a,...b}){return(0,g.jsx)(aV,{"data-slot":"dialog-overlay",className:(0,bc.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",a),...b})}function bh({className:a,children:b,showCloseButton:c=!0,...d}){return(0,g.jsxs)(bf,{"data-slot":"dialog-portal",children:[(0,g.jsx)(bg,{}),(0,g.jsxs)(aZ,{"data-slot":"dialog-content",className:(0,bc.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",a),...d,children:[b,c&&(0,g.jsxs)(a4,{"data-slot":"dialog-close",className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,g.jsx)(bb.A,{}),(0,g.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}let bi=(0,c(2688).A)("camera",[["path",{d:"M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z",key:"1tc9qg"}],["circle",{cx:"12",cy:"13",r:"3",key:"1vg3eu"}]]),bj=[{id:"g1",src:"/images/gallery/interior-main.jpg",alt:"Main seating area with cozy wooden furniture",title:"Cozy Main Seating Area",category:"interior",description:"Our warm and inviting main seating area with handcrafted wooden furniture"},{id:"g2",src:"/images/gallery/interior-corner.jpg",alt:"Corner reading nook with books and plants",title:"Reading Corner",category:"interior",description:"Perfect corner for book lovers with natural lighting and plants"},{id:"g3",src:"/images/gallery/interior-counter.jpg",alt:"Coffee counter with barista equipment",title:"Coffee Counter",category:"interior",description:"Our professional coffee counter where magic happens"},{id:"g4",src:"/images/gallery/interior-upstairs.jpg",alt:"Upstairs seating area with mountain views",title:"Upstairs Seating",category:"interior",description:"Second floor seating with beautiful mountain views"},{id:"g5",src:"/images/gallery/food-momos.jpg",alt:"Fresh steamed momos with sauce",title:"Traditional Momos",category:"food",description:"Our signature momos, handmade fresh daily"},{id:"g6",src:"/images/gallery/food-dal-bhat.jpg",alt:"Traditional dal bhat set meal",title:"Dal Bhat Set",category:"food",description:"Authentic Nepali dal bhat with fresh vegetables"},{id:"g7",src:"/images/gallery/food-pastries.jpg",alt:"Fresh baked pastries and bread",title:"Fresh Pastries",category:"food",description:"Daily baked pastries and bread made in-house"},{id:"g8",src:"/images/gallery/food-breakfast.jpg",alt:"English breakfast plate",title:"Hearty Breakfast",category:"food",description:"Start your day with our hearty breakfast options"}];var bk=c(5814),bl=c.n(bk);function bm(){let[a,b]=(0,h.useState)(null);return(0,g.jsx)("section",{className:"py-20 bg-white",children:(0,g.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,g.jsxs)(j.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.6},viewport:{once:!0},className:"text-center mb-16",children:[(0,g.jsx)("h2",{className:"font-serif text-4xl md:text-5xl font-bold text-cafe-dark-brown mb-6",children:"Gallery"}),(0,g.jsx)("p",{className:"text-xl text-cafe-brown max-w-3xl mx-auto leading-relaxed",children:"Take a visual journey through our cozy caf\xe9, delicious food, and the warm moments shared by our community."})]}),(0,g.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 mb-12",children:bj.map((a,b)=>(0,g.jsx)(j.P.div,{initial:{opacity:0,scale:.9},whileInView:{opacity:1,scale:1},transition:{duration:.5,delay:.1*b},viewport:{once:!0},className:`relative group cursor-pointer overflow-hidden rounded-lg ${0===b?"col-span-2 row-span-2":3===b?"col-span-2":""}`,children:(0,g.jsxs)(bd,{children:[(0,g.jsx)(be,{asChild:!0,children:(0,g.jsxs)("div",{className:"relative aspect-square overflow-hidden rounded-lg",children:[(0,g.jsx)("img",{src:a.src,alt:a.alt,className:"w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"}),(0,g.jsx)("div",{className:"absolute inset-0 bg-black/0 group-hover:bg-black/40 transition-colors duration-300 flex items-center justify-center",children:(0,g.jsx)("div",{className:"opacity-0 group-hover:opacity-100 transition-opacity duration-300",children:(0,g.jsx)("div",{className:"w-12 h-12 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center",children:(0,g.jsx)(bi,{className:"h-6 w-6 text-white"})})})}),(0,g.jsx)("div",{className:"absolute top-2 left-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300",children:(0,g.jsx)("span",{className:"bg-cafe-brown/80 text-cafe-cream text-xs px-2 py-1 rounded-full backdrop-blur-sm",children:a.category})})]})}),(0,g.jsx)(bh,{className:"max-w-4xl w-full p-0 border-0",children:(0,g.jsxs)("div",{className:"relative",children:[(0,g.jsx)("img",{src:a.src,alt:a.alt,className:"w-full h-auto max-h-[80vh] object-contain"}),(0,g.jsxs)("div",{className:"absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/60 to-transparent p-6",children:[(0,g.jsx)("h3",{className:"text-white font-serif text-xl font-semibold mb-2",children:a.title}),a.description&&(0,g.jsx)("p",{className:"text-white/90 text-sm",children:a.description})]})]})})]})},a.id))}),(0,g.jsx)(j.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.3},viewport:{once:!0},className:"text-center",children:(0,g.jsx)(bl(),{href:"/gallery",children:(0,g.jsxs)(k.$,{size:"lg",className:"bg-cafe-brown hover:bg-cafe-dark-brown text-cafe-cream px-8 py-3 text-lg font-semibold shadow-cafe-lg",children:[(0,g.jsx)(bi,{className:"h-5 w-5 mr-2"}),"View Full Gallery"]})})})]})})}},2789:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(5239),e=c(8088),f=c(7220),g=c(1289),h=c(6191),i=c(4823),j=c(1998),k=c(2603),l=c(4649),m=c(2781),n=c(2602),o=c(1268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(6713),u=c(3365),v=c(1454),w=c(7778),x=c(6143),y=c(9105),z=c(8171),A=c(6439),B=c(6133),C=c.n(B),D=c(893),E=c(2836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,1204)),"C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\app\\page.tsx"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,4431)),"C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,6133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,9868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,9615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=["C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\app\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},3033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3038:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"useMergedRef",{enumerable:!0,get:function(){return e}});let d=c(3210);function e(a,b){let c=(0,d.useRef)(null),e=(0,d.useRef)(null);return(0,d.useCallback)(d=>{if(null===d){let a=c.current;a&&(c.current=null,a());let b=e.current;b&&(e.current=null,b())}else a&&(c.current=f(a,d)),b&&(e.current=f(b,d))},[a,b])}function f(a,b){if("function"!=typeof a)return a.current=b,()=>{a.current=null};{let c=a(b);return"function"==typeof c?c:()=>a(null)}}("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},3166:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(2688).A)("coffee",[["path",{d:"M10 2v2",key:"7u0qdc"}],["path",{d:"M14 2v2",key:"6buw04"}],["path",{d:"M16 8a1 1 0 0 1 1 1v8a4 4 0 0 1-4 4H7a4 4 0 0 1-4-4V9a1 1 0 0 1 1-1h14a4 4 0 1 1 0 8h-1",key:"pwadti"}],["path",{d:"M6 2v2",key:"colzsn"}]])},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3406:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{IDLE_LINK_STATUS:function(){return j},PENDING_LINK_STATUS:function(){return i},mountFormInstance:function(){return s},mountLinkInstance:function(){return r},onLinkVisibilityChanged:function(){return u},onNavigationIntent:function(){return v},pingVisibleLinks:function(){return x},setLinkForCurrentNavigation:function(){return k},unmountLinkForCurrentNavigation:function(){return l},unmountPrefetchableInstance:function(){return t}}),c(3690);let d=c(9752),e=c(9154),f=c(593),g=c(3210),h=null,i={pending:!0},j={pending:!1};function k(a){(0,g.startTransition)(()=>{null==h||h.setOptimisticLinkStatus(j),null==a||a.setOptimisticLinkStatus(i),h=a})}function l(a){h===a&&(h=null)}let m="function"==typeof WeakMap?new WeakMap:new Map,n=new Set,o="function"==typeof IntersectionObserver?new IntersectionObserver(function(a){for(let b of a){let a=b.intersectionRatio>0;u(b.target,a)}},{rootMargin:"200px"}):null;function p(a,b){void 0!==m.get(a)&&t(a),m.set(a,b),null!==o&&o.observe(a)}function q(a){try{return(0,d.createPrefetchURL)(a)}catch(b){return("function"==typeof reportError?reportError:console.error)("Cannot prefetch '"+a+"' because it cannot be converted to a URL."),null}}function r(a,b,c,d,e,f){if(e){let e=q(b);if(null!==e){let b={router:c,kind:d,isVisible:!1,prefetchTask:null,prefetchHref:e.href,setOptimisticLinkStatus:f};return p(a,b),b}}return{router:c,kind:d,isVisible:!1,prefetchTask:null,prefetchHref:null,setOptimisticLinkStatus:f}}function s(a,b,c,d){let e=q(b);null!==e&&p(a,{router:c,kind:d,isVisible:!1,prefetchTask:null,prefetchHref:e.href,setOptimisticLinkStatus:null})}function t(a){let b=m.get(a);if(void 0!==b){m.delete(a),n.delete(b);let c=b.prefetchTask;null!==c&&(0,f.cancelPrefetchTask)(c)}null!==o&&o.unobserve(a)}function u(a,b){let c=m.get(a);void 0!==c&&(c.isVisible=b,b?n.add(c):n.delete(c),w(c,f.PrefetchPriority.Default))}function v(a,b){let c=m.get(a);void 0!==c&&void 0!==c&&w(c,f.PrefetchPriority.Intent)}function w(a,b){let c=a.prefetchTask;if(!a.isVisible){null!==c&&(0,f.cancelPrefetchTask)(c);return}}function x(a,b){for(let c of n){let d=c.prefetchTask;if(null!==d&&!(0,f.isPrefetchTaskDirty)(d,a,b))continue;null!==d&&(0,f.cancelPrefetchTask)(d);let g=(0,f.createCacheKey)(c.prefetchHref,a);c.prefetchTask=(0,f.schedulePrefetchTask)(g,b,c.kind===e.PrefetchKind.FULL,f.PrefetchPriority.Default,null)}}("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},3570:(a,b,c)=>{"use strict";c.d(b,{ContactSection:()=>dO});var d,e=c(687),f=c(4124),g=c(4493),h=c(7992),i=c(8340),j=c(1550),k=c(6349),l=c(3210),m=a=>a instanceof Date,n=a=>null==a,o=a=>!n(a)&&!Array.isArray(a)&&"object"==typeof a&&!m(a),p=a=>o(a)&&a.target?"checkbox"===a.target.type?a.target.checked:a.target.value:a,q=(a,b)=>a.has((a=>a.substring(0,a.search(/\.\d+(\.|$)/))||a)(b)),r="undefined"!=typeof window&&void 0!==window.HTMLElement&&"undefined"!=typeof document;function s(a){let b,c=Array.isArray(a),d="undefined"!=typeof FileList&&a instanceof FileList;if(a instanceof Date)b=new Date(a);else if(!(!(r&&(a instanceof Blob||d))&&(c||o(a))))return a;else if(b=c?[]:{},c||(a=>{let b=a.constructor&&a.constructor.prototype;return o(b)&&b.hasOwnProperty("isPrototypeOf")})(a))for(let c in a)a.hasOwnProperty(c)&&(b[c]=s(a[c]));else b=a;return b}var t=a=>/^\w*$/.test(a),u=a=>void 0===a,v=a=>Array.isArray(a)?a.filter(Boolean):[],w=a=>v(a.replace(/["|']|\]/g,"").split(/\.|\[/)),x=(a,b,c)=>{if(!b||!o(a))return c;let d=(t(b)?[b]:w(b)).reduce((a,b)=>n(a)?a:a[b],a);return u(d)||d===a?u(a[b])?c:a[b]:d},y=(a,b,c)=>{let d=-1,e=t(b)?[b]:w(b),f=e.length,g=f-1;for(;++d<f;){let b=e[d],f=c;if(d!==g){let c=a[b];f=o(c)||Array.isArray(c)?c:isNaN(+e[d+1])?{}:[]}if("__proto__"===b||"constructor"===b||"prototype"===b)return;a[b]=f,a=a[b]}};let z={BLUR:"blur",FOCUS_OUT:"focusout",CHANGE:"change"},A={onBlur:"onBlur",onChange:"onChange",onSubmit:"onSubmit",onTouched:"onTouched",all:"all"},B={max:"max",min:"min",maxLength:"maxLength",minLength:"minLength",pattern:"pattern",required:"required",validate:"validate"},C=l.createContext(null);C.displayName="HookFormContext";let D=()=>l.useContext(C);var E=(a,b,c,d=!0)=>{let e={defaultValues:b._defaultValues};for(let f in a)Object.defineProperty(e,f,{get:()=>(b._proxyFormState[f]!==A.all&&(b._proxyFormState[f]=!d||A.all),c&&(c[f]=!0),a[f])});return e};let F="undefined"!=typeof window?l.useLayoutEffect:l.useEffect;function G(a){let b=D(),{control:c=b.control,disabled:d,name:e,exact:f}=a||{},[g,h]=l.useState(c._formState),i=l.useRef({isDirty:!1,isLoading:!1,dirtyFields:!1,touchedFields:!1,validatingFields:!1,isValidating:!1,isValid:!1,errors:!1});return F(()=>c._subscribe({name:e,formState:i.current,exact:f,callback:a=>{d||h({...c._formState,...a})}}),[e,d,f]),l.useEffect(()=>{i.current.isValid&&c._setValid(!0)},[c]),l.useMemo(()=>E(g,c,i.current,!1),[g,c])}var H=(a,b,c,d,e)=>"string"==typeof a?(d&&b.watch.add(a),x(c,a,e)):Array.isArray(a)?a.map(a=>(d&&b.watch.add(a),x(c,a))):(d&&(b.watchAll=!0),c);let I=a=>a.render(function(a){let b=D(),{name:c,disabled:d,control:e=b.control,shouldUnregister:f}=a,g=q(e._names.array,c),h=function(a){let b=D(),{control:c=b.control,name:d,defaultValue:e,disabled:f,exact:g}=a||{},h=l.useRef(e),[i,j]=l.useState(c._getWatch(d,h.current));return F(()=>c._subscribe({name:d,formState:{values:!0},exact:g,callback:a=>!f&&j(H(d,c._names,a.values||c._formValues,!1,h.current))}),[d,c,f,g]),l.useEffect(()=>c._removeUnmounted()),i}({control:e,name:c,defaultValue:x(e._formValues,c,x(e._defaultValues,c,a.defaultValue)),exact:!0}),i=G({control:e,name:c,exact:!0}),j=l.useRef(a),k=l.useRef(e.register(c,{...a.rules,value:h,..."boolean"==typeof a.disabled?{disabled:a.disabled}:{}})),m=l.useMemo(()=>Object.defineProperties({},{invalid:{enumerable:!0,get:()=>!!x(i.errors,c)},isDirty:{enumerable:!0,get:()=>!!x(i.dirtyFields,c)},isTouched:{enumerable:!0,get:()=>!!x(i.touchedFields,c)},isValidating:{enumerable:!0,get:()=>!!x(i.validatingFields,c)},error:{enumerable:!0,get:()=>x(i.errors,c)}}),[i,c]),n=l.useCallback(a=>k.current.onChange({target:{value:p(a),name:c},type:z.CHANGE}),[c]),o=l.useCallback(()=>k.current.onBlur({target:{value:x(e._formValues,c),name:c},type:z.BLUR}),[c,e._formValues]),r=l.useCallback(a=>{let b=x(e._fields,c);b&&a&&(b._f.ref={focus:()=>a.focus&&a.focus(),select:()=>a.select&&a.select(),setCustomValidity:b=>a.setCustomValidity(b),reportValidity:()=>a.reportValidity()})},[e._fields,c]),t=l.useMemo(()=>({name:c,value:h,..."boolean"==typeof d||i.disabled?{disabled:i.disabled||d}:{},onChange:n,onBlur:o,ref:r}),[c,d,i.disabled,n,o,r,h]);return l.useEffect(()=>{let a=e._options.shouldUnregister||f;e.register(c,{...j.current.rules,..."boolean"==typeof j.current.disabled?{disabled:j.current.disabled}:{}});let b=(a,b)=>{let c=x(e._fields,a);c&&c._f&&(c._f.mount=b)};if(b(c,!0),a){let a=s(x(e._options.defaultValues,c));y(e._defaultValues,c,a),u(x(e._formValues,c))&&y(e._formValues,c,a)}return g||e.register(c),()=>{(g?a&&!e._state.action:a)?e.unregister(c):b(c,!1)}},[c,e,g,f]),l.useEffect(()=>{e._setDisabledField({disabled:d,name:c})},[d,c,e]),l.useMemo(()=>({field:t,formState:i,fieldState:m}),[t,i,m])}(a));var J=(a,b,c,d,e)=>b?{...c[a],types:{...c[a]&&c[a].types?c[a].types:{},[d]:e||!0}}:{},K=a=>Array.isArray(a)?a:[a],L=()=>{let a=[];return{get observers(){return a},next:b=>{for(let c of a)c.next&&c.next(b)},subscribe:b=>(a.push(b),{unsubscribe:()=>{a=a.filter(a=>a!==b)}}),unsubscribe:()=>{a=[]}}},M=a=>n(a)||"object"!=typeof a;function N(a,b,c=new WeakSet){if(M(a)||M(b))return a===b;if(m(a)&&m(b))return a.getTime()===b.getTime();let d=Object.keys(a),e=Object.keys(b);if(d.length!==e.length)return!1;if(c.has(a)||c.has(b))return!0;for(let f of(c.add(a),c.add(b),d)){let d=a[f];if(!e.includes(f))return!1;if("ref"!==f){let a=b[f];if(m(d)&&m(a)||o(d)&&o(a)||Array.isArray(d)&&Array.isArray(a)?!N(d,a,c):d!==a)return!1}}return!0}var O=a=>o(a)&&!Object.keys(a).length,P=a=>"function"==typeof a,Q=a=>{if(!r)return!1;let b=a?a.ownerDocument:0;return a instanceof(b&&b.defaultView?b.defaultView.HTMLElement:HTMLElement)},R=a=>Q(a)&&a.isConnected;function S(a,b){let c=Array.isArray(b)?b:t(b)?[b]:w(b),d=1===c.length?a:function(a,b){let c=b.slice(0,-1).length,d=0;for(;d<c;)a=u(a)?d++:a[b[d++]];return a}(a,c),e=c.length-1,f=c[e];return d&&delete d[f],0!==e&&(o(d)&&O(d)||Array.isArray(d)&&function(a){for(let b in a)if(a.hasOwnProperty(b)&&!u(a[b]))return!1;return!0}(d))&&S(a,c.slice(0,-1)),a}var T=a=>{for(let b in a)if(P(a[b]))return!0;return!1};function U(a,b={}){let c=Array.isArray(a);if(o(a)||c)for(let c in a)Array.isArray(a[c])||o(a[c])&&!T(a[c])?(b[c]=Array.isArray(a[c])?[]:{},U(a[c],b[c])):n(a[c])||(b[c]=!0);return b}var V=(a,b)=>(function a(b,c,d){let e=Array.isArray(b);if(o(b)||e)for(let e in b)Array.isArray(b[e])||o(b[e])&&!T(b[e])?u(c)||M(d[e])?d[e]=Array.isArray(b[e])?U(b[e],[]):{...U(b[e])}:a(b[e],n(c)?{}:c[e],d[e]):d[e]=!N(b[e],c[e]);return d})(a,b,U(b));let W={value:!1,isValid:!1},X={value:!0,isValid:!0};var Y=a=>{if(Array.isArray(a)){if(a.length>1){let b=a.filter(a=>a&&a.checked&&!a.disabled).map(a=>a.value);return{value:b,isValid:!!b.length}}return a[0].checked&&!a[0].disabled?a[0].attributes&&!u(a[0].attributes.value)?u(a[0].value)||""===a[0].value?X:{value:a[0].value,isValid:!0}:X:W}return W},Z=(a,{valueAsNumber:b,valueAsDate:c,setValueAs:d})=>u(a)?a:b?""===a?NaN:a?+a:a:c&&"string"==typeof a?new Date(a):d?d(a):a;let $={isValid:!1,value:null};var _=a=>Array.isArray(a)?a.reduce((a,b)=>b&&b.checked&&!b.disabled?{isValid:!0,value:b.value}:a,$):$;function aa(a){let b=a.ref;return"file"===b.type?b.files:"radio"===b.type?_(a.refs).value:"select-multiple"===b.type?[...b.selectedOptions].map(({value:a})=>a):"checkbox"===b.type?Y(a.refs).value:Z(u(b.value)?a.ref.value:b.value,a)}var ab=a=>u(a)?a:a instanceof RegExp?a.source:o(a)?a.value instanceof RegExp?a.value.source:a.value:a,ac=a=>({isOnSubmit:!a||a===A.onSubmit,isOnBlur:a===A.onBlur,isOnChange:a===A.onChange,isOnAll:a===A.all,isOnTouch:a===A.onTouched});let ad="AsyncFunction";var ae=a=>!!a&&!!a.validate&&!!(P(a.validate)&&a.validate.constructor.name===ad||o(a.validate)&&Object.values(a.validate).find(a=>a.constructor.name===ad)),af=(a,b,c)=>!c&&(b.watchAll||b.watch.has(a)||[...b.watch].some(b=>a.startsWith(b)&&/^\.\w+/.test(a.slice(b.length))));let ag=(a,b,c,d)=>{for(let e of c||Object.keys(a)){let c=x(a,e);if(c){let{_f:a,...f}=c;if(a){if(a.refs&&a.refs[0]&&b(a.refs[0],e)&&!d)return!0;else if(a.ref&&b(a.ref,a.name)&&!d)return!0;else if(ag(f,b))break}else if(o(f)&&ag(f,b))break}}};function ah(a,b,c){let d=x(a,c);if(d||t(c))return{error:d,name:c};let e=c.split(".");for(;e.length;){let d=e.join("."),f=x(b,d),g=x(a,d);if(f&&!Array.isArray(f)&&c!==d)break;if(g&&g.type)return{name:d,error:g};if(g&&g.root&&g.root.type)return{name:`${d}.root`,error:g.root};e.pop()}return{name:c}}var ai=(a,b,c)=>{let d=K(x(a,c));return y(d,"root",b[c]),y(a,c,d),a},aj=a=>"string"==typeof a;function ak(a,b,c="validate"){if(aj(a)||Array.isArray(a)&&a.every(aj)||"boolean"==typeof a&&!a)return{type:c,message:aj(a)?a:"",ref:b}}var al=a=>!o(a)||a instanceof RegExp?{value:a,message:""}:a,am=async(a,b,c,d,e,f)=>{let{ref:g,refs:h,required:i,maxLength:j,minLength:k,min:l,max:m,pattern:p,validate:q,name:r,valueAsNumber:s,mount:t}=a._f,v=x(c,r);if(!t||b.has(r))return{};let w=h?h[0]:g,y=a=>{e&&w.reportValidity&&(w.setCustomValidity("boolean"==typeof a?"":a||""),w.reportValidity())},z={},A="radio"===g.type,C="checkbox"===g.type,D=(s||"file"===g.type)&&u(g.value)&&u(v)||Q(g)&&""===g.value||""===v||Array.isArray(v)&&!v.length,E=J.bind(null,r,d,z),F=(a,b,c,d=B.maxLength,e=B.minLength)=>{let f=a?b:c;z[r]={type:a?d:e,message:f,ref:g,...E(a?d:e,f)}};if(f?!Array.isArray(v)||!v.length:i&&(!(A||C)&&(D||n(v))||"boolean"==typeof v&&!v||C&&!Y(h).isValid||A&&!_(h).isValid)){let{value:a,message:b}=aj(i)?{value:!!i,message:i}:al(i);if(a&&(z[r]={type:B.required,message:b,ref:w,...E(B.required,b)},!d))return y(b),z}if(!D&&(!n(l)||!n(m))){let a,b,c=al(m),e=al(l);if(n(v)||isNaN(v)){let d=g.valueAsDate||new Date(v),f=a=>new Date(new Date().toDateString()+" "+a),h="time"==g.type,i="week"==g.type;"string"==typeof c.value&&v&&(a=h?f(v)>f(c.value):i?v>c.value:d>new Date(c.value)),"string"==typeof e.value&&v&&(b=h?f(v)<f(e.value):i?v<e.value:d<new Date(e.value))}else{let d=g.valueAsNumber||(v?+v:v);n(c.value)||(a=d>c.value),n(e.value)||(b=d<e.value)}if((a||b)&&(F(!!a,c.message,e.message,B.max,B.min),!d))return y(z[r].message),z}if((j||k)&&!D&&("string"==typeof v||f&&Array.isArray(v))){let a=al(j),b=al(k),c=!n(a.value)&&v.length>+a.value,e=!n(b.value)&&v.length<+b.value;if((c||e)&&(F(c,a.message,b.message),!d))return y(z[r].message),z}if(p&&!D&&"string"==typeof v){let{value:a,message:b}=al(p);if(a instanceof RegExp&&!v.match(a)&&(z[r]={type:B.pattern,message:b,ref:g,...E(B.pattern,b)},!d))return y(b),z}if(q){if(P(q)){let a=ak(await q(v,c),w);if(a&&(z[r]={...a,...E(B.validate,a.message)},!d))return y(a.message),z}else if(o(q)){let a={};for(let b in q){if(!O(a)&&!d)break;let e=ak(await q[b](v,c),w,b);e&&(a={...e,...E(b,e.message)},y(e.message),d&&(z[r]=a))}if(!O(a)&&(z[r]={ref:w,...a},!d))return z}}return y(!0),z};let an={mode:A.onSubmit,reValidateMode:A.onChange,shouldFocusError:!0},ao=(a,b,c)=>{if(a&&"reportValidity"in a){let d=x(c,b);a.setCustomValidity(d&&d.message||""),a.reportValidity()}},ap=(a,b)=>{for(let c in b.fields){let d=b.fields[c];d&&d.ref&&"reportValidity"in d.ref?ao(d.ref,c,a):d&&d.refs&&d.refs.forEach(b=>ao(b,c,a))}},aq=(a,b)=>{b.shouldUseNativeValidation&&ap(a,b);let c={};for(let d in a){let e=x(b.fields,d),f=Object.assign(a[d]||{},{ref:e&&e.ref});if(ar(b.names||Object.keys(a),d)){let a=Object.assign({},x(c,d));y(a,"root",f),y(c,d,a)}else y(c,d,f)}return c},ar=(a,b)=>{let c=as(b);return a.some(a=>as(a).match(`^${c}\\.\\d+`))};function as(a){return a.replace(/\]|\[/g,"")}function at(a,b,c){function d(c,d){var e;for(let f in Object.defineProperty(c,"_zod",{value:c._zod??{},enumerable:!1}),(e=c._zod).traits??(e.traits=new Set),c._zod.traits.add(a),b(c,d),g.prototype)f in c||Object.defineProperty(c,f,{value:g.prototype[f].bind(c)});c._zod.constr=g,c._zod.def=d}let e=c?.Parent??Object;class f extends e{}function g(a){var b;let e=c?.Parent?new f:this;for(let c of(d(e,a),(b=e._zod).deferred??(b.deferred=[]),e._zod.deferred))c();return e}return Object.defineProperty(f,"name",{value:a}),Object.defineProperty(g,"init",{value:d}),Object.defineProperty(g,Symbol.hasInstance,{value:b=>!!c?.Parent&&b instanceof c.Parent||b?._zod?.traits?.has(a)}),Object.defineProperty(g,"name",{value:a}),g}Object.freeze({status:"aborted"}),Symbol("zod_brand");class au extends Error{constructor(){super("Encountered Promise during synchronous parse. Use .parseAsync() instead.")}}let av={};function aw(a){return a&&Object.assign(av,a),av}function ax(a,b){return"bigint"==typeof b?b.toString():b}function ay(a){return{get value(){{let b=a();return Object.defineProperty(this,"value",{value:b}),b}}}}function az(a){let b=+!!a.startsWith("^"),c=a.endsWith("$")?a.length-1:a.length;return a.slice(b,c)}function aA(a,b,c){Object.defineProperty(a,b,{get(){{let d=c();return a[b]=d,d}},set(c){Object.defineProperty(a,b,{value:c})},configurable:!0})}function aB(a,b,c){Object.defineProperty(a,b,{value:c,writable:!0,enumerable:!0,configurable:!0})}function aC(a){return JSON.stringify(a)}let aD=Error.captureStackTrace?Error.captureStackTrace:(...a)=>{};function aE(a){return"object"==typeof a&&null!==a&&!Array.isArray(a)}let aF=ay(()=>{if("undefined"!=typeof navigator&&navigator?.userAgent?.includes("Cloudflare"))return!1;try{return Function(""),!0}catch(a){return!1}});function aG(a){if(!1===aE(a))return!1;let b=a.constructor;if(void 0===b)return!0;let c=b.prototype;return!1!==aE(c)&&!1!==Object.prototype.hasOwnProperty.call(c,"isPrototypeOf")}let aH=new Set(["string","number","symbol"]);function aI(a){return a.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}function aJ(a,b,c){let d=new a._zod.constr(b??a._zod.def);return(!b||c?.parent)&&(d._zod.parent=a),d}function aK(a){if(!a)return{};if("string"==typeof a)return{error:()=>a};if(a?.message!==void 0){if(a?.error!==void 0)throw Error("Cannot specify both `message` and `error` params");a.error=a.message}return(delete a.message,"string"==typeof a.error)?{...a,error:()=>a.error}:a}function aL(a,b=0){for(let c=b;c<a.issues.length;c++)if(a.issues[c]?.continue!==!0)return!0;return!1}function aM(a,b){return b.map(b=>(b.path??(b.path=[]),b.path.unshift(a),b))}function aN(a){return"string"==typeof a?a:a?.message}function aO(a,b,c){let d={...a,path:a.path??[]};return a.message||(d.message=aN(a.inst?._zod.def?.error?.(a))??aN(b?.error?.(a))??aN(c.customError?.(a))??aN(c.localeError?.(a))??"Invalid input"),delete d.inst,delete d.continue,b?.reportInput||delete d.input,d}function aP(a){return Array.isArray(a)?"array":"string"==typeof a?"string":"unknown"}function aQ(...a){let[b,c,d]=a;return"string"==typeof b?{message:b,code:"custom",input:c,inst:d}:{...b}}Number.MIN_SAFE_INTEGER,Number.MAX_SAFE_INTEGER,Number.MAX_VALUE,Number.MAX_VALUE;let aR=(a,b)=>{a.name="$ZodError",Object.defineProperty(a,"_zod",{value:a._zod,enumerable:!1}),Object.defineProperty(a,"issues",{value:b,enumerable:!1}),Object.defineProperty(a,"message",{get:()=>JSON.stringify(b,ax,2),enumerable:!0}),Object.defineProperty(a,"toString",{value:()=>a.message,enumerable:!1})},aS=at("$ZodError",aR),aT=at("$ZodError",aR,{Parent:Error}),aU=a=>(b,c,d,e)=>{let f=d?Object.assign(d,{async:!1}):{async:!1},g=b._zod.run({value:c,issues:[]},f);if(g instanceof Promise)throw new au;if(g.issues.length){let b=new(e?.Err??a)(g.issues.map(a=>aO(a,f,aw())));throw aD(b,e?.callee),b}return g.value},aV=aU(aT),aW=a=>async(b,c,d,e)=>{let f=d?Object.assign(d,{async:!0}):{async:!0},g=b._zod.run({value:c,issues:[]},f);if(g instanceof Promise&&(g=await g),g.issues.length){let b=new(e?.Err??a)(g.issues.map(a=>aO(a,f,aw())));throw aD(b,e?.callee),b}return g.value},aX=aW(aT),aY=a=>(b,c,d)=>{let e=d?{...d,async:!1}:{async:!1},f=b._zod.run({value:c,issues:[]},e);if(f instanceof Promise)throw new au;return f.issues.length?{success:!1,error:new(a??aS)(f.issues.map(a=>aO(a,e,aw())))}:{success:!0,data:f.value}},aZ=aY(aT),a$=a=>async(b,c,d)=>{let e=d?Object.assign(d,{async:!0}):{async:!0},f=b._zod.run({value:c,issues:[]},e);return f instanceof Promise&&(f=await f),f.issues.length?{success:!1,error:new a(f.issues.map(a=>aO(a,e,aw())))}:{success:!0,data:f.value}},a_=a$(aT);function a0(a,b){try{var c=a()}catch(a){return b(a)}return c&&c.then?c.then(void 0,b):c}let a1=/^[cC][^\s-]{8,}$/,a2=/^[0-9a-z]+$/,a3=/^[0-9A-HJKMNP-TV-Za-hjkmnp-tv-z]{26}$/,a4=/^[0-9a-vA-V]{20}$/,a5=/^[A-Za-z0-9]{27}$/,a6=/^[a-zA-Z0-9_-]{21}$/,a7=/^P(?:(\d+W)|(?!.*W)(?=\d|T\d)(\d+Y)?(\d+M)?(\d+D)?(T(?=\d)(\d+H)?(\d+M)?(\d+([.,]\d+)?S)?)?)$/,a8=/^([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12})$/,a9=a=>a?RegExp(`^([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-${a}[0-9a-fA-F]{3}-[89abAB][0-9a-fA-F]{3}-[0-9a-fA-F]{12})$`):/^([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[1-8][0-9a-fA-F]{3}-[89abAB][0-9a-fA-F]{3}-[0-9a-fA-F]{12}|00000000-0000-0000-0000-000000000000)$/,ba=/^(?!\.)(?!.*\.\.)([A-Za-z0-9_'+\-\.]*)[A-Za-z0-9_+-]@([A-Za-z0-9][A-Za-z0-9\-]*\.)+[A-Za-z]{2,}$/,bb=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,bc=/^(([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}|::|([0-9a-fA-F]{1,4})?::([0-9a-fA-F]{1,4}:?){0,6})$/,bd=/^((25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/([0-9]|[1-2][0-9]|3[0-2])$/,be=/^(([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}|::|([0-9a-fA-F]{1,4})?::([0-9a-fA-F]{1,4}:?){0,6})\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,bf=/^$|^(?:[0-9a-zA-Z+/]{4})*(?:(?:[0-9a-zA-Z+/]{2}==)|(?:[0-9a-zA-Z+/]{3}=))?$/,bg=/^[A-Za-z0-9_-]*$/,bh=/^([a-zA-Z0-9-]+\.)*[a-zA-Z0-9-]+$/,bi=/^\+(?:[0-9]){6,14}[0-9]$/,bj="(?:(?:\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-(?:(?:0[13578]|1[02])-(?:0[1-9]|[12]\\d|3[01])|(?:0[469]|11)-(?:0[1-9]|[12]\\d|30)|(?:02)-(?:0[1-9]|1\\d|2[0-8])))",bk=RegExp(`^${bj}$`);function bl(a){let b="(?:[01]\\d|2[0-3]):[0-5]\\d";return"number"==typeof a.precision?-1===a.precision?`${b}`:0===a.precision?`${b}:[0-5]\\d`:`${b}:[0-5]\\d\\.\\d{${a.precision}}`:`${b}(?::[0-5]\\d(?:\\.\\d+)?)?`}let bm=/^[^A-Z]*$/,bn=/^[^a-z]*$/,bo=at("$ZodCheck",(a,b)=>{var c;a._zod??(a._zod={}),a._zod.def=b,(c=a._zod).onattach??(c.onattach=[])}),bp=at("$ZodCheckMaxLength",(a,b)=>{var c;bo.init(a,b),(c=a._zod.def).when??(c.when=a=>{let b=a.value;return null!=b&&void 0!==b.length}),a._zod.onattach.push(a=>{let c=a._zod.bag.maximum??1/0;b.maximum<c&&(a._zod.bag.maximum=b.maximum)}),a._zod.check=c=>{let d=c.value;if(d.length<=b.maximum)return;let e=aP(d);c.issues.push({origin:e,code:"too_big",maximum:b.maximum,inclusive:!0,input:d,inst:a,continue:!b.abort})}}),bq=at("$ZodCheckMinLength",(a,b)=>{var c;bo.init(a,b),(c=a._zod.def).when??(c.when=a=>{let b=a.value;return null!=b&&void 0!==b.length}),a._zod.onattach.push(a=>{let c=a._zod.bag.minimum??-1/0;b.minimum>c&&(a._zod.bag.minimum=b.minimum)}),a._zod.check=c=>{let d=c.value;if(d.length>=b.minimum)return;let e=aP(d);c.issues.push({origin:e,code:"too_small",minimum:b.minimum,inclusive:!0,input:d,inst:a,continue:!b.abort})}}),br=at("$ZodCheckLengthEquals",(a,b)=>{var c;bo.init(a,b),(c=a._zod.def).when??(c.when=a=>{let b=a.value;return null!=b&&void 0!==b.length}),a._zod.onattach.push(a=>{let c=a._zod.bag;c.minimum=b.length,c.maximum=b.length,c.length=b.length}),a._zod.check=c=>{let d=c.value,e=d.length;if(e===b.length)return;let f=aP(d),g=e>b.length;c.issues.push({origin:f,...g?{code:"too_big",maximum:b.length}:{code:"too_small",minimum:b.length},inclusive:!0,exact:!0,input:c.value,inst:a,continue:!b.abort})}}),bs=at("$ZodCheckStringFormat",(a,b)=>{var c,d;bo.init(a,b),a._zod.onattach.push(a=>{let c=a._zod.bag;c.format=b.format,b.pattern&&(c.patterns??(c.patterns=new Set),c.patterns.add(b.pattern))}),b.pattern?(c=a._zod).check??(c.check=c=>{b.pattern.lastIndex=0,b.pattern.test(c.value)||c.issues.push({origin:"string",code:"invalid_format",format:b.format,input:c.value,...b.pattern?{pattern:b.pattern.toString()}:{},inst:a,continue:!b.abort})}):(d=a._zod).check??(d.check=()=>{})}),bt=at("$ZodCheckRegex",(a,b)=>{bs.init(a,b),a._zod.check=c=>{b.pattern.lastIndex=0,b.pattern.test(c.value)||c.issues.push({origin:"string",code:"invalid_format",format:"regex",input:c.value,pattern:b.pattern.toString(),inst:a,continue:!b.abort})}}),bu=at("$ZodCheckLowerCase",(a,b)=>{b.pattern??(b.pattern=bm),bs.init(a,b)}),bv=at("$ZodCheckUpperCase",(a,b)=>{b.pattern??(b.pattern=bn),bs.init(a,b)}),bw=at("$ZodCheckIncludes",(a,b)=>{bo.init(a,b);let c=aI(b.includes),d=new RegExp("number"==typeof b.position?`^.{${b.position}}${c}`:c);b.pattern=d,a._zod.onattach.push(a=>{let b=a._zod.bag;b.patterns??(b.patterns=new Set),b.patterns.add(d)}),a._zod.check=c=>{c.value.includes(b.includes,b.position)||c.issues.push({origin:"string",code:"invalid_format",format:"includes",includes:b.includes,input:c.value,inst:a,continue:!b.abort})}}),bx=at("$ZodCheckStartsWith",(a,b)=>{bo.init(a,b);let c=RegExp(`^${aI(b.prefix)}.*`);b.pattern??(b.pattern=c),a._zod.onattach.push(a=>{let b=a._zod.bag;b.patterns??(b.patterns=new Set),b.patterns.add(c)}),a._zod.check=c=>{c.value.startsWith(b.prefix)||c.issues.push({origin:"string",code:"invalid_format",format:"starts_with",prefix:b.prefix,input:c.value,inst:a,continue:!b.abort})}}),by=at("$ZodCheckEndsWith",(a,b)=>{bo.init(a,b);let c=RegExp(`.*${aI(b.suffix)}$`);b.pattern??(b.pattern=c),a._zod.onattach.push(a=>{let b=a._zod.bag;b.patterns??(b.patterns=new Set),b.patterns.add(c)}),a._zod.check=c=>{c.value.endsWith(b.suffix)||c.issues.push({origin:"string",code:"invalid_format",format:"ends_with",suffix:b.suffix,input:c.value,inst:a,continue:!b.abort})}}),bz=at("$ZodCheckOverwrite",(a,b)=>{bo.init(a,b),a._zod.check=a=>{a.value=b.tx(a.value)}});class bA{constructor(a=[]){this.content=[],this.indent=0,this&&(this.args=a)}indented(a){this.indent+=1,a(this),this.indent-=1}write(a){if("function"==typeof a){a(this,{execution:"sync"}),a(this,{execution:"async"});return}let b=a.split("\n").filter(a=>a),c=Math.min(...b.map(a=>a.length-a.trimStart().length));for(let a of b.map(a=>a.slice(c)).map(a=>" ".repeat(2*this.indent)+a))this.content.push(a)}compile(){return Function(...this?.args,[...(this?.content??[""]).map(a=>`  ${a}`)].join("\n"))}}let bB={major:4,minor:0,patch:5},bC=at("$ZodType",(a,b)=>{var c;a??(a={}),a._zod.def=b,a._zod.bag=a._zod.bag||{},a._zod.version=bB;let d=[...a._zod.def.checks??[]];for(let b of(a._zod.traits.has("$ZodCheck")&&d.unshift(a),d))for(let c of b._zod.onattach)c(a);if(0===d.length)(c=a._zod).deferred??(c.deferred=[]),a._zod.deferred?.push(()=>{a._zod.run=a._zod.parse});else{let b=(a,b,c)=>{let d,e=aL(a);for(let f of b){if(f._zod.def.when){if(!f._zod.def.when(a))continue}else if(e)continue;let b=a.issues.length,g=f._zod.check(a);if(g instanceof Promise&&c?.async===!1)throw new au;if(d||g instanceof Promise)d=(d??Promise.resolve()).then(async()=>{await g,a.issues.length!==b&&(e||(e=aL(a,b)))});else{if(a.issues.length===b)continue;e||(e=aL(a,b))}}return d?d.then(()=>a):a};a._zod.run=(c,e)=>{let f=a._zod.parse(c,e);if(f instanceof Promise){if(!1===e.async)throw new au;return f.then(a=>b(a,d,e))}return b(f,d,e)}}a["~standard"]={validate:b=>{try{let c=aZ(a,b);return c.success?{value:c.data}:{issues:c.error?.issues}}catch(c){return a_(a,b).then(a=>a.success?{value:a.data}:{issues:a.error?.issues})}},vendor:"zod",version:1}}),bD=at("$ZodString",(a,b)=>{bC.init(a,b),a._zod.pattern=[...a?._zod.bag?.patterns??[]].pop()??(a=>{let b=a?`[\\s\\S]{${a?.minimum??0},${a?.maximum??""}}`:"[\\s\\S]*";return RegExp(`^${b}$`)})(a._zod.bag),a._zod.parse=(c,d)=>{if(b.coerce)try{c.value=String(c.value)}catch(a){}return"string"==typeof c.value||c.issues.push({expected:"string",code:"invalid_type",input:c.value,inst:a}),c}}),bE=at("$ZodStringFormat",(a,b)=>{bs.init(a,b),bD.init(a,b)}),bF=at("$ZodGUID",(a,b)=>{b.pattern??(b.pattern=a8),bE.init(a,b)}),bG=at("$ZodUUID",(a,b)=>{if(b.version){let a={v1:1,v2:2,v3:3,v4:4,v5:5,v6:6,v7:7,v8:8}[b.version];if(void 0===a)throw Error(`Invalid UUID version: "${b.version}"`);b.pattern??(b.pattern=a9(a))}else b.pattern??(b.pattern=a9());bE.init(a,b)}),bH=at("$ZodEmail",(a,b)=>{b.pattern??(b.pattern=ba),bE.init(a,b)}),bI=at("$ZodURL",(a,b)=>{bE.init(a,b),a._zod.check=c=>{try{let d=c.value,e=new URL(d),f=e.href;b.hostname&&(b.hostname.lastIndex=0,b.hostname.test(e.hostname)||c.issues.push({code:"invalid_format",format:"url",note:"Invalid hostname",pattern:bh.source,input:c.value,inst:a,continue:!b.abort})),b.protocol&&(b.protocol.lastIndex=0,b.protocol.test(e.protocol.endsWith(":")?e.protocol.slice(0,-1):e.protocol)||c.issues.push({code:"invalid_format",format:"url",note:"Invalid protocol",pattern:b.protocol.source,input:c.value,inst:a,continue:!b.abort})),!d.endsWith("/")&&f.endsWith("/")?c.value=f.slice(0,-1):c.value=f;return}catch(d){c.issues.push({code:"invalid_format",format:"url",input:c.value,inst:a,continue:!b.abort})}}}),bJ=at("$ZodEmoji",(a,b)=>{b.pattern??(b.pattern=RegExp("^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$","u")),bE.init(a,b)}),bK=at("$ZodNanoID",(a,b)=>{b.pattern??(b.pattern=a6),bE.init(a,b)}),bL=at("$ZodCUID",(a,b)=>{b.pattern??(b.pattern=a1),bE.init(a,b)}),bM=at("$ZodCUID2",(a,b)=>{b.pattern??(b.pattern=a2),bE.init(a,b)}),bN=at("$ZodULID",(a,b)=>{b.pattern??(b.pattern=a3),bE.init(a,b)}),bO=at("$ZodXID",(a,b)=>{b.pattern??(b.pattern=a4),bE.init(a,b)}),bP=at("$ZodKSUID",(a,b)=>{b.pattern??(b.pattern=a5),bE.init(a,b)}),bQ=at("$ZodISODateTime",(a,b)=>{b.pattern??(b.pattern=function(a){let b=bl({precision:a.precision}),c=["Z"];a.local&&c.push(""),a.offset&&c.push("([+-]\\d{2}:\\d{2})");let d=`${b}(?:${c.join("|")})`;return RegExp(`^${bj}T(?:${d})$`)}(b)),bE.init(a,b)}),bR=at("$ZodISODate",(a,b)=>{b.pattern??(b.pattern=bk),bE.init(a,b)}),bS=at("$ZodISOTime",(a,b)=>{b.pattern??(b.pattern=RegExp(`^${bl(b)}$`)),bE.init(a,b)}),bT=at("$ZodISODuration",(a,b)=>{b.pattern??(b.pattern=a7),bE.init(a,b)}),bU=at("$ZodIPv4",(a,b)=>{b.pattern??(b.pattern=bb),bE.init(a,b),a._zod.onattach.push(a=>{a._zod.bag.format="ipv4"})}),bV=at("$ZodIPv6",(a,b)=>{b.pattern??(b.pattern=bc),bE.init(a,b),a._zod.onattach.push(a=>{a._zod.bag.format="ipv6"}),a._zod.check=c=>{try{new URL(`http://[${c.value}]`)}catch{c.issues.push({code:"invalid_format",format:"ipv6",input:c.value,inst:a,continue:!b.abort})}}}),bW=at("$ZodCIDRv4",(a,b)=>{b.pattern??(b.pattern=bd),bE.init(a,b)}),bX=at("$ZodCIDRv6",(a,b)=>{b.pattern??(b.pattern=be),bE.init(a,b),a._zod.check=c=>{let[d,e]=c.value.split("/");try{if(!e)throw Error();let a=Number(e);if(`${a}`!==e||a<0||a>128)throw Error();new URL(`http://[${d}]`)}catch{c.issues.push({code:"invalid_format",format:"cidrv6",input:c.value,inst:a,continue:!b.abort})}}});function bY(a){if(""===a)return!0;if(a.length%4!=0)return!1;try{return atob(a),!0}catch{return!1}}let bZ=at("$ZodBase64",(a,b)=>{b.pattern??(b.pattern=bf),bE.init(a,b),a._zod.onattach.push(a=>{a._zod.bag.contentEncoding="base64"}),a._zod.check=c=>{bY(c.value)||c.issues.push({code:"invalid_format",format:"base64",input:c.value,inst:a,continue:!b.abort})}}),b$=at("$ZodBase64URL",(a,b)=>{b.pattern??(b.pattern=bg),bE.init(a,b),a._zod.onattach.push(a=>{a._zod.bag.contentEncoding="base64url"}),a._zod.check=c=>{!function(a){if(!bg.test(a))return!1;let b=a.replace(/[-_]/g,a=>"-"===a?"+":"/");return bY(b.padEnd(4*Math.ceil(b.length/4),"="))}(c.value)&&c.issues.push({code:"invalid_format",format:"base64url",input:c.value,inst:a,continue:!b.abort})}}),b_=at("$ZodE164",(a,b)=>{b.pattern??(b.pattern=bi),bE.init(a,b)}),b0=at("$ZodJWT",(a,b)=>{bE.init(a,b),a._zod.check=c=>{!function(a,b=null){try{let c=a.split(".");if(3!==c.length)return!1;let[d]=c;if(!d)return!1;let e=JSON.parse(atob(d));if("typ"in e&&e?.typ!=="JWT"||!e.alg||b&&(!("alg"in e)||e.alg!==b))return!1;return!0}catch{return!1}}(c.value,b.alg)&&c.issues.push({code:"invalid_format",format:"jwt",input:c.value,inst:a,continue:!b.abort})}}),b1=at("$ZodUnknown",(a,b)=>{bC.init(a,b),a._zod.parse=a=>a}),b2=at("$ZodNever",(a,b)=>{bC.init(a,b),a._zod.parse=(b,c)=>(b.issues.push({expected:"never",code:"invalid_type",input:b.value,inst:a}),b)});function b3(a,b,c){a.issues.length&&b.issues.push(...aM(c,a.issues)),b.value[c]=a.value}let b4=at("$ZodArray",(a,b)=>{bC.init(a,b),a._zod.parse=(c,d)=>{let e=c.value;if(!Array.isArray(e))return c.issues.push({expected:"array",code:"invalid_type",input:e,inst:a}),c;c.value=Array(e.length);let f=[];for(let a=0;a<e.length;a++){let g=e[a],h=b.element._zod.run({value:g,issues:[]},d);h instanceof Promise?f.push(h.then(b=>b3(b,c,a))):b3(h,c,a)}return f.length?Promise.all(f).then(()=>c):c}});function b5(a,b,c){a.issues.length&&b.issues.push(...aM(c,a.issues)),b.value[c]=a.value}function b6(a,b,c,d){a.issues.length?void 0===d[c]?c in d?b.value[c]=void 0:b.value[c]=a.value:b.issues.push(...aM(c,a.issues)):void 0===a.value?c in d&&(b.value[c]=void 0):b.value[c]=a.value}let b7=at("$ZodObject",(a,b)=>{let c,d;bC.init(a,b);let e=ay(()=>{let a=Object.keys(b.shape);for(let c of a)if(!(b.shape[c]instanceof bC))throw Error(`Invalid element at key "${c}": expected a Zod schema`);let c=function(a){return Object.keys(a).filter(b=>"optional"===a[b]._zod.optin&&"optional"===a[b]._zod.optout)}(b.shape);return{shape:b.shape,keys:a,keySet:new Set(a),numKeys:a.length,optionalKeys:new Set(c)}});aA(a._zod,"propValues",()=>{let a=b.shape,c={};for(let b in a){let d=a[b]._zod;if(d.values)for(let a of(c[b]??(c[b]=new Set),d.values))c[b].add(a)}return c});let f=!av.jitless,g=f&&aF.value,h=b.catchall;a._zod.parse=(i,j)=>{d??(d=e.value);let k=i.value;if(!aE(k))return i.issues.push({expected:"object",code:"invalid_type",input:k,inst:a}),i;let l=[];if(f&&g&&j?.async===!1&&!0!==j.jitless)c||(c=(a=>{let b=new bA(["shape","payload","ctx"]),c=e.value,d=a=>{let b=aC(a);return`shape[${b}]._zod.run({ value: input[${b}], issues: [] }, ctx)`};b.write("const input = payload.value;");let f=Object.create(null),g=0;for(let a of c.keys)f[a]=`key_${g++}`;for(let a of(b.write("const newResult = {}"),c.keys))if(c.optionalKeys.has(a)){let c=f[a];b.write(`const ${c} = ${d(a)};`);let e=aC(a);b.write(`
        if (${c}.issues.length) {
          if (input[${e}] === undefined) {
            if (${e} in input) {
              newResult[${e}] = undefined;
            }
          } else {
            payload.issues = payload.issues.concat(
              ${c}.issues.map((iss) => ({
                ...iss,
                path: iss.path ? [${e}, ...iss.path] : [${e}],
              }))
            );
          }
        } else if (${c}.value === undefined) {
          if (${e} in input) newResult[${e}] = undefined;
        } else {
          newResult[${e}] = ${c}.value;
        }
        `)}else{let c=f[a];b.write(`const ${c} = ${d(a)};`),b.write(`
          if (${c}.issues.length) payload.issues = payload.issues.concat(${c}.issues.map(iss => ({
            ...iss,
            path: iss.path ? [${aC(a)}, ...iss.path] : [${aC(a)}]
          })));`),b.write(`newResult[${aC(a)}] = ${c}.value`)}b.write("payload.value = newResult;"),b.write("return payload;");let h=b.compile();return(b,c)=>h(a,b,c)})(b.shape)),i=c(i,j);else{i.value={};let a=d.shape;for(let b of d.keys){let c=a[b],d=c._zod.run({value:k[b],issues:[]},j),e="optional"===c._zod.optin&&"optional"===c._zod.optout;d instanceof Promise?l.push(d.then(a=>e?b6(a,i,b,k):b5(a,i,b))):e?b6(d,i,b,k):b5(d,i,b)}}if(!h)return l.length?Promise.all(l).then(()=>i):i;let m=[],n=d.keySet,o=h._zod,p=o.def.type;for(let a of Object.keys(k)){if(n.has(a))continue;if("never"===p){m.push(a);continue}let b=o.run({value:k[a],issues:[]},j);b instanceof Promise?l.push(b.then(b=>b5(b,i,a))):b5(b,i,a)}return(m.length&&i.issues.push({code:"unrecognized_keys",keys:m,input:k,inst:a}),l.length)?Promise.all(l).then(()=>i):i}});function b8(a,b,c,d){for(let c of a)if(0===c.issues.length)return b.value=c.value,b;return b.issues.push({code:"invalid_union",input:b.value,inst:c,errors:a.map(a=>a.issues.map(a=>aO(a,d,aw())))}),b}let b9=at("$ZodUnion",(a,b)=>{bC.init(a,b),aA(a._zod,"optin",()=>b.options.some(a=>"optional"===a._zod.optin)?"optional":void 0),aA(a._zod,"optout",()=>b.options.some(a=>"optional"===a._zod.optout)?"optional":void 0),aA(a._zod,"values",()=>{if(b.options.every(a=>a._zod.values))return new Set(b.options.flatMap(a=>Array.from(a._zod.values)))}),aA(a._zod,"pattern",()=>{if(b.options.every(a=>a._zod.pattern)){let a=b.options.map(a=>a._zod.pattern);return RegExp(`^(${a.map(a=>az(a.source)).join("|")})$`)}}),a._zod.parse=(c,d)=>{let e=!1,f=[];for(let a of b.options){let b=a._zod.run({value:c.value,issues:[]},d);if(b instanceof Promise)f.push(b),e=!0;else{if(0===b.issues.length)return b;f.push(b)}}return e?Promise.all(f).then(b=>b8(b,c,a,d)):b8(f,c,a,d)}}),ca=at("$ZodIntersection",(a,b)=>{bC.init(a,b),a._zod.parse=(a,c)=>{let d=a.value,e=b.left._zod.run({value:d,issues:[]},c),f=b.right._zod.run({value:d,issues:[]},c);return e instanceof Promise||f instanceof Promise?Promise.all([e,f]).then(([b,c])=>cb(a,b,c)):cb(a,e,f)}});function cb(a,b,c){if(b.issues.length&&a.issues.push(...b.issues),c.issues.length&&a.issues.push(...c.issues),aL(a))return a;let d=function a(b,c){if(b===c||b instanceof Date&&c instanceof Date&&+b==+c)return{valid:!0,data:b};if(aG(b)&&aG(c)){let d=Object.keys(c),e=Object.keys(b).filter(a=>-1!==d.indexOf(a)),f={...b,...c};for(let d of e){let e=a(b[d],c[d]);if(!e.valid)return{valid:!1,mergeErrorPath:[d,...e.mergeErrorPath]};f[d]=e.data}return{valid:!0,data:f}}if(Array.isArray(b)&&Array.isArray(c)){if(b.length!==c.length)return{valid:!1,mergeErrorPath:[]};let d=[];for(let e=0;e<b.length;e++){let f=a(b[e],c[e]);if(!f.valid)return{valid:!1,mergeErrorPath:[e,...f.mergeErrorPath]};d.push(f.data)}return{valid:!0,data:d}}return{valid:!1,mergeErrorPath:[]}}(b.value,c.value);if(!d.valid)throw Error(`Unmergable intersection. Error path: ${JSON.stringify(d.mergeErrorPath)}`);return a.value=d.data,a}let cc=at("$ZodEnum",(a,b)=>{bC.init(a,b);let c=function(a){let b=Object.values(a).filter(a=>"number"==typeof a);return Object.entries(a).filter(([a,c])=>-1===b.indexOf(+a)).map(([a,b])=>b)}(b.entries);a._zod.values=new Set(c),a._zod.pattern=RegExp(`^(${c.filter(a=>aH.has(typeof a)).map(a=>"string"==typeof a?aI(a):a.toString()).join("|")})$`),a._zod.parse=(b,d)=>{let e=b.value;return a._zod.values.has(e)||b.issues.push({code:"invalid_value",values:c,input:e,inst:a}),b}}),cd=at("$ZodTransform",(a,b)=>{bC.init(a,b),a._zod.parse=(a,c)=>{let d=b.transform(a.value,a);if(c.async)return(d instanceof Promise?d:Promise.resolve(d)).then(b=>(a.value=b,a));if(d instanceof Promise)throw new au;return a.value=d,a}}),ce=at("$ZodOptional",(a,b)=>{bC.init(a,b),a._zod.optin="optional",a._zod.optout="optional",aA(a._zod,"values",()=>b.innerType._zod.values?new Set([...b.innerType._zod.values,void 0]):void 0),aA(a._zod,"pattern",()=>{let a=b.innerType._zod.pattern;return a?RegExp(`^(${az(a.source)})?$`):void 0}),a._zod.parse=(a,c)=>"optional"===b.innerType._zod.optin?b.innerType._zod.run(a,c):void 0===a.value?a:b.innerType._zod.run(a,c)}),cf=at("$ZodNullable",(a,b)=>{bC.init(a,b),aA(a._zod,"optin",()=>b.innerType._zod.optin),aA(a._zod,"optout",()=>b.innerType._zod.optout),aA(a._zod,"pattern",()=>{let a=b.innerType._zod.pattern;return a?RegExp(`^(${az(a.source)}|null)$`):void 0}),aA(a._zod,"values",()=>b.innerType._zod.values?new Set([...b.innerType._zod.values,null]):void 0),a._zod.parse=(a,c)=>null===a.value?a:b.innerType._zod.run(a,c)}),cg=at("$ZodDefault",(a,b)=>{bC.init(a,b),a._zod.optin="optional",aA(a._zod,"values",()=>b.innerType._zod.values),a._zod.parse=(a,c)=>{if(void 0===a.value)return a.value=b.defaultValue,a;let d=b.innerType._zod.run(a,c);return d instanceof Promise?d.then(a=>ch(a,b)):ch(d,b)}});function ch(a,b){return void 0===a.value&&(a.value=b.defaultValue),a}let ci=at("$ZodPrefault",(a,b)=>{bC.init(a,b),a._zod.optin="optional",aA(a._zod,"values",()=>b.innerType._zod.values),a._zod.parse=(a,c)=>(void 0===a.value&&(a.value=b.defaultValue),b.innerType._zod.run(a,c))}),cj=at("$ZodNonOptional",(a,b)=>{bC.init(a,b),aA(a._zod,"values",()=>{let a=b.innerType._zod.values;return a?new Set([...a].filter(a=>void 0!==a)):void 0}),a._zod.parse=(c,d)=>{let e=b.innerType._zod.run(c,d);return e instanceof Promise?e.then(b=>ck(b,a)):ck(e,a)}});function ck(a,b){return a.issues.length||void 0!==a.value||a.issues.push({code:"invalid_type",expected:"nonoptional",input:a.value,inst:b}),a}let cl=at("$ZodCatch",(a,b)=>{bC.init(a,b),a._zod.optin="optional",aA(a._zod,"optout",()=>b.innerType._zod.optout),aA(a._zod,"values",()=>b.innerType._zod.values),a._zod.parse=(a,c)=>{let d=b.innerType._zod.run(a,c);return d instanceof Promise?d.then(d=>(a.value=d.value,d.issues.length&&(a.value=b.catchValue({...a,error:{issues:d.issues.map(a=>aO(a,c,aw()))},input:a.value}),a.issues=[]),a)):(a.value=d.value,d.issues.length&&(a.value=b.catchValue({...a,error:{issues:d.issues.map(a=>aO(a,c,aw()))},input:a.value}),a.issues=[]),a)}}),cm=at("$ZodPipe",(a,b)=>{bC.init(a,b),aA(a._zod,"values",()=>b.in._zod.values),aA(a._zod,"optin",()=>b.in._zod.optin),aA(a._zod,"optout",()=>b.out._zod.optout),aA(a._zod,"propValues",()=>b.in._zod.propValues),a._zod.parse=(a,c)=>{let d=b.in._zod.run(a,c);return d instanceof Promise?d.then(a=>cn(a,b,c)):cn(d,b,c)}});function cn(a,b,c){return aL(a)?a:b.out._zod.run({value:a.value,issues:a.issues},c)}let co=at("$ZodReadonly",(a,b)=>{bC.init(a,b),aA(a._zod,"propValues",()=>b.innerType._zod.propValues),aA(a._zod,"values",()=>b.innerType._zod.values),aA(a._zod,"optin",()=>b.innerType._zod.optin),aA(a._zod,"optout",()=>b.innerType._zod.optout),a._zod.parse=(a,c)=>{let d=b.innerType._zod.run(a,c);return d instanceof Promise?d.then(cp):cp(d)}});function cp(a){return a.value=Object.freeze(a.value),a}let cq=at("$ZodCustom",(a,b)=>{bo.init(a,b),bC.init(a,b),a._zod.parse=(a,b)=>a,a._zod.check=c=>{let d=c.value,e=b.fn(d);if(e instanceof Promise)return e.then(b=>cr(b,c,d,a));cr(e,c,d,a)}});function cr(a,b,c,d){if(!a){let a={code:"custom",input:c,inst:d,path:[...d._zod.def.path??[]],continue:!d._zod.def.abort};d._zod.def.params&&(a.params=d._zod.def.params),b.issues.push(aQ(a))}}Symbol("ZodOutput"),Symbol("ZodInput");class cs{constructor(){this._map=new Map,this._idmap=new Map}add(a,...b){let c=b[0];if(this._map.set(a,c),c&&"object"==typeof c&&"id"in c){if(this._idmap.has(c.id))throw Error(`ID ${c.id} already exists in the registry`);this._idmap.set(c.id,a)}return this}clear(){return this._map=new Map,this._idmap=new Map,this}remove(a){let b=this._map.get(a);return b&&"object"==typeof b&&"id"in b&&this._idmap.delete(b.id),this._map.delete(a),this}get(a){let b=a._zod.parent;if(b){let c={...this.get(b)??{}};return delete c.id,{...c,...this._map.get(a)}}return this._map.get(a)}has(a){return this._map.has(a)}}let ct=new cs;function cu(a,b){return new a({type:"string",format:"guid",check:"string_format",abort:!1,...aK(b)})}function cv(a,b){return new bp({check:"max_length",...aK(b),maximum:a})}function cw(a,b){return new bq({check:"min_length",...aK(b),minimum:a})}function cx(a,b){return new br({check:"length_equals",...aK(b),length:a})}function cy(a){return new bz({check:"overwrite",tx:a})}let cz=at("ZodISODateTime",(a,b)=>{bQ.init(a,b),cN.init(a,b)}),cA=at("ZodISODate",(a,b)=>{bR.init(a,b),cN.init(a,b)}),cB=at("ZodISOTime",(a,b)=>{bS.init(a,b),cN.init(a,b)}),cC=at("ZodISODuration",(a,b)=>{bT.init(a,b),cN.init(a,b)}),cD=(a,b)=>{aS.init(a,b),a.name="ZodError",Object.defineProperties(a,{format:{value:b=>(function(a,b){let c=b||function(a){return a.message},d={_errors:[]},e=a=>{for(let b of a.issues)if("invalid_union"===b.code&&b.errors.length)b.errors.map(a=>e({issues:a}));else if("invalid_key"===b.code)e({issues:b.issues});else if("invalid_element"===b.code)e({issues:b.issues});else if(0===b.path.length)d._errors.push(c(b));else{let a=d,e=0;for(;e<b.path.length;){let d=b.path[e];e===b.path.length-1?(a[d]=a[d]||{_errors:[]},a[d]._errors.push(c(b))):a[d]=a[d]||{_errors:[]},a=a[d],e++}}};return e(a),d})(a,b)},flatten:{value:b=>(function(a,b=a=>a.message){let c={},d=[];for(let e of a.issues)e.path.length>0?(c[e.path[0]]=c[e.path[0]]||[],c[e.path[0]].push(b(e))):d.push(b(e));return{formErrors:d,fieldErrors:c}})(a,b)},addIssue:{value:b=>a.issues.push(b)},addIssues:{value:b=>a.issues.push(...b)},isEmpty:{get:()=>0===a.issues.length}})};at("ZodError",cD);let cE=at("ZodError",cD,{Parent:Error}),cF=aU(cE),cG=aW(cE),cH=aY(cE),cI=a$(cE),cJ=at("ZodType",(a,b)=>(bC.init(a,b),a.def=b,Object.defineProperty(a,"_def",{value:b}),a.check=(...c)=>a.clone({...b,checks:[...b.checks??[],...c.map(a=>"function"==typeof a?{_zod:{check:a,def:{check:"custom"},onattach:[]}}:a)]}),a.clone=(b,c)=>aJ(a,b,c),a.brand=()=>a,a.register=(b,c)=>(b.add(a,c),a),a.parse=(b,c)=>cF(a,b,c,{callee:a.parse}),a.safeParse=(b,c)=>cH(a,b,c),a.parseAsync=async(b,c)=>cG(a,b,c,{callee:a.parseAsync}),a.safeParseAsync=async(b,c)=>cI(a,b,c),a.spa=a.safeParseAsync,a.refine=(b,c)=>a.check(function(a,b={}){return new dq({type:"custom",check:"custom",fn:a,...aK(b)})}(b,c)),a.superRefine=b=>a.check(function(a){let b=function(a){let b=new bo({check:"custom"});return b._zod.check=a,b}(c=>(c.addIssue=a=>{"string"==typeof a?c.issues.push(aQ(a,c.value,b._zod.def)):(a.fatal&&(a.continue=!1),a.code??(a.code="custom"),a.input??(a.input=c.value),a.inst??(a.inst=b),a.continue??(a.continue=!b._zod.def.abort),c.issues.push(aQ(a)))},a(c.value,c)));return b}(b)),a.overwrite=b=>a.check(cy(b)),a.optional=()=>df(a),a.nullable=()=>dh(a),a.nullish=()=>df(dh(a)),a.nonoptional=b=>new dk({type:"nonoptional",innerType:a,...aK(b)}),a.array=()=>(function(a,b){return new c8({type:"array",element:a,...aK(void 0)})})(a),a.or=b=>new da({type:"union",options:[a,b],...aK(void 0)}),a.and=b=>new db({type:"intersection",left:a,right:b}),a.transform=b=>dn(a,new dd({type:"transform",transform:b})),a.default=b=>(function(a,b){return new di({type:"default",innerType:a,get defaultValue(){return"function"==typeof b?b():b}})})(a,b),a.prefault=b=>(function(a,b){return new dj({type:"prefault",innerType:a,get defaultValue(){return"function"==typeof b?b():b}})})(a,b),a.catch=b=>(function(a,b){return new dl({type:"catch",innerType:a,catchValue:"function"==typeof b?b:()=>b})})(a,b),a.pipe=b=>dn(a,b),a.readonly=()=>new dp({type:"readonly",innerType:a}),a.describe=b=>{let c=a.clone();return ct.add(c,{description:b}),c},Object.defineProperty(a,"description",{get:()=>ct.get(a)?.description,configurable:!0}),a.meta=(...b)=>{if(0===b.length)return ct.get(a);let c=a.clone();return ct.add(c,b[0]),c},a.isOptional=()=>a.safeParse(void 0).success,a.isNullable=()=>a.safeParse(null).success,a)),cK=at("_ZodString",(a,b)=>{bD.init(a,b),cJ.init(a,b);let c=a._zod.bag;a.format=c.format??null,a.minLength=c.minimum??null,a.maxLength=c.maximum??null,a.regex=(...b)=>a.check(function(a,b){return new bt({check:"string_format",format:"regex",...aK(b),pattern:a})}(...b)),a.includes=(...b)=>a.check(function(a,b){return new bw({check:"string_format",format:"includes",...aK(b),includes:a})}(...b)),a.startsWith=(...b)=>a.check(function(a,b){return new bx({check:"string_format",format:"starts_with",...aK(b),prefix:a})}(...b)),a.endsWith=(...b)=>a.check(function(a,b){return new by({check:"string_format",format:"ends_with",...aK(b),suffix:a})}(...b)),a.min=(...b)=>a.check(cw(...b)),a.max=(...b)=>a.check(cv(...b)),a.length=(...b)=>a.check(cx(...b)),a.nonempty=(...b)=>a.check(cw(1,...b)),a.lowercase=b=>a.check(new bu({check:"string_format",format:"lowercase",...aK(b)})),a.uppercase=b=>a.check(new bv({check:"string_format",format:"uppercase",...aK(b)})),a.trim=()=>a.check(cy(a=>a.trim())),a.normalize=(...b)=>a.check(function(a){return cy(b=>b.normalize(a))}(...b)),a.toLowerCase=()=>a.check(cy(a=>a.toLowerCase())),a.toUpperCase=()=>a.check(cy(a=>a.toUpperCase()))}),cL=at("ZodString",(a,b)=>{bD.init(a,b),cK.init(a,b),a.email=b=>a.check(new cO({type:"string",format:"email",check:"string_format",abort:!1,...aK(b)})),a.url=b=>a.check(new cR({type:"string",format:"url",check:"string_format",abort:!1,...aK(b)})),a.jwt=b=>a.check(new c4({type:"string",format:"jwt",check:"string_format",abort:!1,...aK(b)})),a.emoji=b=>a.check(new cS({type:"string",format:"emoji",check:"string_format",abort:!1,...aK(b)})),a.guid=b=>a.check(cu(cP,b)),a.uuid=b=>a.check(new cQ({type:"string",format:"uuid",check:"string_format",abort:!1,...aK(b)})),a.uuidv4=b=>a.check(new cQ({type:"string",format:"uuid",check:"string_format",abort:!1,version:"v4",...aK(b)})),a.uuidv6=b=>a.check(new cQ({type:"string",format:"uuid",check:"string_format",abort:!1,version:"v6",...aK(b)})),a.uuidv7=b=>a.check(new cQ({type:"string",format:"uuid",check:"string_format",abort:!1,version:"v7",...aK(b)})),a.nanoid=b=>a.check(new cT({type:"string",format:"nanoid",check:"string_format",abort:!1,...aK(b)})),a.guid=b=>a.check(cu(cP,b)),a.cuid=b=>a.check(new cU({type:"string",format:"cuid",check:"string_format",abort:!1,...aK(b)})),a.cuid2=b=>a.check(new cV({type:"string",format:"cuid2",check:"string_format",abort:!1,...aK(b)})),a.ulid=b=>a.check(new cW({type:"string",format:"ulid",check:"string_format",abort:!1,...aK(b)})),a.base64=b=>a.check(new c1({type:"string",format:"base64",check:"string_format",abort:!1,...aK(b)})),a.base64url=b=>a.check(new c2({type:"string",format:"base64url",check:"string_format",abort:!1,...aK(b)})),a.xid=b=>a.check(new cX({type:"string",format:"xid",check:"string_format",abort:!1,...aK(b)})),a.ksuid=b=>a.check(new cY({type:"string",format:"ksuid",check:"string_format",abort:!1,...aK(b)})),a.ipv4=b=>a.check(new cZ({type:"string",format:"ipv4",check:"string_format",abort:!1,...aK(b)})),a.ipv6=b=>a.check(new c$({type:"string",format:"ipv6",check:"string_format",abort:!1,...aK(b)})),a.cidrv4=b=>a.check(new c_({type:"string",format:"cidrv4",check:"string_format",abort:!1,...aK(b)})),a.cidrv6=b=>a.check(new c0({type:"string",format:"cidrv6",check:"string_format",abort:!1,...aK(b)})),a.e164=b=>a.check(new c3({type:"string",format:"e164",check:"string_format",abort:!1,...aK(b)})),a.datetime=b=>a.check(new cz({type:"string",format:"datetime",check:"string_format",offset:!1,local:!1,precision:null,...aK(b)})),a.date=b=>a.check(new cA({type:"string",format:"date",check:"string_format",...aK(b)})),a.time=b=>a.check(new cB({type:"string",format:"time",check:"string_format",precision:null,...aK(b)})),a.duration=b=>a.check(new cC({type:"string",format:"duration",check:"string_format",...aK(b)}))});function cM(a){return new cL({type:"string",...aK(a)})}let cN=at("ZodStringFormat",(a,b)=>{bE.init(a,b),cK.init(a,b)}),cO=at("ZodEmail",(a,b)=>{bH.init(a,b),cN.init(a,b)}),cP=at("ZodGUID",(a,b)=>{bF.init(a,b),cN.init(a,b)}),cQ=at("ZodUUID",(a,b)=>{bG.init(a,b),cN.init(a,b)}),cR=at("ZodURL",(a,b)=>{bI.init(a,b),cN.init(a,b)}),cS=at("ZodEmoji",(a,b)=>{bJ.init(a,b),cN.init(a,b)}),cT=at("ZodNanoID",(a,b)=>{bK.init(a,b),cN.init(a,b)}),cU=at("ZodCUID",(a,b)=>{bL.init(a,b),cN.init(a,b)}),cV=at("ZodCUID2",(a,b)=>{bM.init(a,b),cN.init(a,b)}),cW=at("ZodULID",(a,b)=>{bN.init(a,b),cN.init(a,b)}),cX=at("ZodXID",(a,b)=>{bO.init(a,b),cN.init(a,b)}),cY=at("ZodKSUID",(a,b)=>{bP.init(a,b),cN.init(a,b)}),cZ=at("ZodIPv4",(a,b)=>{bU.init(a,b),cN.init(a,b)}),c$=at("ZodIPv6",(a,b)=>{bV.init(a,b),cN.init(a,b)}),c_=at("ZodCIDRv4",(a,b)=>{bW.init(a,b),cN.init(a,b)}),c0=at("ZodCIDRv6",(a,b)=>{bX.init(a,b),cN.init(a,b)}),c1=at("ZodBase64",(a,b)=>{bZ.init(a,b),cN.init(a,b)}),c2=at("ZodBase64URL",(a,b)=>{b$.init(a,b),cN.init(a,b)}),c3=at("ZodE164",(a,b)=>{b_.init(a,b),cN.init(a,b)}),c4=at("ZodJWT",(a,b)=>{b0.init(a,b),cN.init(a,b)}),c5=at("ZodUnknown",(a,b)=>{b1.init(a,b),cJ.init(a,b)});function c6(){return new c5({type:"unknown"})}let c7=at("ZodNever",(a,b)=>{b2.init(a,b),cJ.init(a,b)}),c8=at("ZodArray",(a,b)=>{b4.init(a,b),cJ.init(a,b),a.element=b.element,a.min=(b,c)=>a.check(cw(b,c)),a.nonempty=b=>a.check(cw(1,b)),a.max=(b,c)=>a.check(cv(b,c)),a.length=(b,c)=>a.check(cx(b,c)),a.unwrap=()=>a.element}),c9=at("ZodObject",(a,b)=>{b7.init(a,b),cJ.init(a,b),aA(a,"shape",()=>b.shape),a.keyof=()=>(function(a,b){return new dc({type:"enum",entries:Array.isArray(a)?Object.fromEntries(a.map(a=>[a,a])):a,...aK(void 0)})})(Object.keys(a._zod.def.shape)),a.catchall=b=>a.clone({...a._zod.def,catchall:b}),a.passthrough=()=>a.clone({...a._zod.def,catchall:c6()}),a.loose=()=>a.clone({...a._zod.def,catchall:c6()}),a.strict=()=>a.clone({...a._zod.def,catchall:new c7({type:"never",...aK(void 0)})}),a.strip=()=>a.clone({...a._zod.def,catchall:void 0}),a.extend=b=>(function(a,b){if(!aG(b))throw Error("Invalid input to extend: expected a plain object");let c={...a._zod.def,get shape(){let c={...a._zod.def.shape,...b};return aB(this,"shape",c),c},checks:[]};return aJ(a,c)})(a,b),a.merge=b=>(function(a,b){return aJ(a,{...a._zod.def,get shape(){let c={...a._zod.def.shape,...b._zod.def.shape};return aB(this,"shape",c),c},catchall:b._zod.def.catchall,checks:[]})})(a,b),a.pick=b=>(function(a,b){let c={},d=a._zod.def;for(let a in b){if(!(a in d.shape))throw Error(`Unrecognized key: "${a}"`);b[a]&&(c[a]=d.shape[a])}return aJ(a,{...a._zod.def,shape:c,checks:[]})})(a,b),a.omit=b=>(function(a,b){let c={...a._zod.def.shape},d=a._zod.def;for(let a in b){if(!(a in d.shape))throw Error(`Unrecognized key: "${a}"`);b[a]&&delete c[a]}return aJ(a,{...a._zod.def,shape:c,checks:[]})})(a,b),a.partial=(...b)=>(function(a,b,c){let d=b._zod.def.shape,e={...d};if(c)for(let b in c){if(!(b in d))throw Error(`Unrecognized key: "${b}"`);c[b]&&(e[b]=a?new a({type:"optional",innerType:d[b]}):d[b])}else for(let b in d)e[b]=a?new a({type:"optional",innerType:d[b]}):d[b];return aJ(b,{...b._zod.def,shape:e,checks:[]})})(de,a,b[0]),a.required=(...b)=>(function(a,b,c){let d=b._zod.def.shape,e={...d};if(c)for(let b in c){if(!(b in e))throw Error(`Unrecognized key: "${b}"`);c[b]&&(e[b]=new a({type:"nonoptional",innerType:d[b]}))}else for(let b in d)e[b]=new a({type:"nonoptional",innerType:d[b]});return aJ(b,{...b._zod.def,shape:e,checks:[]})})(dk,a,b[0])}),da=at("ZodUnion",(a,b)=>{b9.init(a,b),cJ.init(a,b),a.options=b.options}),db=at("ZodIntersection",(a,b)=>{ca.init(a,b),cJ.init(a,b)}),dc=at("ZodEnum",(a,b)=>{cc.init(a,b),cJ.init(a,b),a.enum=b.entries,a.options=Object.values(b.entries);let c=new Set(Object.keys(b.entries));a.extract=(a,d)=>{let e={};for(let d of a)if(c.has(d))e[d]=b.entries[d];else throw Error(`Key ${d} not found in enum`);return new dc({...b,checks:[],...aK(d),entries:e})},a.exclude=(a,d)=>{let e={...b.entries};for(let b of a)if(c.has(b))delete e[b];else throw Error(`Key ${b} not found in enum`);return new dc({...b,checks:[],...aK(d),entries:e})}}),dd=at("ZodTransform",(a,b)=>{cd.init(a,b),cJ.init(a,b),a._zod.parse=(c,d)=>{c.addIssue=d=>{"string"==typeof d?c.issues.push(aQ(d,c.value,b)):(d.fatal&&(d.continue=!1),d.code??(d.code="custom"),d.input??(d.input=c.value),d.inst??(d.inst=a),d.continue??(d.continue=!0),c.issues.push(aQ(d)))};let e=b.transform(c.value,c);return e instanceof Promise?e.then(a=>(c.value=a,c)):(c.value=e,c)}}),de=at("ZodOptional",(a,b)=>{ce.init(a,b),cJ.init(a,b),a.unwrap=()=>a._zod.def.innerType});function df(a){return new de({type:"optional",innerType:a})}let dg=at("ZodNullable",(a,b)=>{cf.init(a,b),cJ.init(a,b),a.unwrap=()=>a._zod.def.innerType});function dh(a){return new dg({type:"nullable",innerType:a})}let di=at("ZodDefault",(a,b)=>{cg.init(a,b),cJ.init(a,b),a.unwrap=()=>a._zod.def.innerType,a.removeDefault=a.unwrap}),dj=at("ZodPrefault",(a,b)=>{ci.init(a,b),cJ.init(a,b),a.unwrap=()=>a._zod.def.innerType}),dk=at("ZodNonOptional",(a,b)=>{cj.init(a,b),cJ.init(a,b),a.unwrap=()=>a._zod.def.innerType}),dl=at("ZodCatch",(a,b)=>{cl.init(a,b),cJ.init(a,b),a.unwrap=()=>a._zod.def.innerType,a.removeCatch=a.unwrap}),dm=at("ZodPipe",(a,b)=>{cm.init(a,b),cJ.init(a,b),a.in=b.in,a.out=b.out});function dn(a,b){return new dm({type:"pipe",in:a,out:b})}let dp=at("ZodReadonly",(a,b)=>{co.init(a,b),cJ.init(a,b)}),dq=at("ZodCustom",(a,b)=>{cq.init(a,b),cJ.init(a,b)});var dr=c(9523),ds=c(8730),dt=c(1743),du=c(4163),dv=l.forwardRef((a,b)=>(0,e.jsx)(du.sG.label,{...a,ref:b,onMouseDown:b=>{b.target.closest("button, input, select, textarea")||(a.onMouseDown?.(b),!b.defaultPrevented&&b.detail>1&&b.preventDefault())}}));function dw({className:a,...b}){return(0,e.jsx)(dv,{"data-slot":"label",className:(0,dt.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",a),...b})}dv.displayName="Label";let dx=a=>{let{children:b,...c}=a;return l.createElement(C.Provider,{value:c},b)},dy=l.createContext({}),dz=({...a})=>(0,e.jsx)(dy.Provider,{value:{name:a.name},children:(0,e.jsx)(I,{...a})}),dA=()=>{let a=l.useContext(dy),b=l.useContext(dB),{getFieldState:c}=D(),d=G({name:a.name}),e=c(a.name,d);if(!a)throw Error("useFormField should be used within <FormField>");let{id:f}=b;return{id:f,name:a.name,formItemId:`${f}-form-item`,formDescriptionId:`${f}-form-item-description`,formMessageId:`${f}-form-item-message`,...e}},dB=l.createContext({});function dC({className:a,...b}){let c=l.useId();return(0,e.jsx)(dB.Provider,{value:{id:c},children:(0,e.jsx)("div",{"data-slot":"form-item",className:(0,dt.cn)("grid gap-2",a),...b})})}function dD({className:a,...b}){let{error:c,formItemId:d}=dA();return(0,e.jsx)(dw,{"data-slot":"form-label","data-error":!!c,className:(0,dt.cn)("data-[error=true]:text-destructive",a),htmlFor:d,...b})}function dE({...a}){let{error:b,formItemId:c,formDescriptionId:d,formMessageId:f}=dA();return(0,e.jsx)(ds.DX,{"data-slot":"form-control",id:c,"aria-describedby":b?`${d} ${f}`:`${d}`,"aria-invalid":!!b,...a})}function dF({className:a,...b}){let{error:c,formMessageId:d}=dA(),f=c?String(c?.message??""):b.children;return f?(0,e.jsx)("p",{"data-slot":"form-message",id:d,className:(0,dt.cn)("text-destructive text-sm",a),...b,children:f}):null}function dG({className:a,type:b,...c}){return(0,e.jsx)("input",{type:b,"data-slot":"input",className:(0,dt.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",a),...c})}function dH({className:a,...b}){return(0,e.jsx)("textarea",{"data-slot":"textarea",className:(0,dt.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",a),...b})}var dI=c(2688);let dJ=(0,dI.A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]),dK=(0,dI.A)("send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]]),dL=(d={name:cM().min(2,"Name must be at least 2 characters"),email:cM().email("Please enter a valid email address"),phone:cM().optional(),subject:cM().min(5,"Subject must be at least 5 characters"),message:cM().min(10,"Message must be at least 10 characters")},new c9({type:"object",get shape(){return aB(this,"shape",{...d}),this.shape},...aK(void 0)}));function dM(){let[a,b]=(0,l.useState)(!1),[c,d]=(0,l.useState)(!1),h=function(a={}){let b=l.useRef(void 0),c=l.useRef(void 0),[d,e]=l.useState({isDirty:!1,isValidating:!1,isLoading:P(a.defaultValues),isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,submitCount:0,dirtyFields:{},touchedFields:{},validatingFields:{},errors:a.errors||{},disabled:a.disabled||!1,isReady:!1,defaultValues:P(a.defaultValues)?void 0:a.defaultValues});if(!b.current)if(a.formControl)b.current={...a.formControl,formState:d},a.defaultValues&&!P(a.defaultValues)&&a.formControl.reset(a.defaultValues,a.resetOptions);else{let{formControl:c,...e}=function(a={}){let b,c={...an,...a},d={submitCount:0,isDirty:!1,isReady:!1,isLoading:P(c.defaultValues),isValidating:!1,isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,touchedFields:{},dirtyFields:{},validatingFields:{},errors:c.errors||{},disabled:c.disabled||!1},e={},f=(o(c.defaultValues)||o(c.values))&&s(c.defaultValues||c.values)||{},g=c.shouldUnregister?{}:s(f),h={action:!1,mount:!1,watch:!1},i={mount:new Set,disabled:new Set,unMount:new Set,array:new Set,watch:new Set},j=0,k={isDirty:!1,dirtyFields:!1,validatingFields:!1,touchedFields:!1,isValidating:!1,isValid:!1,errors:!1},l={...k},t={array:L(),state:L()},w=c.criteriaMode===A.all,B=async a=>{if(!c.disabled&&(k.isValid||l.isValid||a)){let a=c.resolver?O((await F()).errors):await I(e,!0);a!==d.isValid&&t.state.next({isValid:a})}},C=(a,b)=>{!c.disabled&&(k.isValidating||k.validatingFields||l.isValidating||l.validatingFields)&&((a||Array.from(i.mount)).forEach(a=>{a&&(b?y(d.validatingFields,a,b):S(d.validatingFields,a))}),t.state.next({validatingFields:d.validatingFields,isValidating:!O(d.validatingFields)}))},D=(a,b,c,d)=>{let i=x(e,a);if(i){let e=x(g,a,u(c)?x(f,a):c);u(e)||d&&d.defaultChecked||b?y(g,a,b?e:aa(i._f)):T(a,e),h.mount&&B()}},E=(a,b,e,g,h)=>{let i=!1,j=!1,m={name:a};if(!c.disabled){if(!e||g){(k.isDirty||l.isDirty)&&(j=d.isDirty,d.isDirty=m.isDirty=J(),i=j!==m.isDirty);let c=N(x(f,a),b);j=!!x(d.dirtyFields,a),c?S(d.dirtyFields,a):y(d.dirtyFields,a,!0),m.dirtyFields=d.dirtyFields,i=i||(k.dirtyFields||l.dirtyFields)&&!c!==j}if(e){let b=x(d.touchedFields,a);b||(y(d.touchedFields,a,e),m.touchedFields=d.touchedFields,i=i||(k.touchedFields||l.touchedFields)&&b!==e)}i&&h&&t.state.next(m)}return i?m:{}},F=async a=>{C(a,!0);let b=await c.resolver(g,c.context,((a,b,c,d)=>{let e={};for(let c of a){let a=x(b,c);a&&y(e,c,a._f)}return{criteriaMode:c,names:[...a],fields:e,shouldUseNativeValidation:d}})(a||i.mount,e,c.criteriaMode,c.shouldUseNativeValidation));return C(a),b},G=async a=>{let{errors:b}=await F(a);if(a)for(let c of a){let a=x(b,c);a?y(d.errors,c,a):S(d.errors,c)}else d.errors=b;return b},I=async(a,b,e={valid:!0})=>{for(let f in a){let h=a[f];if(h){let{_f:a,...j}=h;if(a){let j=i.array.has(a.name),l=h._f&&ae(h._f);l&&k.validatingFields&&C([f],!0);let m=await am(h,i.disabled,g,w,c.shouldUseNativeValidation&&!b,j);if(l&&k.validatingFields&&C([f]),m[a.name]&&(e.valid=!1,b))break;b||(x(m,a.name)?j?ai(d.errors,m,a.name):y(d.errors,a.name,m[a.name]):S(d.errors,a.name))}O(j)||await I(j,b,e)}}return e.valid},J=(a,b)=>!c.disabled&&(a&&b&&y(g,a,b),!N(_(),f)),M=(a,b,c)=>H(a,i,{...h.mount?g:u(b)?f:"string"==typeof a?{[a]:b}:b},c,b),T=(a,b,c={})=>{let d=x(e,a),f=b;if(d){let c=d._f;c&&(c.disabled||y(g,a,Z(b,c)),f=Q(c.ref)&&n(b)?"":b,"select-multiple"===c.ref.type?[...c.ref.options].forEach(a=>a.selected=f.includes(a.value)):c.refs?"checkbox"===c.ref.type?c.refs.forEach(a=>{a.defaultChecked&&a.disabled||(Array.isArray(f)?a.checked=!!f.find(b=>b===a.value):a.checked=f===a.value||!!f)}):c.refs.forEach(a=>a.checked=a.value===f):"file"===c.ref.type?c.ref.value="":(c.ref.value=f,c.ref.type||t.state.next({name:a,values:s(g)})))}(c.shouldDirty||c.shouldTouch)&&E(a,f,c.shouldTouch,c.shouldDirty,!0),c.shouldValidate&&$(a)},U=(a,b,c)=>{for(let d in b){if(!b.hasOwnProperty(d))return;let f=b[d],g=a+"."+d,h=x(e,g);(i.array.has(a)||o(f)||h&&!h._f)&&!m(f)?U(g,f,c):T(g,f,c)}},W=(a,b,c={})=>{let j=x(e,a),m=i.array.has(a),o=s(b);y(g,a,o),m?(t.array.next({name:a,values:s(g)}),(k.isDirty||k.dirtyFields||l.isDirty||l.dirtyFields)&&c.shouldDirty&&t.state.next({name:a,dirtyFields:V(f,g),isDirty:J(a,o)})):!j||j._f||n(o)?T(a,o,c):U(a,o,c),af(a,i)&&t.state.next({...d}),t.state.next({name:h.mount?a:void 0,values:s(g)})},X=async a=>{h.mount=!0;let f=a.target,n=f.name,o=!0,q=x(e,n),r=a=>{o=Number.isNaN(a)||m(a)&&isNaN(a.getTime())||N(a,x(g,n,a))},u=ac(c.mode),v=ac(c.reValidateMode);if(q){let h,m,P,Q=f.type?aa(q._f):p(a),R=a.type===z.BLUR||a.type===z.FOCUS_OUT,T=!((P=q._f).mount&&(P.required||P.min||P.max||P.maxLength||P.minLength||P.pattern||P.validate))&&!c.resolver&&!x(d.errors,n)&&!q._f.deps||(A=R,D=x(d.touchedFields,n),G=d.isSubmitted,H=v,!(J=u).isOnAll&&(!G&&J.isOnTouch?!(D||A):(G?H.isOnBlur:J.isOnBlur)?!A:(G?!H.isOnChange:!J.isOnChange)||A)),U=af(n,i,R);y(g,n,Q),R?(q._f.onBlur&&q._f.onBlur(a),b&&b(0)):q._f.onChange&&q._f.onChange(a);let V=E(n,Q,R),W=!O(V)||U;if(R||t.state.next({name:n,type:a.type,values:s(g)}),T)return(k.isValid||l.isValid)&&("onBlur"===c.mode?R&&B():R||B()),W&&t.state.next({name:n,...U?{}:V});if(!R&&U&&t.state.next({...d}),c.resolver){let{errors:a}=await F([n]);if(r(Q),o){let b=ah(d.errors,e,n),c=ah(a,e,b.name||n);h=c.error,n=c.name,m=O(a)}}else C([n],!0),h=(await am(q,i.disabled,g,w,c.shouldUseNativeValidation))[n],C([n]),r(Q),o&&(h?m=!1:(k.isValid||l.isValid)&&(m=await I(e,!0)));if(o){q._f.deps&&$(q._f.deps);var A,D,G,H,J,K=n,L=m,M=h;let a=x(d.errors,K),e=(k.isValid||l.isValid)&&"boolean"==typeof L&&d.isValid!==L;if(c.delayError&&M){let a;a=()=>{y(d.errors,K,M),t.state.next({errors:d.errors})},(b=b=>{clearTimeout(j),j=setTimeout(a,b)})(c.delayError)}else clearTimeout(j),b=null,M?y(d.errors,K,M):S(d.errors,K);if((M?!N(a,M):a)||!O(V)||e){let a={...V,...e&&"boolean"==typeof L?{isValid:L}:{},errors:d.errors,name:K};d={...d,...a},t.state.next(a)}}}},Y=(a,b)=>{if(x(d.errors,b)&&a.focus)return a.focus(),1},$=async(a,b={})=>{let f,g,h=K(a);if(c.resolver){let b=await G(u(a)?a:h);f=O(b),g=a?!h.some(a=>x(b,a)):f}else a?((g=(await Promise.all(h.map(async a=>{let b=x(e,a);return await I(b&&b._f?{[a]:b}:b)}))).every(Boolean))||d.isValid)&&B():g=f=await I(e);return t.state.next({..."string"!=typeof a||(k.isValid||l.isValid)&&f!==d.isValid?{}:{name:a},...c.resolver||!a?{isValid:f}:{},errors:d.errors}),b.shouldFocus&&!g&&ag(e,Y,a?h:i.mount),g},_=a=>{let b={...h.mount?g:f};return u(a)?b:"string"==typeof a?x(b,a):a.map(a=>x(b,a))},ad=(a,b)=>({invalid:!!x((b||d).errors,a),isDirty:!!x((b||d).dirtyFields,a),error:x((b||d).errors,a),isValidating:!!x(d.validatingFields,a),isTouched:!!x((b||d).touchedFields,a)}),aj=(a,b,c)=>{let f=(x(e,a,{_f:{}})._f||{}).ref,{ref:g,message:h,type:i,...j}=x(d.errors,a)||{};y(d.errors,a,{...j,...b,ref:f}),t.state.next({name:a,errors:d.errors,isValid:!1}),c&&c.shouldFocus&&f&&f.focus&&f.focus()},ak=a=>t.state.subscribe({next:b=>{let c,e,f;c=a.name,e=b.name,f=a.exact,(!c||!e||c===e||K(c).some(a=>a&&(f?a===e:a.startsWith(e)||e.startsWith(a))))&&((a,b,c,d)=>{c(a);let{name:e,...f}=a;return O(f)||Object.keys(f).length>=Object.keys(b).length||Object.keys(f).find(a=>b[a]===(!d||A.all))})(b,a.formState||k,au,a.reRenderRoot)&&a.callback({values:{...g},...d,...b})}}).unsubscribe,al=(a,b={})=>{for(let h of a?K(a):i.mount)i.mount.delete(h),i.array.delete(h),b.keepValue||(S(e,h),S(g,h)),b.keepError||S(d.errors,h),b.keepDirty||S(d.dirtyFields,h),b.keepTouched||S(d.touchedFields,h),b.keepIsValidating||S(d.validatingFields,h),c.shouldUnregister||b.keepDefaultValue||S(f,h);t.state.next({values:s(g)}),t.state.next({...d,...!b.keepDirty?{}:{isDirty:J()}}),b.keepIsValid||B()},ao=({disabled:a,name:b})=>{("boolean"==typeof a&&h.mount||a||i.disabled.has(b))&&(a?i.disabled.add(b):i.disabled.delete(b))},ap=(a,b={})=>{let d=x(e,a),g="boolean"==typeof b.disabled||"boolean"==typeof c.disabled;return(y(e,a,{...d||{},_f:{...d&&d._f?d._f:{ref:{name:a}},name:a,mount:!0,...b}}),i.mount.add(a),d)?ao({disabled:"boolean"==typeof b.disabled?b.disabled:c.disabled,name:a}):D(a,!0,b.value),{...g?{disabled:b.disabled||c.disabled}:{},...c.progressive?{required:!!b.required,min:ab(b.min),max:ab(b.max),minLength:ab(b.minLength),maxLength:ab(b.maxLength),pattern:ab(b.pattern)}:{},name:a,onChange:X,onBlur:X,ref:g=>{if(g){let c;ap(a,b),d=x(e,a);let h=u(g.value)&&g.querySelectorAll&&g.querySelectorAll("input,select,textarea")[0]||g,i="radio"===(c=h).type||"checkbox"===c.type,j=d._f.refs||[];(i?j.find(a=>a===h):h===d._f.ref)||(y(e,a,{_f:{...d._f,...i?{refs:[...j.filter(R),h,...Array.isArray(x(f,a))?[{}]:[]],ref:{type:h.type,name:a}}:{ref:h}}}),D(a,!1,void 0,h))}else(d=x(e,a,{}))._f&&(d._f.mount=!1),(c.shouldUnregister||b.shouldUnregister)&&!(q(i.array,a)&&h.action)&&i.unMount.add(a)}}},aq=()=>c.shouldFocusError&&ag(e,Y,i.mount),ar=(a,b)=>async f=>{let h;f&&(f.preventDefault&&f.preventDefault(),f.persist&&f.persist());let j=s(g);if(t.state.next({isSubmitting:!0}),c.resolver){let{errors:a,values:b}=await F();d.errors=a,j=s(b)}else await I(e);if(i.disabled.size)for(let a of i.disabled)S(j,a);if(S(d.errors,"root"),O(d.errors)){t.state.next({errors:{}});try{await a(j,f)}catch(a){h=a}}else b&&await b({...d.errors},f),aq(),setTimeout(aq);if(t.state.next({isSubmitted:!0,isSubmitting:!1,isSubmitSuccessful:O(d.errors)&&!h,submitCount:d.submitCount+1,errors:d.errors}),h)throw h},as=(a,b={})=>{let j=a?s(a):f,l=s(j),m=O(a),n=m?f:l;if(b.keepDefaultValues||(f=j),!b.keepValues){if(b.keepDirtyValues)for(let a of Array.from(new Set([...i.mount,...Object.keys(V(f,g))])))x(d.dirtyFields,a)?y(n,a,x(g,a)):W(a,x(n,a));else{if(r&&u(a))for(let a of i.mount){let b=x(e,a);if(b&&b._f){let a=Array.isArray(b._f.refs)?b._f.refs[0]:b._f.ref;if(Q(a)){let b=a.closest("form");if(b){b.reset();break}}}}if(b.keepFieldsRef)for(let a of i.mount)W(a,x(n,a));else e={}}g=c.shouldUnregister?b.keepDefaultValues?s(f):{}:s(n),t.array.next({values:{...n}}),t.state.next({values:{...n}})}i={mount:b.keepDirtyValues?i.mount:new Set,unMount:new Set,array:new Set,disabled:new Set,watch:new Set,watchAll:!1,focus:""},h.mount=!k.isValid||!!b.keepIsValid||!!b.keepDirtyValues,h.watch=!!c.shouldUnregister,t.state.next({submitCount:b.keepSubmitCount?d.submitCount:0,isDirty:!m&&(b.keepDirty?d.isDirty:!!(b.keepDefaultValues&&!N(a,f))),isSubmitted:!!b.keepIsSubmitted&&d.isSubmitted,dirtyFields:m?{}:b.keepDirtyValues?b.keepDefaultValues&&g?V(f,g):d.dirtyFields:b.keepDefaultValues&&a?V(f,a):b.keepDirty?d.dirtyFields:{},touchedFields:b.keepTouched?d.touchedFields:{},errors:b.keepErrors?d.errors:{},isSubmitSuccessful:!!b.keepIsSubmitSuccessful&&d.isSubmitSuccessful,isSubmitting:!1})},at=(a,b)=>as(P(a)?a(g):a,b),au=a=>{d={...d,...a}},av={control:{register:ap,unregister:al,getFieldState:ad,handleSubmit:ar,setError:aj,_subscribe:ak,_runSchema:F,_focusError:aq,_getWatch:M,_getDirty:J,_setValid:B,_setFieldArray:(a,b=[],i,j,m=!0,n=!0)=>{if(j&&i&&!c.disabled){if(h.action=!0,n&&Array.isArray(x(e,a))){let b=i(x(e,a),j.argA,j.argB);m&&y(e,a,b)}if(n&&Array.isArray(x(d.errors,a))){let b,c=i(x(d.errors,a),j.argA,j.argB);m&&y(d.errors,a,c),v(x(b=d.errors,a)).length||S(b,a)}if((k.touchedFields||l.touchedFields)&&n&&Array.isArray(x(d.touchedFields,a))){let b=i(x(d.touchedFields,a),j.argA,j.argB);m&&y(d.touchedFields,a,b)}(k.dirtyFields||l.dirtyFields)&&(d.dirtyFields=V(f,g)),t.state.next({name:a,isDirty:J(a,b),dirtyFields:d.dirtyFields,errors:d.errors,isValid:d.isValid})}else y(g,a,b)},_setDisabledField:ao,_setErrors:a=>{d.errors=a,t.state.next({errors:d.errors,isValid:!1})},_getFieldArray:a=>v(x(h.mount?g:f,a,c.shouldUnregister?x(f,a,[]):[])),_reset:as,_resetDefaultValues:()=>P(c.defaultValues)&&c.defaultValues().then(a=>{at(a,c.resetOptions),t.state.next({isLoading:!1})}),_removeUnmounted:()=>{for(let a of i.unMount){let b=x(e,a);b&&(b._f.refs?b._f.refs.every(a=>!R(a)):!R(b._f.ref))&&al(a)}i.unMount=new Set},_disableForm:a=>{"boolean"==typeof a&&(t.state.next({disabled:a}),ag(e,(b,c)=>{let d=x(e,c);d&&(b.disabled=d._f.disabled||a,Array.isArray(d._f.refs)&&d._f.refs.forEach(b=>{b.disabled=d._f.disabled||a}))},0,!1))},_subjects:t,_proxyFormState:k,get _fields(){return e},get _formValues(){return g},get _state(){return h},set _state(value){h=value},get _defaultValues(){return f},get _names(){return i},set _names(value){i=value},get _formState(){return d},get _options(){return c},set _options(value){c={...c,...value}}},subscribe:a=>(h.mount=!0,l={...l,...a.formState},ak({...a,formState:l})),trigger:$,register:ap,handleSubmit:ar,watch:(a,b)=>P(a)?t.state.subscribe({next:c=>a(M(void 0,b),c)}):M(a,b,!0),setValue:W,getValues:_,reset:at,resetField:(a,b={})=>{x(e,a)&&(u(b.defaultValue)?W(a,s(x(f,a))):(W(a,b.defaultValue),y(f,a,s(b.defaultValue))),b.keepTouched||S(d.touchedFields,a),b.keepDirty||(S(d.dirtyFields,a),d.isDirty=b.defaultValue?J(a,s(x(f,a))):J()),!b.keepError&&(S(d.errors,a),k.isValid&&B()),t.state.next({...d}))},clearErrors:a=>{a&&K(a).forEach(a=>S(d.errors,a)),t.state.next({errors:a?d.errors:{}})},unregister:al,setError:aj,setFocus:(a,b={})=>{let c=x(e,a),d=c&&c._f;if(d){let a=d.refs?d.refs[0]:d.ref;a.focus&&(a.focus(),b.shouldSelect&&P(a.select)&&a.select())}},getFieldState:ad};return{...av,formControl:av}}(a);b.current={...e,formState:d}}let f=b.current.control;return f._options=a,F(()=>{let a=f._subscribe({formState:f._proxyFormState,callback:()=>e({...f._formState}),reRenderRoot:!0});return e(a=>({...a,isReady:!0})),f._formState.isReady=!0,a},[f]),l.useEffect(()=>f._disableForm(a.disabled),[f,a.disabled]),l.useEffect(()=>{a.mode&&(f._options.mode=a.mode),a.reValidateMode&&(f._options.reValidateMode=a.reValidateMode)},[f,a.mode,a.reValidateMode]),l.useEffect(()=>{a.errors&&(f._setErrors(a.errors),f._focusError())},[f,a.errors]),l.useEffect(()=>{a.shouldUnregister&&f._subjects.state.next({values:f._getWatch()})},[f,a.shouldUnregister]),l.useEffect(()=>{if(f._proxyFormState.isDirty){let a=f._getDirty();a!==d.isDirty&&f._subjects.state.next({isDirty:a})}},[f,d.isDirty]),l.useEffect(()=>{a.values&&!N(a.values,c.current)?(f._reset(a.values,{keepFieldsRef:!0,...f._options.resetOptions}),c.current=a.values,e(a=>({...a}))):f._resetDefaultValues()},[f,a.values]),l.useEffect(()=>{f._state.mount||(f._setValid(),f._state.mount=!0),f._state.watch&&(f._state.watch=!1,f._subjects.state.next({...f._formState})),f._removeUnmounted()}),b.current.formState=E(d,f),b.current}({resolver:function(a,b,c){if(void 0===c&&(c={}),"_def"in a&&"object"==typeof a._def&&"typeName"in a._def)return function(b,d,e){try{return Promise.resolve(a0(function(){return Promise.resolve(a["sync"===c.mode?"parse":"parseAsync"](b,void 0)).then(function(a){return e.shouldUseNativeValidation&&ap({},e),{errors:{},values:c.raw?Object.assign({},b):a}})},function(a){if(Array.isArray(null==a?void 0:a.issues))return{values:{},errors:aq(function(a,b){for(var c={};a.length;){var d=a[0],e=d.code,f=d.message,g=d.path.join(".");if(!c[g])if("unionErrors"in d){var h=d.unionErrors[0].errors[0];c[g]={message:h.message,type:h.code}}else c[g]={message:f,type:e};if("unionErrors"in d&&d.unionErrors.forEach(function(b){return b.errors.forEach(function(b){return a.push(b)})}),b){var i=c[g].types,j=i&&i[d.code];c[g]=J(g,b,c,e,j?[].concat(j,d.message):d.message)}a.shift()}return c}(a.errors,!e.shouldUseNativeValidation&&"all"===e.criteriaMode),e)};throw a}))}catch(a){return Promise.reject(a)}};if("_zod"in a&&"object"==typeof a._zod)return function(b,d,e){try{return Promise.resolve(a0(function(){return Promise.resolve(("sync"===c.mode?aV:aX)(a,b,void 0)).then(function(a){return e.shouldUseNativeValidation&&ap({},e),{errors:{},values:c.raw?Object.assign({},b):a}})},function(a){if(a instanceof aS)return{values:{},errors:aq(function(a,b){for(var c={};a.length;){var d=a[0],e=d.code,f=d.message,g=d.path.join(".");if(!c[g])if("invalid_union"===d.code){var h=d.errors[0][0];c[g]={message:h.message,type:h.code}}else c[g]={message:f,type:e};if("invalid_union"===d.code&&d.errors.forEach(function(b){return b.forEach(function(b){return a.push(b)})}),b){var i=c[g].types,j=i&&i[d.code];c[g]=J(g,b,c,e,j?[].concat(j,d.message):d.message)}a.shift()}return c}(a.issues,!e.shouldUseNativeValidation&&"all"===e.criteriaMode),e)};throw a}))}catch(a){return Promise.reject(a)}};throw Error("Invalid input: not a Zod schema")}(dL),defaultValues:{name:"",email:"",phone:"",subject:"",message:""}}),i=async a=>{d(!0),await new Promise(a=>setTimeout(a,1e3)),console.log("Contact form data:",a),b(!0),d(!1),h.reset()};return a?(0,e.jsxs)(f.P.div,{initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},className:"text-center py-12",children:[(0,e.jsx)("div",{className:"w-16 h-16 bg-cafe-green rounded-full flex items-center justify-center mx-auto mb-4",children:(0,e.jsx)(dJ,{className:"h-8 w-8 text-white"})}),(0,e.jsx)("h3",{className:"font-serif text-2xl font-semibold text-cafe-dark-brown mb-2",children:"Message Sent Successfully!"}),(0,e.jsx)("p",{className:"text-cafe-brown mb-6",children:"Thank you for reaching out. We'll get back to you within 24 hours."}),(0,e.jsx)(dr.$,{onClick:()=>b(!1),variant:"outline",className:"border-cafe-brown text-cafe-brown hover:bg-cafe-brown hover:text-cafe-cream",children:"Send Another Message"})]}):(0,e.jsxs)(g.Zp,{className:"border-cafe-beige shadow-cafe-lg",children:[(0,e.jsxs)(g.aR,{children:[(0,e.jsx)(g.ZB,{className:"font-serif text-2xl text-cafe-dark-brown text-center",children:"Get in Touch"}),(0,e.jsx)("p",{className:"text-cafe-brown text-center",children:"Have a question or want to make a reservation? We'd love to hear from you!"})]}),(0,e.jsx)(g.Wu,{children:(0,e.jsx)(dx,{...h,children:(0,e.jsxs)("form",{onSubmit:h.handleSubmit(i),className:"space-y-6",children:[(0,e.jsx)(dz,{control:h.control,name:"name",render:({field:a})=>(0,e.jsxs)(dC,{children:[(0,e.jsx)(dD,{className:"text-cafe-dark-brown font-medium",children:"Full Name *"}),(0,e.jsx)(dE,{children:(0,e.jsx)(dG,{placeholder:"Enter your full name",className:"border-cafe-beige focus:border-cafe-brown",...a})}),(0,e.jsx)(dF,{})]})}),(0,e.jsx)(dz,{control:h.control,name:"email",render:({field:a})=>(0,e.jsxs)(dC,{children:[(0,e.jsx)(dD,{className:"text-cafe-dark-brown font-medium",children:"Email Address *"}),(0,e.jsx)(dE,{children:(0,e.jsx)(dG,{type:"email",placeholder:"Enter your email address",className:"border-cafe-beige focus:border-cafe-brown",...a})}),(0,e.jsx)(dF,{})]})}),(0,e.jsx)(dz,{control:h.control,name:"phone",render:({field:a})=>(0,e.jsxs)(dC,{children:[(0,e.jsx)(dD,{className:"text-cafe-dark-brown font-medium",children:"Phone Number (Optional)"}),(0,e.jsx)(dE,{children:(0,e.jsx)(dG,{type:"tel",placeholder:"Enter your phone number",className:"border-cafe-beige focus:border-cafe-brown",...a})}),(0,e.jsx)(dF,{})]})}),(0,e.jsx)(dz,{control:h.control,name:"subject",render:({field:a})=>(0,e.jsxs)(dC,{children:[(0,e.jsx)(dD,{className:"text-cafe-dark-brown font-medium",children:"Subject *"}),(0,e.jsx)(dE,{children:(0,e.jsx)(dG,{placeholder:"What's this about?",className:"border-cafe-beige focus:border-cafe-brown",...a})}),(0,e.jsx)(dF,{})]})}),(0,e.jsx)(dz,{control:h.control,name:"message",render:({field:a})=>(0,e.jsxs)(dC,{children:[(0,e.jsx)(dD,{className:"text-cafe-dark-brown font-medium",children:"Message *"}),(0,e.jsx)(dE,{children:(0,e.jsx)(dH,{placeholder:"Tell us more about your inquiry...",className:"border-cafe-beige focus:border-cafe-brown min-h-[120px]",...a})}),(0,e.jsx)(dF,{})]})}),(0,e.jsx)(dr.$,{type:"submit",disabled:c,className:"w-full bg-cafe-brown hover:bg-cafe-dark-brown text-cafe-cream font-semibold py-3",children:c?(0,e.jsxs)("div",{className:"flex items-center gap-2",children:[(0,e.jsx)("div",{className:"w-4 h-4 border-2 border-cafe-cream border-t-transparent rounded-full animate-spin"}),"Sending..."]}):(0,e.jsxs)("div",{className:"flex items-center gap-2",children:[(0,e.jsx)(dK,{className:"h-4 w-4"}),"Send Message"]})})]})})})]})}var dN=c(8465);function dO(){return(0,e.jsx)("section",{className:"py-20 bg-cafe-cream",children:(0,e.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,e.jsxs)(f.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.6},viewport:{once:!0},className:"text-center mb-16",children:[(0,e.jsx)("h2",{className:"font-serif text-4xl md:text-5xl font-bold text-cafe-dark-brown mb-6",children:"Visit Us Today"}),(0,e.jsx)("p",{className:"text-xl text-cafe-brown max-w-3xl mx-auto leading-relaxed",children:"We'd love to welcome you to our cozy caf\xe9. Drop by for a cup of coffee or get in touch with us for any inquiries."})]}),(0,e.jsxs)("div",{className:"grid lg:grid-cols-2 gap-12",children:[(0,e.jsx)(f.P.div,{initial:{opacity:0,x:-30},whileInView:{opacity:1,x:0},transition:{duration:.6},viewport:{once:!0},children:(0,e.jsx)(dM,{})}),(0,e.jsxs)(f.P.div,{initial:{opacity:0,x:30},whileInView:{opacity:1,x:0},transition:{duration:.6,delay:.2},viewport:{once:!0},className:"space-y-6",children:[(0,e.jsx)(g.Zp,{className:"border-cafe-beige shadow-cafe",children:(0,e.jsxs)(g.Wu,{className:"p-6 space-y-6",children:[(0,e.jsx)("h3",{className:"font-serif text-2xl font-semibold text-cafe-dark-brown",children:"Contact Details"}),(0,e.jsxs)("div",{className:"space-y-4",children:[(0,e.jsxs)("div",{className:"flex items-start gap-4",children:[(0,e.jsx)("div",{className:"w-12 h-12 bg-cafe-beige rounded-full flex items-center justify-center flex-shrink-0",children:(0,e.jsx)(h.A,{className:"h-6 w-6 text-cafe-brown"})}),(0,e.jsxs)("div",{children:[(0,e.jsx)("h4",{className:"font-semibold text-cafe-dark-brown mb-1",children:"Address"}),(0,e.jsx)("p",{className:"text-cafe-brown",children:dN.I.contact.address})]})]}),(0,e.jsxs)("div",{className:"flex items-start gap-4",children:[(0,e.jsx)("div",{className:"w-12 h-12 bg-cafe-beige rounded-full flex items-center justify-center flex-shrink-0",children:(0,e.jsx)(i.A,{className:"h-6 w-6 text-cafe-brown"})}),(0,e.jsxs)("div",{children:[(0,e.jsx)("h4",{className:"font-semibold text-cafe-dark-brown mb-1",children:"Phone"}),(0,e.jsx)("a",{href:`tel:${dN.I.contact.phone}`,className:"text-cafe-brown hover:text-cafe-dark-brown transition-colors",children:dN.I.contact.phone})]})]}),(0,e.jsxs)("div",{className:"flex items-start gap-4",children:[(0,e.jsx)("div",{className:"w-12 h-12 bg-cafe-beige rounded-full flex items-center justify-center flex-shrink-0",children:(0,e.jsx)(j.A,{className:"h-6 w-6 text-cafe-brown"})}),(0,e.jsxs)("div",{children:[(0,e.jsx)("h4",{className:"font-semibold text-cafe-dark-brown mb-1",children:"Email"}),(0,e.jsx)("a",{href:`mailto:${dN.I.contact.email}`,className:"text-cafe-brown hover:text-cafe-dark-brown transition-colors",children:dN.I.contact.email})]})]})]})]})}),(0,e.jsx)(g.Zp,{className:"border-cafe-beige shadow-cafe",children:(0,e.jsxs)(g.Wu,{className:"p-6 space-y-6",children:[(0,e.jsx)("h3",{className:"font-serif text-2xl font-semibold text-cafe-dark-brown",children:"Opening Hours"}),(0,e.jsx)("div",{className:"space-y-4",children:(0,e.jsxs)("div",{className:"flex items-start gap-4",children:[(0,e.jsx)("div",{className:"w-12 h-12 bg-cafe-beige rounded-full flex items-center justify-center flex-shrink-0",children:(0,e.jsx)(k.A,{className:"h-6 w-6 text-cafe-brown"})}),(0,e.jsxs)("div",{className:"flex-1",children:[(0,e.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[(0,e.jsx)("span",{className:"font-semibold text-cafe-dark-brown",children:"Monday - Friday"}),(0,e.jsx)("span",{className:"text-cafe-brown",children:dN.I.hours.weekdays})]}),(0,e.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[(0,e.jsx)("span",{className:"font-semibold text-cafe-dark-brown",children:"Saturday - Sunday"}),(0,e.jsx)("span",{className:"text-cafe-brown",children:dN.I.hours.weekends})]}),(0,e.jsxs)("div",{className:"flex justify-between items-center",children:[(0,e.jsx)("span",{className:"font-semibold text-cafe-dark-brown",children:"Public Holidays"}),(0,e.jsx)("span",{className:"text-cafe-brown",children:dN.I.hours.holidays})]})]})]})})]})}),(0,e.jsx)(g.Zp,{className:"border-cafe-beige shadow-cafe",children:(0,e.jsx)(g.Wu,{className:"p-0",children:(0,e.jsx)("div",{className:"aspect-video bg-cafe-beige rounded-lg overflow-hidden",children:(0,e.jsx)("div",{className:"w-full h-full flex items-center justify-center text-cafe-brown",children:(0,e.jsxs)("div",{className:"text-center",children:[(0,e.jsx)(h.A,{className:"h-12 w-12 mx-auto mb-2"}),(0,e.jsx)("p",{className:"font-semibold",children:"Interactive Map"}),(0,e.jsx)("p",{className:"text-sm",children:"Find us on Main Road, Biratnagar"})]})})})})})]})]})]})})}},3620:(a,b,c)=>{"use strict";c.d(b,{Footer:()=>q});var d=c(687),e=c(5814),f=c.n(e),g=c(3166),h=c(2688);let i=(0,h.A)("facebook",[["path",{d:"M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z",key:"1jg4f8"}]]),j=(0,h.A)("instagram",[["rect",{width:"20",height:"20",x:"2",y:"2",rx:"5",ry:"5",key:"2e1cvw"}],["path",{d:"M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z",key:"9exkf1"}],["line",{x1:"17.5",x2:"17.51",y1:"6.5",y2:"6.5",key:"r4j83e"}]]),k=(0,h.A)("twitter",[["path",{d:"M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z",key:"pff0z6"}]]);var l=c(7992),m=c(8340),n=c(1550),o=c(6349),p=c(8465);function q(){let a=new Date().getFullYear();return(0,d.jsx)("footer",{className:"bg-cafe-dark-brown text-cafe-cream",children:(0,d.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16",children:[(0,d.jsxs)("div",{className:"grid md:grid-cols-2 lg:grid-cols-4 gap-8",children:[(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)(f(),{href:"/",className:"flex items-center space-x-2",children:[(0,d.jsx)(g.A,{className:"h-8 w-8 text-cafe-gold"}),(0,d.jsx)("span",{className:"font-serif font-bold text-xl",children:"Himalayan Brew"})]}),(0,d.jsx)("p",{className:"text-cafe-beige leading-relaxed",children:p.I.description}),(0,d.jsxs)("div",{className:"flex space-x-4",children:[(0,d.jsx)("a",{href:p.I.socialMedia.facebook,target:"_blank",rel:"noopener noreferrer",className:"w-10 h-10 bg-cafe-brown rounded-full flex items-center justify-center hover:bg-cafe-gold transition-colors duration-200",children:(0,d.jsx)(i,{className:"h-5 w-5"})}),(0,d.jsx)("a",{href:p.I.socialMedia.instagram,target:"_blank",rel:"noopener noreferrer",className:"w-10 h-10 bg-cafe-brown rounded-full flex items-center justify-center hover:bg-cafe-gold transition-colors duration-200",children:(0,d.jsx)(j,{className:"h-5 w-5"})}),(0,d.jsx)("a",{href:p.I.socialMedia.twitter,target:"_blank",rel:"noopener noreferrer",className:"w-10 h-10 bg-cafe-brown rounded-full flex items-center justify-center hover:bg-cafe-gold transition-colors duration-200",children:(0,d.jsx)(k,{className:"h-5 w-5"})})]})]}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsx)("h3",{className:"font-serif text-lg font-semibold text-cafe-gold",children:"Quick Links"}),(0,d.jsxs)("ul",{className:"space-y-2",children:[(0,d.jsx)("li",{children:(0,d.jsx)(f(),{href:"/",className:"text-cafe-beige hover:text-cafe-gold transition-colors duration-200",children:"Home"})}),(0,d.jsx)("li",{children:(0,d.jsx)(f(),{href:"/about",className:"text-cafe-beige hover:text-cafe-gold transition-colors duration-200",children:"About Us"})}),(0,d.jsx)("li",{children:(0,d.jsx)(f(),{href:"/menu",className:"text-cafe-beige hover:text-cafe-gold transition-colors duration-200",children:"Menu"})}),(0,d.jsx)("li",{children:(0,d.jsx)(f(),{href:"/gallery",className:"text-cafe-beige hover:text-cafe-gold transition-colors duration-200",children:"Gallery"})}),(0,d.jsx)("li",{children:(0,d.jsx)(f(),{href:"/testimonials",className:"text-cafe-beige hover:text-cafe-gold transition-colors duration-200",children:"Testimonials"})}),(0,d.jsx)("li",{children:(0,d.jsx)(f(),{href:"/contact",className:"text-cafe-beige hover:text-cafe-gold transition-colors duration-200",children:"Contact"})})]})]}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsx)("h3",{className:"font-serif text-lg font-semibold text-cafe-gold",children:"Contact Info"}),(0,d.jsxs)("ul",{className:"space-y-3",children:[(0,d.jsxs)("li",{className:"flex items-start gap-3",children:[(0,d.jsx)(l.A,{className:"h-5 w-5 text-cafe-gold mt-0.5 flex-shrink-0"}),(0,d.jsx)("span",{className:"text-cafe-beige",children:p.I.contact.address})]}),(0,d.jsxs)("li",{className:"flex items-center gap-3",children:[(0,d.jsx)(m.A,{className:"h-5 w-5 text-cafe-gold flex-shrink-0"}),(0,d.jsx)("a",{href:`tel:${p.I.contact.phone}`,className:"text-cafe-beige hover:text-cafe-gold transition-colors duration-200",children:p.I.contact.phone})]}),(0,d.jsxs)("li",{className:"flex items-center gap-3",children:[(0,d.jsx)(n.A,{className:"h-5 w-5 text-cafe-gold flex-shrink-0"}),(0,d.jsx)("a",{href:`mailto:${p.I.contact.email}`,className:"text-cafe-beige hover:text-cafe-gold transition-colors duration-200",children:p.I.contact.email})]})]})]}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsx)("h3",{className:"font-serif text-lg font-semibold text-cafe-gold",children:"Opening Hours"}),(0,d.jsxs)("ul",{className:"space-y-2",children:[(0,d.jsxs)("li",{className:"flex items-start gap-3",children:[(0,d.jsx)(o.A,{className:"h-5 w-5 text-cafe-gold mt-0.5 flex-shrink-0"}),(0,d.jsxs)("div",{className:"text-cafe-beige",children:[(0,d.jsx)("div",{className:"font-medium",children:"Monday - Friday"}),(0,d.jsx)("div",{className:"text-sm",children:p.I.hours.weekdays})]})]}),(0,d.jsxs)("li",{className:"flex items-start gap-3",children:[(0,d.jsx)(o.A,{className:"h-5 w-5 text-cafe-gold mt-0.5 flex-shrink-0"}),(0,d.jsxs)("div",{className:"text-cafe-beige",children:[(0,d.jsx)("div",{className:"font-medium",children:"Saturday - Sunday"}),(0,d.jsx)("div",{className:"text-sm",children:p.I.hours.weekends})]})]}),(0,d.jsxs)("li",{className:"flex items-start gap-3",children:[(0,d.jsx)(o.A,{className:"h-5 w-5 text-cafe-gold mt-0.5 flex-shrink-0"}),(0,d.jsxs)("div",{className:"text-cafe-beige",children:[(0,d.jsx)("div",{className:"font-medium",children:"Public Holidays"}),(0,d.jsx)("div",{className:"text-sm",children:p.I.hours.holidays})]})]})]})]})]}),(0,d.jsxs)("div",{className:"border-t border-cafe-brown mt-12 pt-8 flex flex-col md:flex-row justify-between items-center",children:[(0,d.jsxs)("p",{className:"text-cafe-beige text-sm",children:["\xa9 ",a," Himalayan Brew Caf\xe9. All rights reserved."]}),(0,d.jsxs)("div",{className:"flex space-x-6 mt-4 md:mt-0",children:[(0,d.jsx)(f(),{href:"/privacy",className:"text-cafe-beige hover:text-cafe-gold text-sm transition-colors duration-200",children:"Privacy Policy"}),(0,d.jsx)(f(),{href:"/terms",className:"text-cafe-beige hover:text-cafe-gold text-sm transition-colors duration-200",children:"Terms of Service"})]})]})]})})}},3690:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{createMutableActionQueue:function(){return o},dispatchNavigateAction:function(){return q},dispatchTraverseAction:function(){return r},getCurrentAppRouterState:function(){return p},publicAppRouterInstance:function(){return s}});let d=c(9154),e=c(8830),f=c(3210),g=c(1992);c(593);let h=c(9129),i=c(6127),j=c(9752),k=c(5076),l=c(3406);function m(a,b){null!==a.pending&&(a.pending=a.pending.next,null!==a.pending?n({actionQueue:a,action:a.pending,setState:b}):a.needsRefresh&&(a.needsRefresh=!1,a.dispatch({type:d.ACTION_REFRESH,origin:window.location.origin},b)))}async function n(a){let{actionQueue:b,action:c,setState:d}=a,e=b.state;b.pending=c;let f=c.payload,h=b.action(e,f);function i(a){c.discarded||(b.state=a,m(b,d),c.resolve(a))}(0,g.isThenable)(h)?h.then(i,a=>{m(b,d),c.reject(a)}):i(h)}function o(a,b){let c={state:a,dispatch:(a,b)=>(function(a,b,c){let e={resolve:c,reject:()=>{}};if(b.type!==d.ACTION_RESTORE){let a=new Promise((a,b)=>{e={resolve:a,reject:b}});(0,f.startTransition)(()=>{c(a)})}let g={payload:b,next:null,resolve:e.resolve,reject:e.reject};null===a.pending?(a.last=g,n({actionQueue:a,action:g,setState:c})):b.type===d.ACTION_NAVIGATE||b.type===d.ACTION_RESTORE?(a.pending.discarded=!0,g.next=a.pending.next,a.pending.payload.type===d.ACTION_SERVER_ACTION&&(a.needsRefresh=!0),n({actionQueue:a,action:g,setState:c})):(null!==a.last&&(a.last.next=g),a.last=g)})(c,a,b),action:async(a,b)=>(0,e.reducer)(a,b),pending:null,last:null,onRouterTransitionStart:null!==b&&"function"==typeof b.onRouterTransitionStart?b.onRouterTransitionStart:null};return c}function p(){return null}function q(a,b,c,e){let f=new URL((0,i.addBasePath)(a),location.href);(0,l.setLinkForCurrentNavigation)(e);(0,h.dispatchAppRouterAction)({type:d.ACTION_NAVIGATE,url:f,isExternalUrl:(0,j.isExternalURL)(f),locationSearch:location.search,shouldScroll:c,navigateType:b,allowAliasing:!0})}function r(a,b){(0,h.dispatchAppRouterAction)({type:d.ACTION_RESTORE,url:new URL(a),tree:b})}let s={back:()=>window.history.back(),forward:()=>window.history.forward(),prefetch:(a,b)=>{let c=function(){throw Object.defineProperty(Error("Internal Next.js error: Router action dispatched before initialization."),"__NEXT_ERROR_CODE",{value:"E668",enumerable:!1,configurable:!0})}(),e=(0,j.createPrefetchURL)(a);if(null!==e){var f;(0,k.prefetchReducer)(c.state,{type:d.ACTION_PREFETCH,url:e,kind:null!=(f=null==b?void 0:b.kind)?f:d.PrefetchKind.FULL})}},replace:(a,b)=>{(0,f.startTransition)(()=>{var c;q(a,"replace",null==(c=null==b?void 0:b.scroll)||c,null)})},push:(a,b)=>{(0,f.startTransition)(()=>{var c;q(a,"push",null==(c=null==b?void 0:b.scroll)||c,null)})},refresh:()=>{(0,f.startTransition)(()=>{(0,h.dispatchAppRouterAction)({type:d.ACTION_REFRESH,origin:window.location.origin})})},hmrRefresh:()=>{throw Object.defineProperty(Error("hmrRefresh can only be used in development mode. Please use refresh instead."),"__NEXT_ERROR_CODE",{value:"E485",enumerable:!1,configurable:!0})}};("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},3802:(a,b,c)=>{"use strict";c.d(b,{ContactSection:()=>d});let d=(0,c(1369).registerClientReference)(function(){throw Error("Attempted to call ContactSection() from the server but ContactSection is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\contact-section.tsx","ContactSection")},3841:()=>{},3873:a=>{"use strict";a.exports=require("path")},3898:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{fillCacheWithNewSubTreeData:function(){return i},fillCacheWithNewSubTreeDataButOnlyLoading:function(){return j}});let d=c(4400),e=c(1500),f=c(3123),g=c(3913);function h(a,b,c,h,i,j){let{segmentPath:k,seedData:l,tree:m,head:n}=h,o=b,p=c;for(let b=0;b<k.length;b+=2){let c=k[b],h=k[b+1],q=b===k.length-2,r=(0,f.createRouterCacheKey)(h),s=p.parallelRoutes.get(c);if(!s)continue;let t=o.parallelRoutes.get(c);t&&t!==s||(t=new Map(s),o.parallelRoutes.set(c,t));let u=s.get(r),v=t.get(r);if(q){if(l&&(!v||!v.lazyData||v===u)){let b=l[0],c=l[1],f=l[3];v={lazyData:null,rsc:j||b!==g.PAGE_SEGMENT_KEY?c:null,prefetchRsc:null,head:null,prefetchHead:null,loading:f,parallelRoutes:j&&u?new Map(u.parallelRoutes):new Map,navigatedAt:a},u&&j&&(0,d.invalidateCacheByRouterState)(v,u,m),j&&(0,e.fillLazyItemsTillLeafWithHead)(a,v,u,m,l,n,i),t.set(r,v)}continue}v&&u&&(v===u&&(v={lazyData:v.lazyData,rsc:v.rsc,prefetchRsc:v.prefetchRsc,head:v.head,prefetchHead:v.prefetchHead,parallelRoutes:new Map(v.parallelRoutes),loading:v.loading},t.set(r,v)),o=v,p=u)}}function i(a,b,c,d,e){h(a,b,c,d,e,!0)}function j(a,b,c,d,e){h(a,b,c,d,e,!1)}("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},3931:(a,b)=>{"use strict";function c(a){let b=a.indexOf("#"),c=a.indexOf("?"),d=c>-1&&(b<0||c<b);return d||b>-1?{pathname:a.substring(0,d?c:b),query:d?a.substring(c,b>-1?b:void 0):"",hash:b>-1?a.slice(b):""}:{pathname:a,query:"",hash:""}}Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"parsePath",{enumerable:!0,get:function(){return c}})},4082:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(2688).A)("leaf",[["path",{d:"M11 20A7 7 0 0 1 9.8 6.1C15.5 5 17 4.48 19 2c1 2 2 4.18 2 8 0 5.5-4.78 10-10 10Z",key:"nnexq3"}],["path",{d:"M2 21c0-3 1.85-5.36 5.08-6C9.5 14.52 12 13 13 12",key:"mt58a7"}]])},4124:(a,b,c)=>{"use strict";let d;c.d(b,{P:()=>fm});var e=c(3210);let f=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],g=new Set(f),h=a=>180*a/Math.PI,i=a=>k(h(Math.atan2(a[1],a[0]))),j={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:a=>(Math.abs(a[0])+Math.abs(a[3]))/2,rotate:i,rotateZ:i,skewX:a=>h(Math.atan(a[1])),skewY:a=>h(Math.atan(a[2])),skew:a=>(Math.abs(a[1])+Math.abs(a[2]))/2},k=a=>((a%=360)<0&&(a+=360),a),l=a=>Math.sqrt(a[0]*a[0]+a[1]*a[1]),m=a=>Math.sqrt(a[4]*a[4]+a[5]*a[5]),n={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:l,scaleY:m,scale:a=>(l(a)+m(a))/2,rotateX:a=>k(h(Math.atan2(a[6],a[5]))),rotateY:a=>k(h(Math.atan2(-a[2],a[0]))),rotateZ:i,rotate:i,skewX:a=>h(Math.atan(a[4])),skewY:a=>h(Math.atan(a[1])),skew:a=>(Math.abs(a[1])+Math.abs(a[4]))/2};function o(a){return+!!a.includes("scale")}function p(a,b){let c,d;if(!a||"none"===a)return o(b);let e=a.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);if(e)c=n,d=e;else{let b=a.match(/^matrix\(([-\d.e\s,]+)\)$/u);c=j,d=b}if(!d)return o(b);let f=c[b],g=d[1].split(",").map(q);return"function"==typeof f?f(g):g[f]}function q(a){return parseFloat(a.trim())}let r=a=>b=>"string"==typeof b&&b.startsWith(a),s=r("--"),t=r("var(--"),u=a=>!!t(a)&&v.test(a.split("/*")[0].trim()),v=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu;function w({top:a,left:b,right:c,bottom:d}){return{x:{min:b,max:c},y:{min:a,max:d}}}let x=(a,b,c)=>a+(b-a)*c;function y(a){return void 0===a||1===a}function z({scale:a,scaleX:b,scaleY:c}){return!y(a)||!y(b)||!y(c)}function A(a){return z(a)||B(a)||a.z||a.rotate||a.rotateX||a.rotateY||a.skewX||a.skewY}function B(a){var b,c;return(b=a.x)&&"0%"!==b||(c=a.y)&&"0%"!==c}function C(a,b,c,d,e){return void 0!==e&&(a=d+e*(a-d)),d+c*(a-d)+b}function D(a,b=0,c=1,d,e){a.min=C(a.min,b,c,d,e),a.max=C(a.max,b,c,d,e)}function E(a,{x:b,y:c}){D(a.x,b.translate,b.scale,b.originPoint),D(a.y,c.translate,c.scale,c.originPoint)}function F(a,b){a.min=a.min+b,a.max=a.max+b}function G(a,b,c,d,e=.5){let f=x(a.min,a.max,e);D(a,b,c,f,d)}function H(a,b){G(a.x,b.x,b.scaleX,b.scale,b.originX),G(a.y,b.y,b.scaleY,b.scale,b.originY)}function I(a,b){return w(function(a,b){if(!b)return a;let c=b({x:a.left,y:a.top}),d=b({x:a.right,y:a.bottom});return{top:c.y,left:c.x,bottom:d.y,right:d.x}}(a.getBoundingClientRect(),b))}let J=new Set(["width","height","top","left","right","bottom",...f]),K=(a,b,c)=>c>b?b:c<a?a:c,L={test:a=>"number"==typeof a,parse:parseFloat,transform:a=>a},M={...L,transform:a=>K(0,1,a)},N={...L,default:1},O=a=>({test:b=>"string"==typeof b&&b.endsWith(a)&&1===b.split(" ").length,parse:parseFloat,transform:b=>`${b}${a}`}),P=O("deg"),Q=O("%"),R=O("px"),S=O("vh"),T=O("vw"),U={...Q,parse:a=>Q.parse(a)/100,transform:a=>Q.transform(100*a)},V=a=>b=>b.test(a),W=[L,R,Q,P,T,S,{test:a=>"auto"===a,parse:a=>a}],X=a=>W.find(V(a)),Y=()=>{},Z=()=>{},$=a=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(a),_=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u,aa=a=>a===L||a===R,ab=new Set(["x","y","z"]),ac=f.filter(a=>!ab.has(a)),ad={width:({x:a},{paddingLeft:b="0",paddingRight:c="0"})=>a.max-a.min-parseFloat(b)-parseFloat(c),height:({y:a},{paddingTop:b="0",paddingBottom:c="0"})=>a.max-a.min-parseFloat(b)-parseFloat(c),top:(a,{top:b})=>parseFloat(b),left:(a,{left:b})=>parseFloat(b),bottom:({y:a},{top:b})=>parseFloat(b)+(a.max-a.min),right:({x:a},{left:b})=>parseFloat(b)+(a.max-a.min),x:(a,{transform:b})=>p(b,"x"),y:(a,{transform:b})=>p(b,"y")};ad.translateX=ad.x,ad.translateY=ad.y;let ae=a=>a,af={},ag=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],ah={value:null,addProjectionMetrics:null};function ai(a,b){let c=!1,d=!0,e={delta:0,timestamp:0,isProcessing:!1},f=()=>c=!0,g=ag.reduce((a,c)=>(a[c]=function(a,b){let c=new Set,d=new Set,e=!1,f=!1,g=new WeakSet,h={delta:0,timestamp:0,isProcessing:!1},i=0;function j(b){g.has(b)&&(k.schedule(b),a()),i++,b(h)}let k={schedule:(a,b=!1,f=!1)=>{let h=f&&e?c:d;return b&&g.add(a),h.has(a)||h.add(a),a},cancel:a=>{d.delete(a),g.delete(a)},process:a=>{if(h=a,e){f=!0;return}e=!0,[c,d]=[d,c],c.forEach(j),b&&ah.value&&ah.value.frameloop[b].push(i),i=0,c.clear(),e=!1,f&&(f=!1,k.process(a))}};return k}(f,b?c:void 0),a),{}),{setup:h,read:i,resolveKeyframes:j,preUpdate:k,update:l,preRender:m,render:n,postRender:o}=g,p=()=>{let f=af.useManualTiming?e.timestamp:performance.now();c=!1,af.useManualTiming||(e.delta=d?1e3/60:Math.max(Math.min(f-e.timestamp,40),1)),e.timestamp=f,e.isProcessing=!0,h.process(e),i.process(e),j.process(e),k.process(e),l.process(e),m.process(e),n.process(e),o.process(e),e.isProcessing=!1,c&&b&&(d=!1,a(p))};return{schedule:ag.reduce((b,f)=>{let h=g[f];return b[f]=(b,f=!1,g=!1)=>(!c&&(c=!0,d=!0,e.isProcessing||a(p)),h.schedule(b,f,g)),b},{}),cancel:a=>{for(let b=0;b<ag.length;b++)g[ag[b]].cancel(a)},state:e,steps:g}}let{schedule:aj,cancel:ak,state:al,steps:am}=ai("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:ae,!0),an=new Set,ao=!1,ap=!1,aq=!1;function ar(){if(ap){let a=Array.from(an).filter(a=>a.needsMeasurement),b=new Set(a.map(a=>a.element)),c=new Map;b.forEach(a=>{let b=function(a){let b=[];return ac.forEach(c=>{let d=a.getValue(c);void 0!==d&&(b.push([c,d.get()]),d.set(+!!c.startsWith("scale")))}),b}(a);b.length&&(c.set(a,b),a.render())}),a.forEach(a=>a.measureInitialState()),b.forEach(a=>{a.render();let b=c.get(a);b&&b.forEach(([b,c])=>{a.getValue(b)?.set(c)})}),a.forEach(a=>a.measureEndState()),a.forEach(a=>{void 0!==a.suspendedScrollY&&window.scrollTo(0,a.suspendedScrollY)})}ap=!1,ao=!1,an.forEach(a=>a.complete(aq)),an.clear()}function as(){an.forEach(a=>{a.readKeyframes(),a.needsMeasurement&&(ap=!0)})}class at{constructor(a,b,c,d,e,f=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...a],this.onComplete=b,this.name=c,this.motionValue=d,this.element=e,this.isAsync=f}scheduleResolve(){this.state="scheduled",this.isAsync?(an.add(this),ao||(ao=!0,aj.read(as),aj.resolveKeyframes(ar))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:a,name:b,element:c,motionValue:d}=this;if(null===a[0]){let e=d?.get(),f=a[a.length-1];if(void 0!==e)a[0]=e;else if(c&&b){let d=c.readValue(b,f);null!=d&&(a[0]=d)}void 0===a[0]&&(a[0]=f),d&&void 0===e&&d.set(a[0])}for(let b=1;b<a.length;b++)a[b]??(a[b]=a[b-1])}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(a=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,a),an.delete(this)}cancel(){"scheduled"===this.state&&(an.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}}let au=a=>/^0[^.\s]+$/u.test(a),av=a=>Math.round(1e5*a)/1e5,aw=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu,ax=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,ay=(a,b)=>c=>!!("string"==typeof c&&ax.test(c)&&c.startsWith(a)||b&&null!=c&&Object.prototype.hasOwnProperty.call(c,b)),az=(a,b,c)=>d=>{if("string"!=typeof d)return d;let[e,f,g,h]=d.match(aw);return{[a]:parseFloat(e),[b]:parseFloat(f),[c]:parseFloat(g),alpha:void 0!==h?parseFloat(h):1}},aA={...L,transform:a=>Math.round(K(0,255,a))},aB={test:ay("rgb","red"),parse:az("red","green","blue"),transform:({red:a,green:b,blue:c,alpha:d=1})=>"rgba("+aA.transform(a)+", "+aA.transform(b)+", "+aA.transform(c)+", "+av(M.transform(d))+")"},aC={test:ay("#"),parse:function(a){let b="",c="",d="",e="";return a.length>5?(b=a.substring(1,3),c=a.substring(3,5),d=a.substring(5,7),e=a.substring(7,9)):(b=a.substring(1,2),c=a.substring(2,3),d=a.substring(3,4),e=a.substring(4,5),b+=b,c+=c,d+=d,e+=e),{red:parseInt(b,16),green:parseInt(c,16),blue:parseInt(d,16),alpha:e?parseInt(e,16)/255:1}},transform:aB.transform},aD={test:ay("hsl","hue"),parse:az("hue","saturation","lightness"),transform:({hue:a,saturation:b,lightness:c,alpha:d=1})=>"hsla("+Math.round(a)+", "+Q.transform(av(b))+", "+Q.transform(av(c))+", "+av(M.transform(d))+")"},aE={test:a=>aB.test(a)||aC.test(a)||aD.test(a),parse:a=>aB.test(a)?aB.parse(a):aD.test(a)?aD.parse(a):aC.parse(a),transform:a=>"string"==typeof a?a:a.hasOwnProperty("red")?aB.transform(a):aD.transform(a),getAnimatableNone:a=>{let b=aE.parse(a);return b.alpha=0,aE.transform(b)}},aF=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu,aG="number",aH="color",aI=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function aJ(a){let b=a.toString(),c=[],d={color:[],number:[],var:[]},e=[],f=0,g=b.replace(aI,a=>(aE.test(a)?(d.color.push(f),e.push(aH),c.push(aE.parse(a))):a.startsWith("var(")?(d.var.push(f),e.push("var"),c.push(a)):(d.number.push(f),e.push(aG),c.push(parseFloat(a))),++f,"${}")).split("${}");return{values:c,split:g,indexes:d,types:e}}function aK(a){return aJ(a).values}function aL(a){let{split:b,types:c}=aJ(a),d=b.length;return a=>{let e="";for(let f=0;f<d;f++)if(e+=b[f],void 0!==a[f]){let b=c[f];b===aG?e+=av(a[f]):b===aH?e+=aE.transform(a[f]):e+=a[f]}return e}}let aM=a=>"number"==typeof a?0:aE.test(a)?aE.getAnimatableNone(a):a,aN={test:function(a){return isNaN(a)&&"string"==typeof a&&(a.match(aw)?.length||0)+(a.match(aF)?.length||0)>0},parse:aK,createTransformer:aL,getAnimatableNone:function(a){let b=aK(a);return aL(a)(b.map(aM))}},aO=new Set(["brightness","contrast","saturate","opacity"]);function aP(a){let[b,c]=a.slice(0,-1).split("(");if("drop-shadow"===b)return a;let[d]=c.match(aw)||[];if(!d)return a;let e=c.replace(d,""),f=+!!aO.has(b);return d!==c&&(f*=100),b+"("+f+e+")"}let aQ=/\b([a-z-]*)\(.*?\)/gu,aR={...aN,getAnimatableNone:a=>{let b=a.match(aQ);return b?b.map(aP).join(" "):a}},aS={...L,transform:Math.round},aT={borderWidth:R,borderTopWidth:R,borderRightWidth:R,borderBottomWidth:R,borderLeftWidth:R,borderRadius:R,radius:R,borderTopLeftRadius:R,borderTopRightRadius:R,borderBottomRightRadius:R,borderBottomLeftRadius:R,width:R,maxWidth:R,height:R,maxHeight:R,top:R,right:R,bottom:R,left:R,padding:R,paddingTop:R,paddingRight:R,paddingBottom:R,paddingLeft:R,margin:R,marginTop:R,marginRight:R,marginBottom:R,marginLeft:R,backgroundPositionX:R,backgroundPositionY:R,rotate:P,rotateX:P,rotateY:P,rotateZ:P,scale:N,scaleX:N,scaleY:N,scaleZ:N,skew:P,skewX:P,skewY:P,distance:R,translateX:R,translateY:R,translateZ:R,x:R,y:R,z:R,perspective:R,transformPerspective:R,opacity:M,originX:U,originY:U,originZ:R,zIndex:aS,fillOpacity:M,strokeOpacity:M,numOctaves:aS},aU={...aT,color:aE,backgroundColor:aE,outlineColor:aE,fill:aE,stroke:aE,borderColor:aE,borderTopColor:aE,borderRightColor:aE,borderBottomColor:aE,borderLeftColor:aE,filter:aR,WebkitFilter:aR},aV=a=>aU[a];function aW(a,b){let c=aV(a);return c!==aR&&(c=aN),c.getAnimatableNone?c.getAnimatableNone(b):void 0}let aX=new Set(["auto","none","0"]);class aY extends at{constructor(a,b,c,d,e){super(a,b,c,d,e,!0)}readKeyframes(){let{unresolvedKeyframes:a,element:b,name:c}=this;if(!b||!b.current)return;super.readKeyframes();for(let c=0;c<a.length;c++){let d=a[c];if("string"==typeof d&&u(d=d.trim())){let e=function a(b,c,d=1){Z(d<=4,`Max CSS variable fallback depth detected in property "${b}". This may indicate a circular fallback dependency.`,"max-css-var-depth");let[e,f]=function(a){let b=_.exec(a);if(!b)return[,];let[,c,d,e]=b;return[`--${c??d}`,e]}(b);if(!e)return;let g=window.getComputedStyle(c).getPropertyValue(e);if(g){let a=g.trim();return $(a)?parseFloat(a):a}return u(f)?a(f,c,d+1):f}(d,b.current);void 0!==e&&(a[c]=e),c===a.length-1&&(this.finalKeyframe=d)}}if(this.resolveNoneKeyframes(),!J.has(c)||2!==a.length)return;let[d,e]=a,f=X(d),g=X(e);if(f!==g)if(aa(f)&&aa(g))for(let b=0;b<a.length;b++){let c=a[b];"string"==typeof c&&(a[b]=parseFloat(c))}else ad[c]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){let{unresolvedKeyframes:a,name:b}=this,c=[];for(let b=0;b<a.length;b++){var d;(null===a[b]||("number"==typeof(d=a[b])?0===d:null===d||"none"===d||"0"===d||au(d)))&&c.push(b)}c.length&&function(a,b,c){let d,e=0;for(;e<a.length&&!d;){let b=a[e];"string"==typeof b&&!aX.has(b)&&aJ(b).values.length&&(d=a[e]),e++}if(d&&c)for(let e of b)a[e]=aW(c,d)}(a,c,b)}measureInitialState(){let{element:a,unresolvedKeyframes:b,name:c}=this;if(!a||!a.current)return;"height"===c&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=ad[c](a.measureViewportBox(),window.getComputedStyle(a.current)),b[0]=this.measuredOrigin;let d=b[b.length-1];void 0!==d&&a.getValue(c,d).jump(d,!1)}measureEndState(){let{element:a,name:b,unresolvedKeyframes:c}=this;if(!a||!a.current)return;let d=a.getValue(b);d&&d.jump(this.measuredOrigin,!1);let e=c.length-1,f=c[e];c[e]=ad[b](a.measureViewportBox(),window.getComputedStyle(a.current)),null!==f&&void 0===this.finalKeyframe&&(this.finalKeyframe=f),this.removedTransforms?.length&&this.removedTransforms.forEach(([b,c])=>{a.getValue(b).set(c)}),this.resolveNoneKeyframes()}}let aZ=a=>!!(a&&a.getVelocity);function a$(){d=void 0}let a_={now:()=>(void 0===d&&a_.set(al.isProcessing||af.useManualTiming?al.timestamp:performance.now()),d),set:a=>{d=a,queueMicrotask(a$)}};function a0(a,b){-1===a.indexOf(b)&&a.push(b)}function a1(a,b){let c=a.indexOf(b);c>-1&&a.splice(c,1)}class a2{constructor(){this.subscriptions=[]}add(a){return a0(this.subscriptions,a),()=>a1(this.subscriptions,a)}notify(a,b,c){let d=this.subscriptions.length;if(d)if(1===d)this.subscriptions[0](a,b,c);else for(let e=0;e<d;e++){let d=this.subscriptions[e];d&&d(a,b,c)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}let a3={current:void 0};class a4{constructor(a,b={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=(a,b=!0)=>{let c=a_.now();if(this.updatedAt!==c&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(a),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(let a of this.dependents)a.dirty();b&&this.events.renderRequest?.notify(this.current)},this.hasAnimated=!1,this.setCurrent(a),this.owner=b.owner}setCurrent(a){this.current=a,this.updatedAt=a_.now(),null===this.canTrackVelocity&&void 0!==a&&(this.canTrackVelocity=!isNaN(parseFloat(this.current)))}setPrevFrameValue(a=this.current){this.prevFrameValue=a,this.prevUpdatedAt=this.updatedAt}onChange(a){return this.on("change",a)}on(a,b){this.events[a]||(this.events[a]=new a2);let c=this.events[a].add(b);return"change"===a?()=>{c(),aj.read(()=>{this.events.change.getSize()||this.stop()})}:c}clearListeners(){for(let a in this.events)this.events[a].clear()}attach(a,b){this.passiveEffect=a,this.stopPassiveEffect=b}set(a,b=!0){b&&this.passiveEffect?this.passiveEffect(a,this.updateAndNotify):this.updateAndNotify(a,b)}setWithVelocity(a,b,c){this.set(b),this.prev=void 0,this.prevFrameValue=a,this.prevUpdatedAt=this.updatedAt-c}jump(a,b=!0){this.updateAndNotify(a),this.prev=a,this.prevUpdatedAt=this.prevFrameValue=void 0,b&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(a){this.dependents||(this.dependents=new Set),this.dependents.add(a)}removeDependent(a){this.dependents&&this.dependents.delete(a)}get(){return a3.current&&a3.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){var a;let b=a_.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||b-this.updatedAt>30)return 0;let c=Math.min(this.updatedAt-this.prevUpdatedAt,30);return a=parseFloat(this.current)-parseFloat(this.prevFrameValue),c?1e3/c*a:0}start(a){return this.stop(),new Promise(b=>{this.hasAnimated=!0,this.animation=a(b),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function a5(a,b){return new a4(a,b)}let a6=[...W,aE,aN],{schedule:a7}=ai(queueMicrotask,!1),a8={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},a9={};for(let a in a8)a9[a]={isEnabled:b=>a8[a].some(a=>!!b[a])};let ba=()=>({translate:0,scale:1,origin:0,originPoint:0}),bb=()=>({x:ba(),y:ba()}),bc=()=>({min:0,max:0}),bd=()=>({x:bc(),y:bc()});var be=c(7044);let bf={current:null},bg={current:!1},bh=new WeakMap;function bi(a){return null!==a&&"object"==typeof a&&"function"==typeof a.start}function bj(a){return"string"==typeof a||Array.isArray(a)}let bk=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],bl=["initial",...bk];function bm(a){return bi(a.animate)||bl.some(b=>bj(a[b]))}function bn(a){return!!(bm(a)||a.variants)}function bo(a){let b=[{},{}];return a?.values.forEach((a,c)=>{b[0][c]=a.get(),b[1][c]=a.getVelocity()}),b}function bp(a,b,c,d){if("function"==typeof b){let[e,f]=bo(d);b=b(void 0!==c?c:a.custom,e,f)}if("string"==typeof b&&(b=a.variants&&a.variants[b]),"function"==typeof b){let[e,f]=bo(d);b=b(void 0!==c?c:a.custom,e,f)}return b}let bq=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class br{scrapeMotionValuesFromProps(a,b,c){return{}}constructor({parent:a,props:b,presenceContext:c,reducedMotionConfig:d,blockInitialAnimation:e,visualState:f},g={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=at,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let a=a_.now();this.renderScheduledAt<a&&(this.renderScheduledAt=a,aj.render(this.render,!1,!0))};let{latestValues:h,renderState:i}=f;this.latestValues=h,this.baseTarget={...h},this.initialValues=b.initial?{...h}:{},this.renderState=i,this.parent=a,this.props=b,this.presenceContext=c,this.depth=a?a.depth+1:0,this.reducedMotionConfig=d,this.options=g,this.blockInitialAnimation=!!e,this.isControllingVariants=bm(b),this.isVariantNode=bn(b),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(a&&a.current);let{willChange:j,...k}=this.scrapeMotionValuesFromProps(b,{},this);for(let a in k){let b=k[a];void 0!==h[a]&&aZ(b)&&b.set(h[a],!1)}}mount(a){this.current=a,bh.set(a,this),this.projection&&!this.projection.instance&&this.projection.mount(a),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((a,b)=>this.bindToMotionValue(b,a)),bg.current||function(){if(bg.current=!0,be.B)if(window.matchMedia){let a=window.matchMedia("(prefers-reduced-motion)"),b=()=>bf.current=a.matches;a.addEventListener("change",b),b()}else bf.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||bf.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let a in this.projection&&this.projection.unmount(),ak(this.notifyUpdate),ak(this.render),this.valueSubscriptions.forEach(a=>a()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[a].clear();for(let a in this.features){let b=this.features[a];b&&(b.unmount(),b.isMounted=!1)}this.current=null}bindToMotionValue(a,b){let c;this.valueSubscriptions.has(a)&&this.valueSubscriptions.get(a)();let d=g.has(a);d&&this.onBindTransform&&this.onBindTransform();let e=b.on("change",b=>{this.latestValues[a]=b,this.props.onUpdate&&aj.preRender(this.notifyUpdate),d&&this.projection&&(this.projection.isTransformDirty=!0)}),f=b.on("renderRequest",this.scheduleRender);window.MotionCheckAppearSync&&(c=window.MotionCheckAppearSync(this,a,b)),this.valueSubscriptions.set(a,()=>{e(),f(),c&&c(),b.owner&&b.stop()})}sortNodePosition(a){return this.current&&this.sortInstanceNodePosition&&this.type===a.type?this.sortInstanceNodePosition(this.current,a.current):0}updateFeatures(){let a="animation";for(a in a9){let b=a9[a];if(!b)continue;let{isEnabled:c,Feature:d}=b;if(!this.features[a]&&d&&c(this.props)&&(this.features[a]=new d(this)),this.features[a]){let b=this.features[a];b.isMounted?b.update():(b.mount(),b.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):bd()}getStaticValue(a){return this.latestValues[a]}setStaticValue(a,b){this.latestValues[a]=b}update(a,b){(a.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=a,this.prevPresenceContext=this.presenceContext,this.presenceContext=b;for(let b=0;b<bq.length;b++){let c=bq[b];this.propEventSubscriptions[c]&&(this.propEventSubscriptions[c](),delete this.propEventSubscriptions[c]);let d=a["on"+c];d&&(this.propEventSubscriptions[c]=this.on(c,d))}this.prevMotionValues=function(a,b,c){for(let d in b){let e=b[d],f=c[d];if(aZ(e))a.addValue(d,e);else if(aZ(f))a.addValue(d,a5(e,{owner:a}));else if(f!==e)if(a.hasValue(d)){let b=a.getValue(d);!0===b.liveStyle?b.jump(e):b.hasAnimated||b.set(e)}else{let b=a.getStaticValue(d);a.addValue(d,a5(void 0!==b?b:e,{owner:a}))}}for(let d in c)void 0===b[d]&&a.removeValue(d);return b}(this,this.scrapeMotionValuesFromProps(a,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(a){return this.props.variants?this.props.variants[a]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(a){let b=this.getClosestVariantNode();if(b)return b.variantChildren&&b.variantChildren.add(a),()=>b.variantChildren.delete(a)}addValue(a,b){let c=this.values.get(a);b!==c&&(c&&this.removeValue(a),this.bindToMotionValue(a,b),this.values.set(a,b),this.latestValues[a]=b.get())}removeValue(a){this.values.delete(a);let b=this.valueSubscriptions.get(a);b&&(b(),this.valueSubscriptions.delete(a)),delete this.latestValues[a],this.removeValueFromRenderState(a,this.renderState)}hasValue(a){return this.values.has(a)}getValue(a,b){if(this.props.values&&this.props.values[a])return this.props.values[a];let c=this.values.get(a);return void 0===c&&void 0!==b&&(c=a5(null===b?void 0:b,{owner:this}),this.addValue(a,c)),c}readValue(a,b){let c=void 0===this.latestValues[a]&&this.current?this.getBaseTargetFromProps(this.props,a)??this.readValueFromInstance(this.current,a,this.options):this.latestValues[a];if(null!=c){if("string"==typeof c&&($(c)||au(c)))c=parseFloat(c);else{let d;d=c,!a6.find(V(d))&&aN.test(b)&&(c=aW(a,b))}this.setBaseTarget(a,aZ(c)?c.get():c)}return aZ(c)?c.get():c}setBaseTarget(a,b){this.baseTarget[a]=b}getBaseTarget(a){let b,{initial:c}=this.props;if("string"==typeof c||"object"==typeof c){let d=bp(this.props,c,this.presenceContext?.custom);d&&(b=d[a])}if(c&&void 0!==b)return b;let d=this.getBaseTargetFromProps(this.props,a);return void 0===d||aZ(d)?void 0!==this.initialValues[a]&&void 0===b?void 0:this.baseTarget[a]:d}on(a,b){return this.events[a]||(this.events[a]=new a2),this.events[a].add(b)}notify(a,...b){this.events[a]&&this.events[a].notify(...b)}scheduleRenderMicrotask(){a7.render(this.render)}}class bs extends br{constructor(){super(...arguments),this.KeyframeResolver=aY}sortInstanceNodePosition(a,b){return 2&a.compareDocumentPosition(b)?1:-1}getBaseTargetFromProps(a,b){return a.style?a.style[b]:void 0}removeValueFromRenderState(a,{vars:b,style:c}){delete b[a],delete c[a]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:a}=this.props;aZ(a)&&(this.childSubscription=a.on("change",a=>{this.current&&(this.current.textContent=`${a}`)}))}}let bt=(a,b)=>b&&"number"==typeof a?b.transform(a):a,bu={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},bv=f.length;function bw(a,b,c){let{style:d,vars:e,transformOrigin:h}=a,i=!1,j=!1;for(let a in b){let c=b[a];if(g.has(a)){i=!0;continue}if(s(a)){e[a]=c;continue}{let b=bt(c,aT[a]);a.startsWith("origin")?(j=!0,h[a]=b):d[a]=b}}if(!b.transform&&(i||c?d.transform=function(a,b,c){let d="",e=!0;for(let g=0;g<bv;g++){let h=f[g],i=a[h];if(void 0===i)continue;let j=!0;if(!(j="number"==typeof i?i===+!!h.startsWith("scale"):0===parseFloat(i))||c){let a=bt(i,aT[h]);if(!j){e=!1;let b=bu[h]||h;d+=`${b}(${a}) `}c&&(b[h]=a)}}return d=d.trim(),c?d=c(b,e?"":d):e&&(d="none"),d}(b,a.transform,c):d.transform&&(d.transform="none")),j){let{originX:a="50%",originY:b="50%",originZ:c=0}=h;d.transformOrigin=`${a} ${b} ${c}`}}function bx(a,{style:b,vars:c},d,e){let f,g=a.style;for(f in b)g[f]=b[f];for(f in e?.applyProjectionStyles(g,d),c)g.setProperty(f,c[f])}let by={};function bz(a,{layout:b,layoutId:c}){return g.has(a)||a.startsWith("origin")||(b||void 0!==c)&&(!!by[a]||"opacity"===a)}function bA(a,b,c){let{style:d}=a,e={};for(let f in d)(aZ(d[f])||b.style&&aZ(b.style[f])||bz(f,a)||c?.getValue(f)?.liveStyle!==void 0)&&(e[f]=d[f]);return e}class bB extends bs{constructor(){super(...arguments),this.type="html",this.renderInstance=bx}readValueFromInstance(a,b){if(g.has(b))return this.projection?.isProjecting?o(b):((a,b)=>{let{transform:c="none"}=getComputedStyle(a);return p(c,b)})(a,b);{let c=window.getComputedStyle(a),d=(s(b)?c.getPropertyValue(b):c[b])||0;return"string"==typeof d?d.trim():d}}measureInstanceViewportBox(a,{transformPagePoint:b}){return I(a,b)}build(a,b,c){bw(a,b,c.transformTemplate)}scrapeMotionValuesFromProps(a,b,c){return bA(a,b,c)}}let bC=a=>a.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),bD={offset:"stroke-dashoffset",array:"stroke-dasharray"},bE={offset:"strokeDashoffset",array:"strokeDasharray"};function bF(a,{attrX:b,attrY:c,attrScale:d,pathLength:e,pathSpacing:f=1,pathOffset:g=0,...h},i,j,k){if(bw(a,h,j),i){a.style.viewBox&&(a.attrs.viewBox=a.style.viewBox);return}a.attrs=a.style,a.style={};let{attrs:l,style:m}=a;l.transform&&(m.transform=l.transform,delete l.transform),(m.transform||l.transformOrigin)&&(m.transformOrigin=l.transformOrigin??"50% 50%",delete l.transformOrigin),m.transform&&(m.transformBox=k?.transformBox??"fill-box",delete l.transformBox),void 0!==b&&(l.x=b),void 0!==c&&(l.y=c),void 0!==d&&(l.scale=d),void 0!==e&&function(a,b,c=1,d=0,e=!0){a.pathLength=1;let f=e?bD:bE;a[f.offset]=R.transform(-d);let g=R.transform(b),h=R.transform(c);a[f.array]=`${g} ${h}`}(l,e,f,g,!1)}let bG=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]),bH=a=>"string"==typeof a&&"svg"===a.toLowerCase();function bI(a,b,c){let d=bA(a,b,c);for(let c in a)(aZ(a[c])||aZ(b[c]))&&(d[-1!==f.indexOf(c)?"attr"+c.charAt(0).toUpperCase()+c.substring(1):c]=a[c]);return d}class bJ extends bs{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=bd}getBaseTargetFromProps(a,b){return a[b]}readValueFromInstance(a,b){if(g.has(b)){let a=aV(b);return a&&a.default||0}return b=bG.has(b)?b:bC(b),a.getAttribute(b)}scrapeMotionValuesFromProps(a,b,c){return bI(a,b,c)}build(a,b,c){bF(a,b,this.isSVGTag,c.transformTemplate,c.style)}renderInstance(a,b,c,d){for(let c in bx(a,b,void 0,d),b.attrs)a.setAttribute(bG.has(c)?c:bC(c),b.attrs[c])}mount(a){this.isSVGTag=bH(a.tagName),super.mount(a)}}let bK=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function bL(a){if("string"!=typeof a||a.includes("-"));else if(bK.indexOf(a)>-1||/[A-Z]/u.test(a))return!0;return!1}var bM=c(687),bN=c(2157);let bO=(0,e.createContext)({strict:!1});var bP=c(2582);let bQ=(0,e.createContext)({});function bR(a){return Array.isArray(a)?a.join(" "):a}let bS=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function bT(a,b,c){for(let d in b)aZ(b[d])||bz(d,c)||(a[d]=b[d])}let bU=()=>({...bS(),attrs:{}}),bV=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function bW(a){return a.startsWith("while")||a.startsWith("drag")&&"draggable"!==a||a.startsWith("layout")||a.startsWith("onTap")||a.startsWith("onPan")||a.startsWith("onLayout")||bV.has(a)}let bX=a=>!bW(a);try{!function(a){"function"==typeof a&&(bX=b=>b.startsWith("on")?!bW(b):a(b))}(require("@emotion/is-prop-valid").default)}catch{}var bY=c(1279),bZ=c(5170);function b$(a){return aZ(a)?a.get():a}let b_=a=>(b,c)=>{let d=(0,e.useContext)(bQ),f=(0,e.useContext)(bY.t),g=()=>(function({scrapeMotionValuesFromProps:a,createRenderState:b},c,d,e){return{latestValues:function(a,b,c,d){let e={},f=d(a,{});for(let a in f)e[a]=b$(f[a]);let{initial:g,animate:h}=a,i=bm(a),j=bn(a);b&&j&&!i&&!1!==a.inherit&&(void 0===g&&(g=b.initial),void 0===h&&(h=b.animate));let k=!!c&&!1===c.initial,l=(k=k||!1===g)?h:g;if(l&&"boolean"!=typeof l&&!bi(l)){let b=Array.isArray(l)?l:[l];for(let c=0;c<b.length;c++){let d=bp(a,b[c]);if(d){let{transitionEnd:a,transition:b,...c}=d;for(let a in c){let b=c[a];if(Array.isArray(b)){let a=k?b.length-1:0;b=b[a]}null!==b&&(e[a]=b)}for(let b in a)e[b]=a[b]}}}return e}(c,d,e,a),renderState:b()}})(a,b,d,f);return c?g():(0,bZ.M)(g)},b0=b_({scrapeMotionValuesFromProps:bA,createRenderState:bS}),b1=b_({scrapeMotionValuesFromProps:bI,createRenderState:bU}),b2=Symbol.for("motionComponentSymbol");function b3(a){return a&&"object"==typeof a&&Object.prototype.hasOwnProperty.call(a,"current")}let b4="data-"+bC("framerAppearId"),b5=(0,e.createContext)({});var b6=c(2743);function b7(a,{forwardMotionProps:b=!1}={},c,d){c&&function(a){for(let b in a)a9[b]={...a9[b],...a[b]}}(c);let f=bL(a)?b1:b0;function g(c,g){var h;let i,j={...(0,e.useContext)(bP.Q),...c,layoutId:function({layoutId:a}){let b=(0,e.useContext)(bN.L).id;return b&&void 0!==a?b+"-"+a:a}(c)},{isStatic:k}=j,l=function(a){let{initial:b,animate:c}=function(a,b){if(bm(a)){let{initial:b,animate:c}=a;return{initial:!1===b||bj(b)?b:void 0,animate:bj(c)?c:void 0}}return!1!==a.inherit?b:{}}(a,(0,e.useContext)(bQ));return(0,e.useMemo)(()=>({initial:b,animate:c}),[bR(b),bR(c)])}(c),m=f(c,k);if(!k&&be.B){(0,e.useContext)(bO).strict;let b=function(a){let{drag:b,layout:c}=a9;if(!b&&!c)return{};let d={...b,...c};return{MeasureLayout:b?.isEnabled(a)||c?.isEnabled(a)?d.MeasureLayout:void 0,ProjectionNode:d.ProjectionNode}}(j);i=b.MeasureLayout,l.visualElement=function(a,b,c,d,f){let{visualElement:g}=(0,e.useContext)(bQ),h=(0,e.useContext)(bO),i=(0,e.useContext)(bY.t),j=(0,e.useContext)(bP.Q).reducedMotion,k=(0,e.useRef)(null);d=d||h.renderer,!k.current&&d&&(k.current=d(a,{visualState:b,parent:g,props:c,presenceContext:i,blockInitialAnimation:!!i&&!1===i.initial,reducedMotionConfig:j}));let l=k.current,m=(0,e.useContext)(b5);l&&!l.projection&&f&&("html"===l.type||"svg"===l.type)&&function(a,b,c,d){let{layoutId:e,layout:f,drag:g,dragConstraints:h,layoutScroll:i,layoutRoot:j,layoutCrossfade:k}=b;a.projection=new c(a.latestValues,b["data-framer-portal-id"]?void 0:function a(b){if(b)return!1!==b.options.allowProjection?b.projection:a(b.parent)}(a.parent)),a.projection.setOptions({layoutId:e,layout:f,alwaysMeasureLayout:!!g||h&&b3(h),visualElement:a,animationType:"string"==typeof f?f:"both",initialPromotionConfig:d,crossfade:k,layoutScroll:i,layoutRoot:j})}(k.current,c,f,m);let n=(0,e.useRef)(!1);(0,e.useInsertionEffect)(()=>{l&&n.current&&l.update(c,i)});let o=c[b4],p=(0,e.useRef)(!!o&&!window.MotionHandoffIsComplete?.(o)&&window.MotionHasOptimisedAnimation?.(o));return(0,b6.E)(()=>{l&&(n.current=!0,window.MotionIsMounted=!0,l.updateFeatures(),l.scheduleRenderMicrotask(),p.current&&l.animationState&&l.animationState.animateChanges())}),(0,e.useEffect)(()=>{l&&(!p.current&&l.animationState&&l.animationState.animateChanges(),p.current&&(queueMicrotask(()=>{window.MotionHandoffMarkAsComplete?.(o)}),p.current=!1))}),l}(a,m,j,d,b.ProjectionNode)}return(0,bM.jsxs)(bQ.Provider,{value:l,children:[i&&l.visualElement?(0,bM.jsx)(i,{visualElement:l.visualElement,...j}):null,function(a,b,c,{latestValues:d},f,g=!1){let h=(bL(a)?function(a,b,c,d){let f=(0,e.useMemo)(()=>{let c=bU();return bF(c,b,bH(d),a.transformTemplate,a.style),{...c.attrs,style:{...c.style}}},[b]);if(a.style){let b={};bT(b,a.style,a),f.style={...b,...f.style}}return f}:function(a,b){let c={},d=function(a,b){let c=a.style||{},d={};return bT(d,c,a),Object.assign(d,function({transformTemplate:a},b){return(0,e.useMemo)(()=>{let c=bS();return bw(c,b,a),Object.assign({},c.vars,c.style)},[b])}(a,b)),d}(a,b);return a.drag&&!1!==a.dragListener&&(c.draggable=!1,d.userSelect=d.WebkitUserSelect=d.WebkitTouchCallout="none",d.touchAction=!0===a.drag?"none":`pan-${"x"===a.drag?"y":"x"}`),void 0===a.tabIndex&&(a.onTap||a.onTapStart||a.whileTap)&&(c.tabIndex=0),c.style=d,c})(b,d,f,a),i=function(a,b,c){let d={};for(let e in a)("values"!==e||"object"!=typeof a.values)&&(bX(e)||!0===c&&bW(e)||!b&&!bW(e)||a.draggable&&e.startsWith("onDrag"))&&(d[e]=a[e]);return d}(b,"string"==typeof a,g),j=a!==e.Fragment?{...i,...h,ref:c}:{},{children:k}=b,l=(0,e.useMemo)(()=>aZ(k)?k.get():k,[k]);return(0,e.createElement)(a,{...j,children:l})}(a,c,(h=l.visualElement,(0,e.useCallback)(a=>{a&&m.onMount&&m.onMount(a),h&&(a?h.mount(a):h.unmount()),g&&("function"==typeof g?g(a):b3(g)&&(g.current=a))},[h])),m,k,b)]})}g.displayName=`motion.${"string"==typeof a?a:`create(${a.displayName??a.name??""})`}`;let h=(0,e.forwardRef)(g);return h[b2]=a,h}function b8(a,b,c){let d=a.getProps();return bp(d,b,void 0!==c?c:d.custom,a)}function b9(a,b){return a?.[b]??a?.default??a}let ca=a=>Array.isArray(a);function cb(a,b){let c=a.getValue("willChange");if(aZ(c)&&c.add)return c.add(b);if(!c&&af.WillChange){let c=new af.WillChange("auto");a.addValue("willChange",c),c.add(b)}}let cc=(a,b)=>c=>b(a(c)),cd=(...a)=>a.reduce(cc),ce=a=>1e3*a,cf={layout:0,mainThread:0,waapi:0};function cg(a,b,c){return(c<0&&(c+=1),c>1&&(c-=1),c<1/6)?a+(b-a)*6*c:c<.5?b:c<2/3?a+(b-a)*(2/3-c)*6:a}function ch(a,b){return c=>c>0?b:a}let ci=(a,b,c)=>{let d=a*a,e=c*(b*b-d)+d;return e<0?0:Math.sqrt(e)},cj=[aC,aB,aD];function ck(a){let b=cj.find(b=>b.test(a));if(Y(!!b,`'${a}' is not an animatable color. Use the equivalent color code instead.`,"color-not-animatable"),!b)return!1;let c=b.parse(a);return b===aD&&(c=function({hue:a,saturation:b,lightness:c,alpha:d}){a/=360,c/=100;let e=0,f=0,g=0;if(b/=100){let d=c<.5?c*(1+b):c+b-c*b,h=2*c-d;e=cg(h,d,a+1/3),f=cg(h,d,a),g=cg(h,d,a-1/3)}else e=f=g=c;return{red:Math.round(255*e),green:Math.round(255*f),blue:Math.round(255*g),alpha:d}}(c)),c}let cl=(a,b)=>{let c=ck(a),d=ck(b);if(!c||!d)return ch(a,b);let e={...c};return a=>(e.red=ci(c.red,d.red,a),e.green=ci(c.green,d.green,a),e.blue=ci(c.blue,d.blue,a),e.alpha=x(c.alpha,d.alpha,a),aB.transform(e))},cm=new Set(["none","hidden"]);function cn(a,b){return c=>x(a,b,c)}function co(a){return"number"==typeof a?cn:"string"==typeof a?u(a)?ch:aE.test(a)?cl:cr:Array.isArray(a)?cp:"object"==typeof a?aE.test(a)?cl:cq:ch}function cp(a,b){let c=[...a],d=c.length,e=a.map((a,c)=>co(a)(a,b[c]));return a=>{for(let b=0;b<d;b++)c[b]=e[b](a);return c}}function cq(a,b){let c={...a,...b},d={};for(let e in c)void 0!==a[e]&&void 0!==b[e]&&(d[e]=co(a[e])(a[e],b[e]));return a=>{for(let b in d)c[b]=d[b](a);return c}}let cr=(a,b)=>{let c=aN.createTransformer(b),d=aJ(a),e=aJ(b);return d.indexes.var.length===e.indexes.var.length&&d.indexes.color.length===e.indexes.color.length&&d.indexes.number.length>=e.indexes.number.length?cm.has(a)&&!e.values.length||cm.has(b)&&!d.values.length?function(a,b){return cm.has(a)?c=>c<=0?a:b:c=>c>=1?b:a}(a,b):cd(cp(function(a,b){let c=[],d={color:0,var:0,number:0};for(let e=0;e<b.values.length;e++){let f=b.types[e],g=a.indexes[f][d[f]],h=a.values[g]??0;c[e]=h,d[f]++}return c}(d,e),e.values),c):(Y(!0,`Complex values '${a}' and '${b}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`,"complex-values-different"),ch(a,b))};function cs(a,b,c){return"number"==typeof a&&"number"==typeof b&&"number"==typeof c?x(a,b,c):co(a)(a,b)}let ct=a=>{let b=({timestamp:b})=>a(b);return{start:(a=!0)=>aj.update(b,a),stop:()=>ak(b),now:()=>al.isProcessing?al.timestamp:a_.now()}},cu=(a,b,c=10)=>{let d="",e=Math.max(Math.round(b/c),2);for(let b=0;b<e;b++)d+=Math.round(1e4*a(b/(e-1)))/1e4+", ";return`linear(${d.substring(0,d.length-2)})`};function cv(a){let b=0,c=a.next(b);for(;!c.done&&b<2e4;)b+=50,c=a.next(b);return b>=2e4?1/0:b}function cw(a,b,c){var d,e;let f=Math.max(b-5,0);return d=c-a(f),(e=b-f)?1e3/e*d:0}let cx={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};function cy(a,b){return a*Math.sqrt(1-b*b)}let cz=["duration","bounce"],cA=["stiffness","damping","mass"];function cB(a,b){return b.some(b=>void 0!==a[b])}function cC(a=cx.visualDuration,b=cx.bounce){let c,d="object"!=typeof a?{visualDuration:a,keyframes:[0,1],bounce:b}:a,{restSpeed:e,restDelta:f}=d,g=d.keyframes[0],h=d.keyframes[d.keyframes.length-1],i={done:!1,value:g},{stiffness:j,damping:k,mass:l,duration:m,velocity:n,isResolvedFromDuration:o}=function(a){let b={velocity:cx.velocity,stiffness:cx.stiffness,damping:cx.damping,mass:cx.mass,isResolvedFromDuration:!1,...a};if(!cB(a,cA)&&cB(a,cz))if(a.visualDuration){let c=2*Math.PI/(1.2*a.visualDuration),d=c*c,e=2*K(.05,1,1-(a.bounce||0))*Math.sqrt(d);b={...b,mass:cx.mass,stiffness:d,damping:e}}else{let c=function({duration:a=cx.duration,bounce:b=cx.bounce,velocity:c=cx.velocity,mass:d=cx.mass}){let e,f;Y(a<=ce(cx.maxDuration),"Spring duration must be 10 seconds or less","spring-duration-limit");let g=1-b;g=K(cx.minDamping,cx.maxDamping,g),a=K(cx.minDuration,cx.maxDuration,a/1e3),g<1?(e=b=>{let d=b*g,e=d*a;return .001-(d-c)/cy(b,g)*Math.exp(-e)},f=b=>{let d=b*g*a,f=Math.pow(g,2)*Math.pow(b,2)*a,h=Math.exp(-d),i=cy(Math.pow(b,2),g);return(d*c+c-f)*h*(-e(b)+.001>0?-1:1)/i}):(e=b=>-.001+Math.exp(-b*a)*((b-c)*a+1),f=b=>a*a*(c-b)*Math.exp(-b*a));let h=function(a,b,c){let d=c;for(let c=1;c<12;c++)d-=a(d)/b(d);return d}(e,f,5/a);if(a=ce(a),isNaN(h))return{stiffness:cx.stiffness,damping:cx.damping,duration:a};{let b=Math.pow(h,2)*d;return{stiffness:b,damping:2*g*Math.sqrt(d*b),duration:a}}}(a);(b={...b,...c,mass:cx.mass}).isResolvedFromDuration=!0}return b}({...d,velocity:-((d.velocity||0)/1e3)}),p=n||0,q=k/(2*Math.sqrt(j*l)),r=h-g,s=Math.sqrt(j/l)/1e3,t=5>Math.abs(r);if(e||(e=t?cx.restSpeed.granular:cx.restSpeed.default),f||(f=t?cx.restDelta.granular:cx.restDelta.default),q<1){let a=cy(s,q);c=b=>h-Math.exp(-q*s*b)*((p+q*s*r)/a*Math.sin(a*b)+r*Math.cos(a*b))}else if(1===q)c=a=>h-Math.exp(-s*a)*(r+(p+s*r)*a);else{let a=s*Math.sqrt(q*q-1);c=b=>{let c=Math.exp(-q*s*b),d=Math.min(a*b,300);return h-c*((p+q*s*r)*Math.sinh(d)+a*r*Math.cosh(d))/a}}let u={calculatedDuration:o&&m||null,next:a=>{let b=c(a);if(o)i.done=a>=m;else{let d=0===a?p:0;q<1&&(d=0===a?ce(p):cw(c,a,b));let g=Math.abs(h-b)<=f;i.done=Math.abs(d)<=e&&g}return i.value=i.done?h:b,i},toString:()=>{let a=Math.min(cv(u),2e4),b=cu(b=>u.next(a*b).value,a,30);return a+"ms "+b},toTransition:()=>{}};return u}function cD({keyframes:a,velocity:b=0,power:c=.8,timeConstant:d=325,bounceDamping:e=10,bounceStiffness:f=500,modifyTarget:g,min:h,max:i,restDelta:j=.5,restSpeed:k}){let l,m,n=a[0],o={done:!1,value:n},p=c*b,q=n+p,r=void 0===g?q:g(q);r!==q&&(p=r-n);let s=a=>-p*Math.exp(-a/d),t=a=>r+s(a),u=a=>{let b=s(a),c=t(a);o.done=Math.abs(b)<=j,o.value=o.done?r:c},v=a=>{let b;if(b=o.value,void 0!==h&&b<h||void 0!==i&&b>i){var c;l=a,m=cC({keyframes:[o.value,(c=o.value,void 0===h?i:void 0===i||Math.abs(h-c)<Math.abs(i-c)?h:i)],velocity:cw(t,a,o.value),damping:e,stiffness:f,restDelta:j,restSpeed:k})}};return v(0),{calculatedDuration:null,next:a=>{let b=!1;return(m||void 0!==l||(b=!0,u(a),v(a)),void 0!==l&&a>=l)?m.next(a-l):(b||u(a),o)}}}cC.applyToOptions=a=>{let b=function(a,b=100,c){let d=c({...a,keyframes:[0,b]}),e=Math.min(cv(d),2e4);return{type:"keyframes",ease:a=>d.next(e*a).value/b,duration:e/1e3}}(a,100,cC);return a.ease=b.ease,a.duration=ce(b.duration),a.type="keyframes",a};let cE=(a,b,c)=>(((1-3*c+3*b)*a+(3*c-6*b))*a+3*b)*a;function cF(a,b,c,d){return a===b&&c===d?ae:e=>0===e||1===e?e:cE(function(a,b,c,d,e){let f,g,h=0;do(f=cE(g=b+(c-b)/2,d,e)-a)>0?c=g:b=g;while(Math.abs(f)>1e-7&&++h<12);return g}(e,0,1,a,c),b,d)}let cG=cF(.42,0,1,1),cH=cF(0,0,.58,1),cI=cF(.42,0,.58,1),cJ=a=>b=>b<=.5?a(2*b)/2:(2-a(2*(1-b)))/2,cK=a=>b=>1-a(1-b),cL=cF(.33,1.53,.69,.99),cM=cK(cL),cN=cJ(cM),cO=a=>(a*=2)<1?.5*cM(a):.5*(2-Math.pow(2,-10*(a-1))),cP=a=>1-Math.sin(Math.acos(a)),cQ=cK(cP),cR=cJ(cP),cS=a=>Array.isArray(a)&&"number"==typeof a[0],cT={linear:ae,easeIn:cG,easeInOut:cI,easeOut:cH,circIn:cP,circInOut:cR,circOut:cQ,backIn:cM,backInOut:cN,backOut:cL,anticipate:cO},cU=a=>{if(cS(a)){Z(4===a.length,"Cubic bezier arrays must contain four numerical values.","cubic-bezier-length");let[b,c,d,e]=a;return cF(b,c,d,e)}return"string"==typeof a?(Z(void 0!==cT[a],`Invalid easing type '${a}'`,"invalid-easing-type"),cT[a]):a},cV=(a,b,c)=>{let d=b-a;return 0===d?1:(c-a)/d};function cW({duration:a=300,keyframes:b,times:c,ease:d="easeInOut"}){var e;let f=Array.isArray(d)&&"number"!=typeof d[0]?d.map(cU):cU(d),g={done:!1,value:b[0]},h=function(a,b,{clamp:c=!0,ease:d,mixer:e}={}){let f=a.length;if(Z(f===b.length,"Both input and output ranges must be the same length","range-length"),1===f)return()=>b[0];if(2===f&&b[0]===b[1])return()=>b[1];let g=a[0]===a[1];a[0]>a[f-1]&&(a=[...a].reverse(),b=[...b].reverse());let h=function(a,b,c){let d=[],e=c||af.mix||cs,f=a.length-1;for(let c=0;c<f;c++){let f=e(a[c],a[c+1]);b&&(f=cd(Array.isArray(b)?b[c]||ae:b,f)),d.push(f)}return d}(b,d,e),i=h.length,j=c=>{if(g&&c<a[0])return b[0];let d=0;if(i>1)for(;d<a.length-2&&!(c<a[d+1]);d++);let e=cV(a[d],a[d+1],c);return h[d](e)};return c?b=>j(K(a[0],a[f-1],b)):j}((e=c&&c.length===b.length?c:function(a){let b=[0];return!function(a,b){let c=a[a.length-1];for(let d=1;d<=b;d++){let e=cV(0,b,d);a.push(x(c,1,e))}}(b,a.length-1),b}(b),e.map(b=>b*a)),b,{ease:Array.isArray(f)?f:b.map(()=>f||cI).splice(0,b.length-1)});return{calculatedDuration:a,next:b=>(g.value=h(b),g.done=b>=a,g)}}let cX=a=>null!==a;function cY(a,{repeat:b,repeatType:c="loop"},d,e=1){let f=a.filter(cX),g=e<0||b&&"loop"!==c&&b%2==1?0:f.length-1;return g&&void 0!==d?d:f[g]}let cZ={decay:cD,inertia:cD,tween:cW,keyframes:cW,spring:cC};function c$(a){"string"==typeof a.type&&(a.type=cZ[a.type])}class c_{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(a=>{this.resolve=a})}notifyFinished(){this.resolve()}then(a,b){return this.finished.then(a,b)}}let c0=a=>a/100;class c1 extends c_{constructor(a){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{let{motionValue:a}=this.options;a&&a.updatedAt!==a_.now()&&this.tick(a_.now()),this.isStopped=!0,"idle"!==this.state&&(this.teardown(),this.options.onStop?.())},cf.mainThread++,this.options=a,this.initAnimation(),this.play(),!1===a.autoplay&&this.pause()}initAnimation(){let{options:a}=this;c$(a);let{type:b=cW,repeat:c=0,repeatDelay:d=0,repeatType:e,velocity:f=0}=a,{keyframes:g}=a,h=b||cW;h!==cW&&"number"!=typeof g[0]&&(this.mixKeyframes=cd(c0,cs(g[0],g[1])),g=[0,100]);let i=h({...a,keyframes:g});"mirror"===e&&(this.mirroredGenerator=h({...a,keyframes:[...g].reverse(),velocity:-f})),null===i.calculatedDuration&&(i.calculatedDuration=cv(i));let{calculatedDuration:j}=i;this.calculatedDuration=j,this.resolvedDuration=j+d,this.totalDuration=this.resolvedDuration*(c+1)-d,this.generator=i}updateTime(a){let b=Math.round(a-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=b}tick(a,b=!1){let{generator:c,totalDuration:d,mixKeyframes:e,mirroredGenerator:f,resolvedDuration:g,calculatedDuration:h}=this;if(null===this.startTime)return c.next(0);let{delay:i=0,keyframes:j,repeat:k,repeatType:l,repeatDelay:m,type:n,onUpdate:o,finalKeyframe:p}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,a):this.speed<0&&(this.startTime=Math.min(a-d/this.speed,this.startTime)),b?this.currentTime=a:this.updateTime(a);let q=this.currentTime-i*(this.playbackSpeed>=0?1:-1),r=this.playbackSpeed>=0?q<0:q>d;this.currentTime=Math.max(q,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=d);let s=this.currentTime,t=c;if(k){let a=Math.min(this.currentTime,d)/g,b=Math.floor(a),c=a%1;!c&&a>=1&&(c=1),1===c&&b--,(b=Math.min(b,k+1))%2&&("reverse"===l?(c=1-c,m&&(c-=m/g)):"mirror"===l&&(t=f)),s=K(0,1,c)*g}let u=r?{done:!1,value:j[0]}:t.next(s);e&&(u.value=e(u.value));let{done:v}=u;r||null===h||(v=this.playbackSpeed>=0?this.currentTime>=d:this.currentTime<=0);let w=null===this.holdTime&&("finished"===this.state||"running"===this.state&&v);return w&&n!==cD&&(u.value=cY(j,this.options,p,this.speed)),o&&o(u.value),w&&this.finish(),u}then(a,b){return this.finished.then(a,b)}get duration(){return this.calculatedDuration/1e3}get time(){return this.currentTime/1e3}set time(a){a=ce(a),this.currentTime=a,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=a:this.driver&&(this.startTime=this.driver.now()-a/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(a){this.updateTime(a_.now());let b=this.playbackSpeed!==a;this.playbackSpeed=a,b&&(this.time=this.currentTime/1e3)}play(){if(this.isStopped)return;let{driver:a=ct,startTime:b}=this.options;this.driver||(this.driver=a(a=>this.tick(a))),this.options.onPlay?.();let c=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=c):null!==this.holdTime?this.startTime=c-this.holdTime:this.startTime||(this.startTime=b??c),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(a_.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state="finished",this.options.onComplete?.()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),this.options.onCancel?.()}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null,cf.mainThread--}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(a){return this.startTime=0,this.tick(a,!0)}attachTimeline(a){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),a.observe(this)}}function c2(a){let b;return()=>(void 0===b&&(b=a()),b)}let c3=c2(()=>void 0!==window.ScrollTimeline),c4={},c5=function(a,b){let c=c2(a);return()=>c4[b]??c()}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(a){return!1}return!0},"linearEasing"),c6=([a,b,c,d])=>`cubic-bezier(${a}, ${b}, ${c}, ${d})`,c7={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:c6([0,.65,.55,1]),circOut:c6([.55,0,1,.45]),backIn:c6([.31,.01,.66,-.59]),backOut:c6([.33,1.53,.69,.99])};function c8(a){return"function"==typeof a&&"applyToOptions"in a}class c9 extends c_{constructor(a){if(super(),this.finishedTime=null,this.isStopped=!1,!a)return;let{element:b,name:c,keyframes:d,pseudoElement:e,allowFlatten:f=!1,finalKeyframe:g,onComplete:h}=a;this.isPseudoElement=!!e,this.allowFlatten=f,this.options=a,Z("string"!=typeof a.type,'Mini animate() doesn\'t support "type" as a string.',"mini-spring");let i=function({type:a,...b}){return c8(a)&&c5()?a.applyToOptions(b):(b.duration??(b.duration=300),b.ease??(b.ease="easeOut"),b)}(a);this.animation=function(a,b,c,{delay:d=0,duration:e=300,repeat:f=0,repeatType:g="loop",ease:h="easeOut",times:i}={},j){let k={[b]:c};i&&(k.offset=i);let l=function a(b,c){if(b)return"function"==typeof b?c5()?cu(b,c):"ease-out":cS(b)?c6(b):Array.isArray(b)?b.map(b=>a(b,c)||c7.easeOut):c7[b]}(h,e);Array.isArray(l)&&(k.easing=l),ah.value&&cf.waapi++;let m={delay:d,duration:e,easing:Array.isArray(l)?"linear":l,fill:"both",iterations:f+1,direction:"reverse"===g?"alternate":"normal"};j&&(m.pseudoElement=j);let n=a.animate(k,m);return ah.value&&n.finished.finally(()=>{cf.waapi--}),n}(b,c,d,i,e),!1===i.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!e){let a=cY(d,this.options,g,this.speed);this.updateMotionValue?this.updateMotionValue(a):function(a,b,c){b.startsWith("--")?a.style.setProperty(b,c):a.style[b]=c}(b,c,a),this.animation.cancel()}h?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(a){}}stop(){if(this.isStopped)return;this.isStopped=!0;let{state:a}=this;"idle"!==a&&"finished"!==a&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){return Number(this.animation.effect?.getComputedTiming?.().duration||0)/1e3}get time(){return(Number(this.animation.currentTime)||0)/1e3}set time(a){this.finishedTime=null,this.animation.currentTime=ce(a)}get speed(){return this.animation.playbackRate}set speed(a){a<0&&(this.finishedTime=null),this.animation.playbackRate=a}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(a){this.animation.startTime=a}attachTimeline({timeline:a,observe:b}){return(this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,a&&c3())?(this.animation.timeline=a,ae):b(this)}}let da={anticipate:cO,backInOut:cN,circInOut:cR};class db extends c9{constructor(a){!function(a){"string"==typeof a.ease&&a.ease in da&&(a.ease=da[a.ease])}(a),c$(a),super(a),a.startTime&&(this.startTime=a.startTime),this.options=a}updateMotionValue(a){let{motionValue:b,onUpdate:c,onComplete:d,element:e,...f}=this.options;if(!b)return;if(void 0!==a)return void b.set(a);let g=new c1({...f,autoplay:!1}),h=ce(this.finishedTime??this.time);b.setWithVelocity(g.sample(h-10).value,g.sample(h).value,10),g.stop()}}let dc=(a,b)=>"zIndex"!==b&&!!("number"==typeof a||Array.isArray(a)||"string"==typeof a&&(aN.test(a)||"0"===a)&&!a.startsWith("url(")),dd=new Set(["opacity","clipPath","filter","transform"]),de=c2(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));class df extends c_{constructor({autoplay:a=!0,delay:b=0,type:c="keyframes",repeat:d=0,repeatDelay:e=0,repeatType:f="loop",keyframes:g,name:h,motionValue:i,element:j,...k}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=a_.now();let l={autoplay:a,delay:b,type:c,repeat:d,repeatDelay:e,repeatType:f,name:h,motionValue:i,element:j,...k},m=j?.KeyframeResolver||at;this.keyframeResolver=new m(g,(a,b,c)=>this.onKeyframesResolved(a,b,l,!c),h,i,j),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(a,b,c,d){this.keyframeResolver=void 0;let{name:e,type:f,velocity:g,delay:h,isHandoff:i,onUpdate:j}=c;this.resolvedAt=a_.now(),!function(a,b,c,d){let e=a[0];if(null===e)return!1;if("display"===b||"visibility"===b)return!0;let f=a[a.length-1],g=dc(e,b),h=dc(f,b);return Y(g===h,`You are trying to animate ${b} from "${e}" to "${f}". "${g?f:e}" is not an animatable value.`,"value-not-animatable"),!!g&&!!h&&(function(a){let b=a[0];if(1===a.length)return!0;for(let c=0;c<a.length;c++)if(a[c]!==b)return!0}(a)||("spring"===c||c8(c))&&d)}(a,e,f,g)&&((af.instantAnimations||!h)&&j?.(cY(a,c,b)),a[0]=a[a.length-1],c.duration=0,c.repeat=0);let k={startTime:d?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:b,...c,keyframes:a},l=!i&&function(a){let{motionValue:b,name:c,repeatDelay:d,repeatType:e,damping:f,type:g}=a;if(!(b?.owner?.current instanceof HTMLElement))return!1;let{onUpdate:h,transformTemplate:i}=b.owner.getProps();return de()&&c&&dd.has(c)&&("transform"!==c||!i)&&!h&&!d&&"mirror"!==e&&0!==f&&"inertia"!==g}(k)?new db({...k,element:k.motionValue.owner.current}):new c1(k);l.finished.then(()=>this.notifyFinished()).catch(ae),this.pendingTimeline&&(this.stopTimeline=l.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=l}get finished(){return this._animation?this.animation.finished:this._finished}then(a,b){return this.finished.finally(a).then(()=>{})}get animation(){return this._animation||(this.keyframeResolver?.resume(),aq=!0,as(),ar(),aq=!1),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(a){this.animation.time=a}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(a){this.animation.speed=a}get startTime(){return this.animation.startTime}attachTimeline(a){return this._animation?this.stopTimeline=this.animation.attachTimeline(a):this.pendingTimeline=a,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}let dg=a=>null!==a,dh={type:"spring",stiffness:500,damping:25,restSpeed:10},di={type:"keyframes",duration:.8},dj={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},dk=(a,b,c,d={},e,f)=>h=>{let i=b9(d,a)||{},j=i.delay||d.delay||0,{elapsed:k=0}=d;k-=ce(j);let l={keyframes:Array.isArray(c)?c:[null,c],ease:"easeOut",velocity:b.getVelocity(),...i,delay:-k,onUpdate:a=>{b.set(a),i.onUpdate&&i.onUpdate(a)},onComplete:()=>{h(),i.onComplete&&i.onComplete()},name:a,motionValue:b,element:f?void 0:e};!function({when:a,delay:b,delayChildren:c,staggerChildren:d,staggerDirection:e,repeat:f,repeatType:g,repeatDelay:h,from:i,elapsed:j,...k}){return!!Object.keys(k).length}(i)&&Object.assign(l,((a,{keyframes:b})=>b.length>2?di:g.has(a)?a.startsWith("scale")?{type:"spring",stiffness:550,damping:0===b[1]?2*Math.sqrt(550):30,restSpeed:10}:dh:dj)(a,l)),l.duration&&(l.duration=ce(l.duration)),l.repeatDelay&&(l.repeatDelay=ce(l.repeatDelay)),void 0!==l.from&&(l.keyframes[0]=l.from);let m=!1;if(!1!==l.type&&(0!==l.duration||l.repeatDelay)||(l.duration=0,0===l.delay&&(m=!0)),(af.instantAnimations||af.skipAnimations)&&(m=!0,l.duration=0,l.delay=0),l.allowFlatten=!i.type&&!i.ease,m&&!f&&void 0!==b.get()){let a=function(a,{repeat:b,repeatType:c="loop"},d){let e=a.filter(dg),f=b&&"loop"!==c&&b%2==1?0:e.length-1;return e[f]}(l.keyframes,i);if(void 0!==a)return void aj.update(()=>{l.onUpdate(a),l.onComplete()})}return i.isSync?new c1(l):new df(l)};function dl(a,b,{delay:c=0,transitionOverride:d,type:e}={}){let{transition:f=a.getDefaultTransition(),transitionEnd:g,...h}=b;d&&(f=d);let i=[],j=e&&a.animationState&&a.animationState.getState()[e];for(let b in h){let d=a.getValue(b,a.latestValues[b]??null),e=h[b];if(void 0===e||j&&function({protectedKeys:a,needsAnimating:b},c){let d=a.hasOwnProperty(c)&&!0!==b[c];return b[c]=!1,d}(j,b))continue;let g={delay:c,...b9(f||{},b)},k=d.get();if(void 0!==k&&!d.isAnimating&&!Array.isArray(e)&&e===k&&!g.velocity)continue;let l=!1;if(window.MotionHandoffAnimation){let c=a.props[b4];if(c){let a=window.MotionHandoffAnimation(c,b,aj);null!==a&&(g.startTime=a,l=!0)}}cb(a,b),d.start(dk(b,d,e,a.shouldReduceMotion&&J.has(b)?{type:!1}:g,a,l));let m=d.animation;m&&i.push(m)}return g&&Promise.all(i).then(()=>{aj.update(()=>{g&&function(a,b){let{transitionEnd:c={},transition:d={},...e}=b8(a,b)||{};for(let b in e={...e,...c}){var f;let c=ca(f=e[b])?f[f.length-1]||0:f;a.hasValue(b)?a.getValue(b).set(c):a.addValue(b,a5(c))}}(a,g)})}),i}function dm(a,b,c={}){let d=b8(a,b,"exit"===c.type?a.presenceContext?.custom:void 0),{transition:e=a.getDefaultTransition()||{}}=d||{};c.transitionOverride&&(e=c.transitionOverride);let f=d?()=>Promise.all(dl(a,d,c)):()=>Promise.resolve(),g=a.variantChildren&&a.variantChildren.size?(d=0)=>{let{delayChildren:f=0,staggerChildren:g,staggerDirection:h}=e;return function(a,b,c=0,d=0,e=0,f=1,g){let h=[],i=a.variantChildren.size,j=(i-1)*e,k="function"==typeof d,l=k?a=>d(a,i):1===f?(a=0)=>a*e:(a=0)=>j-a*e;return Array.from(a.variantChildren).sort(dn).forEach((a,e)=>{a.notify("AnimationStart",b),h.push(dm(a,b,{...g,delay:c+(k?0:d)+l(e)}).then(()=>a.notify("AnimationComplete",b)))}),Promise.all(h)}(a,b,d,f,g,h,c)}:()=>Promise.resolve(),{when:h}=e;if(!h)return Promise.all([f(),g(c.delay)]);{let[a,b]="beforeChildren"===h?[f,g]:[g,f];return a().then(()=>b())}}function dn(a,b){return a.sortNodePosition(b)}function dp(a,b){if(!Array.isArray(b))return!1;let c=b.length;if(c!==a.length)return!1;for(let d=0;d<c;d++)if(b[d]!==a[d])return!1;return!0}let dq=bl.length,dr=[...bk].reverse(),ds=bk.length;function dt(a=!1){return{isActive:a,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function du(){return{animate:dt(!0),whileInView:dt(),whileHover:dt(),whileTap:dt(),whileDrag:dt(),whileFocus:dt(),exit:dt()}}class dv{constructor(a){this.isMounted=!1,this.node=a}update(){}}class dw extends dv{constructor(a){super(a),a.animationState||(a.animationState=function(a){let b=b=>Promise.all(b.map(({animation:b,options:c})=>(function(a,b,c={}){let d;if(a.notify("AnimationStart",b),Array.isArray(b))d=Promise.all(b.map(b=>dm(a,b,c)));else if("string"==typeof b)d=dm(a,b,c);else{let e="function"==typeof b?b8(a,b,c.custom):b;d=Promise.all(dl(a,e,c))}return d.then(()=>{a.notify("AnimationComplete",b)})})(a,b,c))),c=du(),d=!0,e=b=>(c,d)=>{let e=b8(a,d,"exit"===b?a.presenceContext?.custom:void 0);if(e){let{transition:a,transitionEnd:b,...d}=e;c={...c,...d,...b}}return c};function f(f){let{props:g}=a,h=function a(b){if(!b)return;if(!b.isControllingVariants){let c=b.parent&&a(b.parent)||{};return void 0!==b.props.initial&&(c.initial=b.props.initial),c}let c={};for(let a=0;a<dq;a++){let d=bl[a],e=b.props[d];(bj(e)||!1===e)&&(c[d]=e)}return c}(a.parent)||{},i=[],j=new Set,k={},l=1/0;for(let b=0;b<ds;b++){var m,n;let o=dr[b],p=c[o],q=void 0!==g[o]?g[o]:h[o],r=bj(q),s=o===f?p.isActive:null;!1===s&&(l=b);let t=q===h[o]&&q!==g[o]&&r;if(t&&d&&a.manuallyAnimateOnMount&&(t=!1),p.protectedKeys={...k},!p.isActive&&null===s||!q&&!p.prevProp||bi(q)||"boolean"==typeof q)continue;let u=(m=p.prevProp,"string"==typeof(n=q)?n!==m:!!Array.isArray(n)&&!dp(n,m)),v=u||o===f&&p.isActive&&!t&&r||b>l&&r,w=!1,x=Array.isArray(q)?q:[q],y=x.reduce(e(o),{});!1===s&&(y={});let{prevResolvedValues:z={}}=p,A={...z,...y},B=b=>{v=!0,j.has(b)&&(w=!0,j.delete(b)),p.needsAnimating[b]=!0;let c=a.getValue(b);c&&(c.liveStyle=!1)};for(let a in A){let b=y[a],c=z[a];if(!k.hasOwnProperty(a))(ca(b)&&ca(c)?dp(b,c):b===c)?void 0!==b&&j.has(a)?B(a):p.protectedKeys[a]=!0:null!=b?B(a):j.add(a)}p.prevProp=q,p.prevResolvedValues=y,p.isActive&&(k={...k,...y}),d&&a.blockInitialAnimation&&(v=!1);let C=!(t&&u)||w;v&&C&&i.push(...x.map(a=>({animation:a,options:{type:o}})))}if(j.size){let b={};if("boolean"!=typeof g.initial){let c=b8(a,Array.isArray(g.initial)?g.initial[0]:g.initial);c&&c.transition&&(b.transition=c.transition)}j.forEach(c=>{let d=a.getBaseTarget(c),e=a.getValue(c);e&&(e.liveStyle=!0),b[c]=d??null}),i.push({animation:b})}let o=!!i.length;return d&&(!1===g.initial||g.initial===g.animate)&&!a.manuallyAnimateOnMount&&(o=!1),d=!1,o?b(i):Promise.resolve()}return{animateChanges:f,setActive:function(b,d){if(c[b].isActive===d)return Promise.resolve();a.variantChildren?.forEach(a=>a.animationState?.setActive(b,d)),c[b].isActive=d;let e=f(b);for(let a in c)c[a].protectedKeys={};return e},setAnimateFunction:function(c){b=c(a)},getState:()=>c,reset:()=>{c=du(),d=!0}}}(a))}updateAnimationControlsSubscription(){let{animate:a}=this.node.getProps();bi(a)&&(this.unmountControls=a.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:a}=this.node.getProps(),{animate:b}=this.node.prevProps||{};a!==b&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}let dx=0;class dy extends dv{constructor(){super(...arguments),this.id=dx++}update(){if(!this.node.presenceContext)return;let{isPresent:a,onExitComplete:b}=this.node.presenceContext,{isPresent:c}=this.node.prevPresenceContext||{};if(!this.node.animationState||a===c)return;let d=this.node.animationState.setActive("exit",!a);b&&!a&&d.then(()=>{b(this.id)})}mount(){let{register:a,onExitComplete:b}=this.node.presenceContext||{};b&&b(this.id),a&&(this.unmount=a(this.id))}unmount(){}}let dz={x:!1,y:!1};function dA(a,b,c,d={passive:!0}){return a.addEventListener(b,c,d),()=>a.removeEventListener(b,c)}let dB=a=>"mouse"===a.pointerType?"number"!=typeof a.button||a.button<=0:!1!==a.isPrimary;function dC(a){return{point:{x:a.pageX,y:a.pageY}}}function dD(a,b,c,d){return dA(a,b,a=>dB(a)&&c(a,dC(a)),d)}function dE(a){return a.max-a.min}function dF(a,b,c,d=.5){a.origin=d,a.originPoint=x(b.min,b.max,a.origin),a.scale=dE(c)/dE(b),a.translate=x(c.min,c.max,a.origin)-a.originPoint,(a.scale>=.9999&&a.scale<=1.0001||isNaN(a.scale))&&(a.scale=1),(a.translate>=-.01&&a.translate<=.01||isNaN(a.translate))&&(a.translate=0)}function dG(a,b,c,d){dF(a.x,b.x,c.x,d?d.originX:void 0),dF(a.y,b.y,c.y,d?d.originY:void 0)}function dH(a,b,c){a.min=c.min+b.min,a.max=a.min+dE(b)}function dI(a,b,c){a.min=b.min-c.min,a.max=a.min+dE(b)}function dJ(a,b,c){dI(a.x,b.x,c.x),dI(a.y,b.y,c.y)}function dK(a){return[a("x"),a("y")]}let dL=({current:a})=>a?a.ownerDocument.defaultView:null,dM=(a,b)=>Math.abs(a-b);class dN{constructor(a,b,{transformPagePoint:c,contextWindow:d=window,dragSnapToOrigin:e=!1,distanceThreshold:f=3}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let a=dQ(this.lastMoveEventInfo,this.history),b=null!==this.startEvent,c=function(a,b){return Math.sqrt(dM(a.x,b.x)**2+dM(a.y,b.y)**2)}(a.offset,{x:0,y:0})>=this.distanceThreshold;if(!b&&!c)return;let{point:d}=a,{timestamp:e}=al;this.history.push({...d,timestamp:e});let{onStart:f,onMove:g}=this.handlers;b||(f&&f(this.lastMoveEvent,a),this.startEvent=this.lastMoveEvent),g&&g(this.lastMoveEvent,a)},this.handlePointerMove=(a,b)=>{this.lastMoveEvent=a,this.lastMoveEventInfo=dO(b,this.transformPagePoint),aj.update(this.updatePoint,!0)},this.handlePointerUp=(a,b)=>{this.end();let{onEnd:c,onSessionEnd:d,resumeAnimation:e}=this.handlers;if(this.dragSnapToOrigin&&e&&e(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let f=dQ("pointercancel"===a.type?this.lastMoveEventInfo:dO(b,this.transformPagePoint),this.history);this.startEvent&&c&&c(a,f),d&&d(a,f)},!dB(a))return;this.dragSnapToOrigin=e,this.handlers=b,this.transformPagePoint=c,this.distanceThreshold=f,this.contextWindow=d||window;let g=dO(dC(a),this.transformPagePoint),{point:h}=g,{timestamp:i}=al;this.history=[{...h,timestamp:i}];let{onSessionStart:j}=b;j&&j(a,dQ(g,this.history)),this.removeListeners=cd(dD(this.contextWindow,"pointermove",this.handlePointerMove),dD(this.contextWindow,"pointerup",this.handlePointerUp),dD(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(a){this.handlers=a}end(){this.removeListeners&&this.removeListeners(),ak(this.updatePoint)}}function dO(a,b){return b?{point:b(a.point)}:a}function dP(a,b){return{x:a.x-b.x,y:a.y-b.y}}function dQ({point:a},b){return{point:a,delta:dP(a,dR(b)),offset:dP(a,b[0]),velocity:function(a,b){if(a.length<2)return{x:0,y:0};let c=a.length-1,d=null,e=dR(a);for(;c>=0&&(d=a[c],!(e.timestamp-d.timestamp>ce(.1)));)c--;if(!d)return{x:0,y:0};let f=(e.timestamp-d.timestamp)/1e3;if(0===f)return{x:0,y:0};let g={x:(e.x-d.x)/f,y:(e.y-d.y)/f};return g.x===1/0&&(g.x=0),g.y===1/0&&(g.y=0),g}(b,.1)}}function dR(a){return a[a.length-1]}function dS(a,b,c){return{min:void 0!==b?a.min+b:void 0,max:void 0!==c?a.max+c-(a.max-a.min):void 0}}function dT(a,b){let c=b.min-a.min,d=b.max-a.max;return b.max-b.min<a.max-a.min&&([c,d]=[d,c]),{min:c,max:d}}function dU(a,b,c){return{min:dV(a,b),max:dV(a,c)}}function dV(a,b){return"number"==typeof a?a:a[b]||0}let dW=new WeakMap;class dX{constructor(a){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=bd(),this.latestPointerEvent=null,this.latestPanInfo=null,this.visualElement=a}start(a,{snapToCursor:b=!1,distanceThreshold:c}={}){let{presenceContext:d}=this.visualElement;if(d&&!1===d.isPresent)return;let{dragSnapToOrigin:e}=this.getProps();this.panSession=new dN(a,{onSessionStart:a=>{let{dragSnapToOrigin:c}=this.getProps();c?this.pauseAnimation():this.stopAnimation(),b&&this.snapToCursor(dC(a).point)},onStart:(a,b)=>{let{drag:c,dragPropagation:d,onDragStart:e}=this.getProps();if(c&&!d&&(this.openDragLock&&this.openDragLock(),this.openDragLock=function(a){if("x"===a||"y"===a)if(dz[a])return null;else return dz[a]=!0,()=>{dz[a]=!1};return dz.x||dz.y?null:(dz.x=dz.y=!0,()=>{dz.x=dz.y=!1})}(c),!this.openDragLock))return;this.latestPointerEvent=a,this.latestPanInfo=b,this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),dK(a=>{let b=this.getAxisMotionValue(a).get()||0;if(Q.test(b)){let{projection:c}=this.visualElement;if(c&&c.layout){let d=c.layout.layoutBox[a];d&&(b=dE(d)*(parseFloat(b)/100))}}this.originPoint[a]=b}),e&&aj.postRender(()=>e(a,b)),cb(this.visualElement,"transform");let{animationState:f}=this.visualElement;f&&f.setActive("whileDrag",!0)},onMove:(a,b)=>{this.latestPointerEvent=a,this.latestPanInfo=b;let{dragPropagation:c,dragDirectionLock:d,onDirectionLock:e,onDrag:f}=this.getProps();if(!c&&!this.openDragLock)return;let{offset:g}=b;if(d&&null===this.currentDirection){this.currentDirection=function(a,b=10){let c=null;return Math.abs(a.y)>b?c="y":Math.abs(a.x)>b&&(c="x"),c}(g),null!==this.currentDirection&&e&&e(this.currentDirection);return}this.updateAxis("x",b.point,g),this.updateAxis("y",b.point,g),this.visualElement.render(),f&&f(a,b)},onSessionEnd:(a,b)=>{this.latestPointerEvent=a,this.latestPanInfo=b,this.stop(a,b),this.latestPointerEvent=null,this.latestPanInfo=null},resumeAnimation:()=>dK(a=>"paused"===this.getAnimationState(a)&&this.getAxisMotionValue(a).animation?.play())},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:e,distanceThreshold:c,contextWindow:dL(this.visualElement)})}stop(a,b){let c=a||this.latestPointerEvent,d=b||this.latestPanInfo,e=this.isDragging;if(this.cancel(),!e||!d||!c)return;let{velocity:f}=d;this.startAnimation(f);let{onDragEnd:g}=this.getProps();g&&aj.postRender(()=>g(c,d))}cancel(){this.isDragging=!1;let{projection:a,animationState:b}=this.visualElement;a&&(a.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:c}=this.getProps();!c&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),b&&b.setActive("whileDrag",!1)}updateAxis(a,b,c){let{drag:d}=this.getProps();if(!c||!dY(a,d,this.currentDirection))return;let e=this.getAxisMotionValue(a),f=this.originPoint[a]+c[a];this.constraints&&this.constraints[a]&&(f=function(a,{min:b,max:c},d){return void 0!==b&&a<b?a=d?x(b,a,d.min):Math.max(a,b):void 0!==c&&a>c&&(a=d?x(c,a,d.max):Math.min(a,c)),a}(f,this.constraints[a],this.elastic[a])),e.set(f)}resolveConstraints(){let{dragConstraints:a,dragElastic:b}=this.getProps(),c=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,d=this.constraints;a&&b3(a)?this.constraints||(this.constraints=this.resolveRefConstraints()):a&&c?this.constraints=function(a,{top:b,left:c,bottom:d,right:e}){return{x:dS(a.x,c,e),y:dS(a.y,b,d)}}(c.layoutBox,a):this.constraints=!1,this.elastic=function(a=.35){return!1===a?a=0:!0===a&&(a=.35),{x:dU(a,"left","right"),y:dU(a,"top","bottom")}}(b),d!==this.constraints&&c&&this.constraints&&!this.hasMutatedConstraints&&dK(a=>{!1!==this.constraints&&this.getAxisMotionValue(a)&&(this.constraints[a]=function(a,b){let c={};return void 0!==b.min&&(c.min=b.min-a.min),void 0!==b.max&&(c.max=b.max-a.min),c}(c.layoutBox[a],this.constraints[a]))})}resolveRefConstraints(){var a;let{dragConstraints:b,onMeasureDragConstraints:c}=this.getProps();if(!b||!b3(b))return!1;let d=b.current;Z(null!==d,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.","drag-constraints-ref");let{projection:e}=this.visualElement;if(!e||!e.layout)return!1;let f=function(a,b,c){let d=I(a,c),{scroll:e}=b;return e&&(F(d.x,e.offset.x),F(d.y,e.offset.y)),d}(d,e.root,this.visualElement.getTransformPagePoint()),g=(a=e.layout.layoutBox,{x:dT(a.x,f.x),y:dT(a.y,f.y)});if(c){let a=c(function({x:a,y:b}){return{top:b.min,right:a.max,bottom:b.max,left:a.min}}(g));this.hasMutatedConstraints=!!a,a&&(g=w(a))}return g}startAnimation(a){let{drag:b,dragMomentum:c,dragElastic:d,dragTransition:e,dragSnapToOrigin:f,onDragTransitionEnd:g}=this.getProps(),h=this.constraints||{};return Promise.all(dK(g=>{if(!dY(g,b,this.currentDirection))return;let i=h&&h[g]||{};f&&(i={min:0,max:0});let j={type:"inertia",velocity:c?a[g]:0,bounceStiffness:d?200:1e6,bounceDamping:d?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...e,...i};return this.startAxisValueAnimation(g,j)})).then(g)}startAxisValueAnimation(a,b){let c=this.getAxisMotionValue(a);return cb(this.visualElement,a),c.start(dk(a,c,0,b,this.visualElement,!1))}stopAnimation(){dK(a=>this.getAxisMotionValue(a).stop())}pauseAnimation(){dK(a=>this.getAxisMotionValue(a).animation?.pause())}getAnimationState(a){return this.getAxisMotionValue(a).animation?.state}getAxisMotionValue(a){let b=`_drag${a.toUpperCase()}`,c=this.visualElement.getProps();return c[b]||this.visualElement.getValue(a,(c.initial?c.initial[a]:void 0)||0)}snapToCursor(a){dK(b=>{let{drag:c}=this.getProps();if(!dY(b,c,this.currentDirection))return;let{projection:d}=this.visualElement,e=this.getAxisMotionValue(b);if(d&&d.layout){let{min:c,max:f}=d.layout.layoutBox[b];e.set(a[b]-x(c,f,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:a,dragConstraints:b}=this.getProps(),{projection:c}=this.visualElement;if(!b3(b)||!c||!this.constraints)return;this.stopAnimation();let d={x:0,y:0};dK(a=>{let b=this.getAxisMotionValue(a);if(b&&!1!==this.constraints){let c=b.get();d[a]=function(a,b){let c=.5,d=dE(a),e=dE(b);return e>d?c=cV(b.min,b.max-d,a.min):d>e&&(c=cV(a.min,a.max-e,b.min)),K(0,1,c)}({min:c,max:c},this.constraints[a])}});let{transformTemplate:e}=this.visualElement.getProps();this.visualElement.current.style.transform=e?e({},""):"none",c.root&&c.root.updateScroll(),c.updateLayout(),this.resolveConstraints(),dK(b=>{if(!dY(b,a,null))return;let c=this.getAxisMotionValue(b),{min:e,max:f}=this.constraints[b];c.set(x(e,f,d[b]))})}addListeners(){if(!this.visualElement.current)return;dW.set(this.visualElement,this);let a=dD(this.visualElement.current,"pointerdown",a=>{let{drag:b,dragListener:c=!0}=this.getProps();b&&c&&this.start(a)}),b=()=>{let{dragConstraints:a}=this.getProps();b3(a)&&a.current&&(this.constraints=this.resolveRefConstraints())},{projection:c}=this.visualElement,d=c.addEventListener("measure",b);c&&!c.layout&&(c.root&&c.root.updateScroll(),c.updateLayout()),aj.read(b);let e=dA(window,"resize",()=>this.scalePositionWithinConstraints()),f=c.addEventListener("didUpdate",({delta:a,hasLayoutChanged:b})=>{this.isDragging&&b&&(dK(b=>{let c=this.getAxisMotionValue(b);c&&(this.originPoint[b]+=a[b].translate,c.set(c.get()+a[b].translate))}),this.visualElement.render())});return()=>{e(),a(),d(),f&&f()}}getProps(){let a=this.visualElement.getProps(),{drag:b=!1,dragDirectionLock:c=!1,dragPropagation:d=!1,dragConstraints:e=!1,dragElastic:f=.35,dragMomentum:g=!0}=a;return{...a,drag:b,dragDirectionLock:c,dragPropagation:d,dragConstraints:e,dragElastic:f,dragMomentum:g}}}function dY(a,b,c){return(!0===b||b===a)&&(null===c||c===a)}class dZ extends dv{constructor(a){super(a),this.removeGroupControls=ae,this.removeListeners=ae,this.controls=new dX(a)}mount(){let{dragControls:a}=this.node.getProps();a&&(this.removeGroupControls=a.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||ae}unmount(){this.removeGroupControls(),this.removeListeners()}}let d$=a=>(b,c)=>{a&&aj.postRender(()=>a(b,c))};class d_ extends dv{constructor(){super(...arguments),this.removePointerDownListener=ae}onPointerDown(a){this.session=new dN(a,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:dL(this.node)})}createPanHandlers(){let{onPanSessionStart:a,onPanStart:b,onPan:c,onPanEnd:d}=this.node.getProps();return{onSessionStart:d$(a),onStart:d$(b),onMove:c,onEnd:(a,b)=>{delete this.session,d&&aj.postRender(()=>d(a,b))}}}mount(){this.removePointerDownListener=dD(this.node.current,"pointerdown",a=>this.onPointerDown(a))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}var d0=c(6044);let d1={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function d2(a,b){return b.max===b.min?0:a/(b.max-b.min)*100}let d3={correct:(a,b)=>{if(!b.target)return a;if("string"==typeof a)if(!R.test(a))return a;else a=parseFloat(a);let c=d2(a,b.target.x),d=d2(a,b.target.y);return`${c}% ${d}%`}},d4=!1;class d5 extends e.Component{componentDidMount(){let{visualElement:a,layoutGroup:b,switchLayoutGroup:c,layoutId:d}=this.props,{projection:e}=a;for(let a in d7)by[a]=d7[a],s(a)&&(by[a].isCSSVariable=!0);e&&(b.group&&b.group.add(e),c&&c.register&&d&&c.register(e),d4&&e.root.didUpdate(),e.addEventListener("animationComplete",()=>{this.safeToRemove()}),e.setOptions({...e.options,onExitComplete:()=>this.safeToRemove()})),d1.hasEverUpdated=!0}getSnapshotBeforeUpdate(a){let{layoutDependency:b,visualElement:c,drag:d,isPresent:e}=this.props,{projection:f}=c;return f&&(f.isPresent=e,d4=!0,d||a.layoutDependency!==b||void 0===b||a.isPresent!==e?f.willUpdate():this.safeToRemove(),a.isPresent!==e&&(e?f.promote():f.relegate()||aj.postRender(()=>{let a=f.getStack();a&&a.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:a}=this.props.visualElement;a&&(a.root.didUpdate(),a7.postRender(()=>{!a.currentAnimation&&a.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:a,layoutGroup:b,switchLayoutGroup:c}=this.props,{projection:d}=a;d&&(d.scheduleCheckAfterUnmount(),b&&b.group&&b.group.remove(d),c&&c.deregister&&c.deregister(d))}safeToRemove(){let{safeToRemove:a}=this.props;a&&a()}render(){return null}}function d6(a){let[b,c]=(0,d0.xQ)(),d=(0,e.useContext)(bN.L);return(0,bM.jsx)(d5,{...a,layoutGroup:d,switchLayoutGroup:(0,e.useContext)(b5),isPresent:b,safeToRemove:c})}let d7={borderRadius:{...d3,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:d3,borderTopRightRadius:d3,borderBottomLeftRadius:d3,borderBottomRightRadius:d3,boxShadow:{correct:(a,{treeScale:b,projectionDelta:c})=>{let d=aN.parse(a);if(d.length>5)return a;let e=aN.createTransformer(a),f=+("number"!=typeof d[0]),g=c.x.scale*b.x,h=c.y.scale*b.y;d[0+f]/=g,d[1+f]/=h;let i=x(g,h,.5);return"number"==typeof d[2+f]&&(d[2+f]/=i),"number"==typeof d[3+f]&&(d[3+f]/=i),e(d)}}};var d8=c(4479);function d9(a){return(0,d8.G)(a)&&"ownerSVGElement"in a}let ea=(a,b)=>a.depth-b.depth;class eb{constructor(){this.children=[],this.isDirty=!1}add(a){a0(this.children,a),this.isDirty=!0}remove(a){a1(this.children,a),this.isDirty=!0}forEach(a){this.isDirty&&this.children.sort(ea),this.isDirty=!1,this.children.forEach(a)}}let ec=["TopLeft","TopRight","BottomLeft","BottomRight"],ed=ec.length,ee=a=>"string"==typeof a?parseFloat(a):a,ef=a=>"number"==typeof a||R.test(a);function eg(a,b){return void 0!==a[b]?a[b]:a.borderRadius}let eh=ej(0,.5,cQ),ei=ej(.5,.95,ae);function ej(a,b,c){return d=>d<a?0:d>b?1:c(cV(a,b,d))}function ek(a,b){a.min=b.min,a.max=b.max}function el(a,b){ek(a.x,b.x),ek(a.y,b.y)}function em(a,b){a.translate=b.translate,a.scale=b.scale,a.originPoint=b.originPoint,a.origin=b.origin}function en(a,b,c,d,e){return a-=b,a=d+1/c*(a-d),void 0!==e&&(a=d+1/e*(a-d)),a}function eo(a,b,[c,d,e],f,g){!function(a,b=0,c=1,d=.5,e,f=a,g=a){if(Q.test(b)&&(b=parseFloat(b),b=x(g.min,g.max,b/100)-g.min),"number"!=typeof b)return;let h=x(f.min,f.max,d);a===f&&(h-=b),a.min=en(a.min,b,c,h,e),a.max=en(a.max,b,c,h,e)}(a,b[c],b[d],b[e],b.scale,f,g)}let ep=["x","scaleX","originX"],eq=["y","scaleY","originY"];function er(a,b,c,d){eo(a.x,b,ep,c?c.x:void 0,d?d.x:void 0),eo(a.y,b,eq,c?c.y:void 0,d?d.y:void 0)}function es(a){return 0===a.translate&&1===a.scale}function et(a){return es(a.x)&&es(a.y)}function eu(a,b){return a.min===b.min&&a.max===b.max}function ev(a,b){return Math.round(a.min)===Math.round(b.min)&&Math.round(a.max)===Math.round(b.max)}function ew(a,b){return ev(a.x,b.x)&&ev(a.y,b.y)}function ex(a){return dE(a.x)/dE(a.y)}function ey(a,b){return a.translate===b.translate&&a.scale===b.scale&&a.originPoint===b.originPoint}class ez{constructor(){this.members=[]}add(a){a0(this.members,a),a.scheduleRender()}remove(a){if(a1(this.members,a),a===this.prevLead&&(this.prevLead=void 0),a===this.lead){let a=this.members[this.members.length-1];a&&this.promote(a)}}relegate(a){let b,c=this.members.findIndex(b=>a===b);if(0===c)return!1;for(let a=c;a>=0;a--){let c=this.members[a];if(!1!==c.isPresent){b=c;break}}return!!b&&(this.promote(b),!0)}promote(a,b){let c=this.lead;if(a!==c&&(this.prevLead=c,this.lead=a,a.show(),c)){c.instance&&c.scheduleRender(),a.scheduleRender(),a.resumeFrom=c,b&&(a.resumeFrom.preserveOpacity=!0),c.snapshot&&(a.snapshot=c.snapshot,a.snapshot.latestValues=c.animationValues||c.latestValues),a.root&&a.root.isUpdating&&(a.isLayoutDirty=!0);let{crossfade:d}=a.options;!1===d&&c.hide()}}exitAnimationComplete(){this.members.forEach(a=>{let{options:b,resumingFrom:c}=a;b.onExitComplete&&b.onExitComplete(),c&&c.options.onExitComplete&&c.options.onExitComplete()})}scheduleRender(){this.members.forEach(a=>{a.instance&&a.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}let eA={nodes:0,calculatedTargetDeltas:0,calculatedProjections:0},eB=["","X","Y","Z"],eC=0;function eD(a,b,c,d){let{latestValues:e}=b;e[a]&&(c[a]=e[a],b.setStaticValue(a,0),d&&(d[a]=0))}function eE({attachResizeListener:a,defaultParent:b,measureScroll:c,checkIsScrollRoot:d,resetTransform:e}){return class{constructor(a={},c=b?.()){this.id=eC++,this.animationId=0,this.animationCommitId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,ah.value&&(eA.nodes=eA.calculatedTargetDeltas=eA.calculatedProjections=0),this.nodes.forEach(eH),this.nodes.forEach(eO),this.nodes.forEach(eP),this.nodes.forEach(eI),ah.addProjectionMetrics&&ah.addProjectionMetrics(eA)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=a,this.root=c?c.root||c:this,this.path=c?[...c.path,c]:[],this.parent=c,this.depth=c?c.depth+1:0;for(let a=0;a<this.path.length;a++)this.path[a].shouldResetTransform=!0;this.root===this&&(this.nodes=new eb)}addEventListener(a,b){return this.eventHandlers.has(a)||this.eventHandlers.set(a,new a2),this.eventHandlers.get(a).add(b)}notifyListeners(a,...b){let c=this.eventHandlers.get(a);c&&c.notify(...b)}hasListeners(a){return this.eventHandlers.has(a)}mount(b){if(this.instance)return;this.isSVG=d9(b)&&!(d9(b)&&"svg"===b.tagName),this.instance=b;let{layoutId:c,layout:d,visualElement:e}=this.options;if(e&&!e.current&&e.mount(b),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(d||c)&&(this.isLayoutDirty=!0),a){let c,d=0,e=()=>this.root.updateBlockedByResize=!1;aj.read(()=>{d=window.innerWidth}),a(b,()=>{let a=window.innerWidth;a!==d&&(d=a,this.root.updateBlockedByResize=!0,c&&c(),c=function(a,b){let c=a_.now(),d=({timestamp:b})=>{let e=b-c;e>=250&&(ak(d),a(e-250))};return aj.setup(d,!0),()=>ak(d)}(e,250),d1.hasAnimatedSinceResize&&(d1.hasAnimatedSinceResize=!1,this.nodes.forEach(eN)))})}c&&this.root.registerSharedNode(c,this),!1!==this.options.animate&&e&&(c||d)&&this.addEventListener("didUpdate",({delta:a,hasLayoutChanged:b,hasRelativeLayoutChanged:c,layout:d})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let f=this.options.transition||e.getDefaultTransition()||eV,{onLayoutAnimationStart:g,onLayoutAnimationComplete:h}=e.getProps(),i=!this.targetLayout||!ew(this.targetLayout,d),j=!b&&c;if(this.options.layoutRoot||this.resumeFrom||j||b&&(i||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);let b={...b9(f,"layout"),onPlay:g,onComplete:h};(e.shouldReduceMotion||this.options.layoutRoot)&&(b.delay=0,b.type=!1),this.startAnimation(b),this.setAnimationOrigin(a,j)}else b||eN(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=d})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let a=this.getStack();a&&a.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),ak(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(eQ),this.animationId++)}getTransformTemplate(){let{visualElement:a}=this.options;return a&&a.getProps().transformTemplate}willUpdate(a=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function a(b){if(b.hasCheckedOptimisedAppear=!0,b.root===b)return;let{visualElement:c}=b.options;if(!c)return;let d=c.props[b4];if(window.MotionHasOptimisedAnimation(d,"transform")){let{layout:a,layoutId:c}=b.options;window.MotionCancelOptimisedAnimation(d,"transform",aj,!(a||c))}let{parent:e}=b;e&&!e.hasCheckedOptimisedAppear&&a(e)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let a=0;a<this.path.length;a++){let b=this.path[a];b.shouldResetTransform=!0,b.updateScroll("snapshot"),b.options.layoutRoot&&b.willUpdate(!1)}let{layoutId:b,layout:c}=this.options;if(void 0===b&&!c)return;let d=this.getTransformTemplate();this.prevTransformTemplateValue=d?d(this.latestValues,""):void 0,this.updateSnapshot(),a&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(eK);return}if(this.animationId<=this.animationCommitId)return void this.nodes.forEach(eL);this.animationCommitId=this.animationId,this.isUpdating?(this.isUpdating=!1,this.nodes.forEach(eM),this.nodes.forEach(eF),this.nodes.forEach(eG)):this.nodes.forEach(eL),this.clearAllSnapshots();let a=a_.now();al.delta=K(0,1e3/60,a-al.timestamp),al.timestamp=a,al.isProcessing=!0,am.update.process(al),am.preRender.process(al),am.render.process(al),al.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,a7.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(eJ),this.sharedNodes.forEach(eR)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,aj.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){aj.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure(),!this.snapshot||dE(this.snapshot.measuredBox.x)||dE(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let a=0;a<this.path.length;a++)this.path[a].updateScroll();let a=this.layout;this.layout=this.measure(!1),this.layoutCorrected=bd(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:b}=this.options;b&&b.notify("LayoutMeasure",this.layout.layoutBox,a?a.layoutBox:void 0)}updateScroll(a="measure"){let b=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===a&&(b=!1),b&&this.instance){let b=d(this.instance);this.scroll={animationId:this.root.animationId,phase:a,isRoot:b,offset:c(this.instance),wasRoot:this.scroll?this.scroll.isRoot:b}}}resetTransform(){if(!e)return;let a=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,b=this.projectionDelta&&!et(this.projectionDelta),c=this.getTransformTemplate(),d=c?c(this.latestValues,""):void 0,f=d!==this.prevTransformTemplateValue;a&&this.instance&&(b||A(this.latestValues)||f)&&(e(this.instance,d),this.shouldResetTransform=!1,this.scheduleRender())}measure(a=!0){var b;let c=this.measurePageBox(),d=this.removeElementScroll(c);return a&&(d=this.removeTransform(d)),eY((b=d).x),eY(b.y),{animationId:this.root.animationId,measuredBox:c,layoutBox:d,latestValues:{},source:this.id}}measurePageBox(){let{visualElement:a}=this.options;if(!a)return bd();let b=a.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(e$))){let{scroll:a}=this.root;a&&(F(b.x,a.offset.x),F(b.y,a.offset.y))}return b}removeElementScroll(a){let b=bd();if(el(b,a),this.scroll?.wasRoot)return b;for(let c=0;c<this.path.length;c++){let d=this.path[c],{scroll:e,options:f}=d;d!==this.root&&e&&f.layoutScroll&&(e.wasRoot&&el(b,a),F(b.x,e.offset.x),F(b.y,e.offset.y))}return b}applyTransform(a,b=!1){let c=bd();el(c,a);for(let a=0;a<this.path.length;a++){let d=this.path[a];!b&&d.options.layoutScroll&&d.scroll&&d!==d.root&&H(c,{x:-d.scroll.offset.x,y:-d.scroll.offset.y}),A(d.latestValues)&&H(c,d.latestValues)}return A(this.latestValues)&&H(c,this.latestValues),c}removeTransform(a){let b=bd();el(b,a);for(let a=0;a<this.path.length;a++){let c=this.path[a];if(!c.instance||!A(c.latestValues))continue;z(c.latestValues)&&c.updateSnapshot();let d=bd();el(d,c.measurePageBox()),er(b,c.latestValues,c.snapshot?c.snapshot.layoutBox:void 0,d)}return A(this.latestValues)&&er(b,this.latestValues),b}setTargetDelta(a){this.targetDelta=a,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(a){this.options={...this.options,...a,crossfade:void 0===a.crossfade||a.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==al.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(a=!1){let b=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=b.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=b.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=b.isSharedProjectionDirty);let c=!!this.resumingFrom||this!==b;if(!(a||c&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:d,layoutId:e}=this.options;if(this.layout&&(d||e)){if(this.resolvedRelativeTargetAt=al.timestamp,!this.targetDelta&&!this.relativeTarget){let a=this.getClosestProjectingParent();a&&a.layout&&1!==this.animationProgress?(this.relativeParent=a,this.forceRelativeParentToResolveTarget(),this.relativeTarget=bd(),this.relativeTargetOrigin=bd(),dJ(this.relativeTargetOrigin,this.layout.layoutBox,a.layout.layoutBox),el(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if(this.target||(this.target=bd(),this.targetWithTransforms=bd()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target){var f,g,h;this.forceRelativeParentToResolveTarget(),f=this.target,g=this.relativeTarget,h=this.relativeParent.target,dH(f.x,g.x,h.x),dH(f.y,g.y,h.y)}else this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):el(this.target,this.layout.layoutBox),E(this.target,this.targetDelta)):el(this.target,this.layout.layoutBox);if(this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let a=this.getClosestProjectingParent();a&&!!a.resumingFrom==!!this.resumingFrom&&!a.options.layoutScroll&&a.target&&1!==this.animationProgress?(this.relativeParent=a,this.forceRelativeParentToResolveTarget(),this.relativeTarget=bd(),this.relativeTargetOrigin=bd(),dJ(this.relativeTargetOrigin,this.target,a.target),el(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}ah.value&&eA.calculatedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||z(this.parent.latestValues)||B(this.parent.latestValues)))if(this.parent.isProjecting())return this.parent;else return this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){let a=this.getLead(),b=!!this.resumingFrom||this!==a,c=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(c=!1),b&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(c=!1),this.resolvedRelativeTargetAt===al.timestamp&&(c=!1),c)return;let{layout:d,layoutId:e}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(d||e))return;el(this.layoutCorrected,this.layout.layoutBox);let f=this.treeScale.x,g=this.treeScale.y;!function(a,b,c,d=!1){let e,f,g=c.length;if(g){b.x=b.y=1;for(let h=0;h<g;h++){f=(e=c[h]).projectionDelta;let{visualElement:g}=e.options;(!g||!g.props.style||"contents"!==g.props.style.display)&&(d&&e.options.layoutScroll&&e.scroll&&e!==e.root&&H(a,{x:-e.scroll.offset.x,y:-e.scroll.offset.y}),f&&(b.x*=f.x.scale,b.y*=f.y.scale,E(a,f)),d&&A(e.latestValues)&&H(a,e.latestValues))}b.x<1.0000000000001&&b.x>.999999999999&&(b.x=1),b.y<1.0000000000001&&b.y>.999999999999&&(b.y=1)}}(this.layoutCorrected,this.treeScale,this.path,b),a.layout&&!a.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(a.target=a.layout.layoutBox,a.targetWithTransforms=bd());let{target:h}=a;if(!h){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(em(this.prevProjectionDelta.x,this.projectionDelta.x),em(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),dG(this.projectionDelta,this.layoutCorrected,h,this.latestValues),this.treeScale.x===f&&this.treeScale.y===g&&ey(this.projectionDelta.x,this.prevProjectionDelta.x)&&ey(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",h)),ah.value&&eA.calculatedProjections++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(a=!0){if(this.options.visualElement?.scheduleRender(),a){let a=this.getStack();a&&a.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=bb(),this.projectionDelta=bb(),this.projectionDeltaWithTransform=bb()}setAnimationOrigin(a,b=!1){let c,d=this.snapshot,e=d?d.latestValues:{},f={...this.latestValues},g=bb();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!b;let h=bd(),i=(d?d.source:void 0)!==(this.layout?this.layout.source:void 0),j=this.getStack(),k=!j||j.members.length<=1,l=!!(i&&!k&&!0===this.options.crossfade&&!this.path.some(eU));this.animationProgress=0,this.mixTargetDelta=b=>{let d=b/1e3;if(eS(g.x,a.x,d),eS(g.y,a.y,d),this.setTargetDelta(g),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var j,m,n,o,p,q;dJ(h,this.layout.layoutBox,this.relativeParent.layout.layoutBox),n=this.relativeTarget,o=this.relativeTargetOrigin,p=h,q=d,eT(n.x,o.x,p.x,q),eT(n.y,o.y,p.y,q),c&&(j=this.relativeTarget,m=c,eu(j.x,m.x)&&eu(j.y,m.y))&&(this.isProjectionDirty=!1),c||(c=bd()),el(c,this.relativeTarget)}i&&(this.animationValues=f,function(a,b,c,d,e,f){e?(a.opacity=x(0,c.opacity??1,eh(d)),a.opacityExit=x(b.opacity??1,0,ei(d))):f&&(a.opacity=x(b.opacity??1,c.opacity??1,d));for(let e=0;e<ed;e++){let f=`border${ec[e]}Radius`,g=eg(b,f),h=eg(c,f);(void 0!==g||void 0!==h)&&(g||(g=0),h||(h=0),0===g||0===h||ef(g)===ef(h)?(a[f]=Math.max(x(ee(g),ee(h),d),0),(Q.test(h)||Q.test(g))&&(a[f]+="%")):a[f]=h)}(b.rotate||c.rotate)&&(a.rotate=x(b.rotate||0,c.rotate||0,d))}(f,e,this.latestValues,d,l,k)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=d},this.mixTargetDelta(1e3*!!this.options.layoutRoot)}startAnimation(a){this.notifyListeners("animationStart"),this.currentAnimation?.stop(),this.resumingFrom?.currentAnimation?.stop(),this.pendingAnimation&&(ak(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=aj.update(()=>{d1.hasAnimatedSinceResize=!0,cf.layout++,this.motionValue||(this.motionValue=a5(0)),this.currentAnimation=function(a,b,c){let d=aZ(a)?a:a5(a);return d.start(dk("",d,b,c)),d.animation}(this.motionValue,[0,1e3],{...a,velocity:0,isSync:!0,onUpdate:b=>{this.mixTargetDelta(b),a.onUpdate&&a.onUpdate(b)},onStop:()=>{cf.layout--},onComplete:()=>{cf.layout--,a.onComplete&&a.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let a=this.getStack();a&&a.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let a=this.getLead(),{targetWithTransforms:b,target:c,layout:d,latestValues:e}=a;if(b&&c&&d){if(this!==a&&this.layout&&d&&eZ(this.options.animationType,this.layout.layoutBox,d.layoutBox)){c=this.target||bd();let b=dE(this.layout.layoutBox.x);c.x.min=a.target.x.min,c.x.max=c.x.min+b;let d=dE(this.layout.layoutBox.y);c.y.min=a.target.y.min,c.y.max=c.y.min+d}el(b,c),H(b,e),dG(this.projectionDeltaWithTransform,this.layoutCorrected,b,e)}}registerSharedNode(a,b){this.sharedNodes.has(a)||this.sharedNodes.set(a,new ez),this.sharedNodes.get(a).add(b);let c=b.options.initialPromotionConfig;b.promote({transition:c?c.transition:void 0,preserveFollowOpacity:c&&c.shouldPreserveFollowOpacity?c.shouldPreserveFollowOpacity(b):void 0})}isLead(){let a=this.getStack();return!a||a.lead===this}getLead(){let{layoutId:a}=this.options;return a&&this.getStack()?.lead||this}getPrevLead(){let{layoutId:a}=this.options;return a?this.getStack()?.prevLead:void 0}getStack(){let{layoutId:a}=this.options;if(a)return this.root.sharedNodes.get(a)}promote({needsReset:a,transition:b,preserveFollowOpacity:c}={}){let d=this.getStack();d&&d.promote(this,c),a&&(this.projectionDelta=void 0,this.needsReset=!0),b&&this.setOptions({transition:b})}relegate(){let a=this.getStack();return!!a&&a.relegate(this)}resetSkewAndRotation(){let{visualElement:a}=this.options;if(!a)return;let b=!1,{latestValues:c}=a;if((c.z||c.rotate||c.rotateX||c.rotateY||c.rotateZ||c.skewX||c.skewY)&&(b=!0),!b)return;let d={};c.z&&eD("z",a,d,this.animationValues);for(let b=0;b<eB.length;b++)eD(`rotate${eB[b]}`,a,d,this.animationValues),eD(`skew${eB[b]}`,a,d,this.animationValues);for(let b in a.render(),d)a.setStaticValue(b,d[b]),this.animationValues&&(this.animationValues[b]=d[b]);a.scheduleRender()}applyProjectionStyles(a,b){if(!this.instance||this.isSVG)return;if(!this.isVisible){a.visibility="hidden";return}let c=this.getTransformTemplate();if(this.needsReset){this.needsReset=!1,a.visibility="",a.opacity="",a.pointerEvents=b$(b?.pointerEvents)||"",a.transform=c?c(this.latestValues,""):"none";return}let d=this.getLead();if(!this.projectionDelta||!this.layout||!d.target){this.options.layoutId&&(a.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,a.pointerEvents=b$(b?.pointerEvents)||""),this.hasProjected&&!A(this.latestValues)&&(a.transform=c?c({},""):"none",this.hasProjected=!1);return}a.visibility="";let e=d.animationValues||d.latestValues;this.applyTransformsToTarget();let f=function(a,b,c){let d="",e=a.x.translate/b.x,f=a.y.translate/b.y,g=c?.z||0;if((e||f||g)&&(d=`translate3d(${e}px, ${f}px, ${g}px) `),(1!==b.x||1!==b.y)&&(d+=`scale(${1/b.x}, ${1/b.y}) `),c){let{transformPerspective:a,rotate:b,rotateX:e,rotateY:f,skewX:g,skewY:h}=c;a&&(d=`perspective(${a}px) ${d}`),b&&(d+=`rotate(${b}deg) `),e&&(d+=`rotateX(${e}deg) `),f&&(d+=`rotateY(${f}deg) `),g&&(d+=`skewX(${g}deg) `),h&&(d+=`skewY(${h}deg) `)}let h=a.x.scale*b.x,i=a.y.scale*b.y;return(1!==h||1!==i)&&(d+=`scale(${h}, ${i})`),d||"none"}(this.projectionDeltaWithTransform,this.treeScale,e);c&&(f=c(e,f)),a.transform=f;let{x:g,y:h}=this.projectionDelta;for(let b in a.transformOrigin=`${100*g.origin}% ${100*h.origin}% 0`,d.animationValues?a.opacity=d===this?e.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:e.opacityExit:a.opacity=d===this?void 0!==e.opacity?e.opacity:"":void 0!==e.opacityExit?e.opacityExit:0,by){if(void 0===e[b])continue;let{correct:c,applyTo:g,isCSSVariable:h}=by[b],i="none"===f?e[b]:c(e[b],d);if(g){let b=g.length;for(let c=0;c<b;c++)a[g[c]]=i}else h?this.options.visualElement.renderState.vars[b]=i:a[b]=i}this.options.layoutId&&(a.pointerEvents=d===this?b$(b?.pointerEvents)||"":"none")}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(a=>a.currentAnimation?.stop()),this.root.nodes.forEach(eK),this.root.sharedNodes.clear()}}}function eF(a){a.updateLayout()}function eG(a){let b=a.resumeFrom?.snapshot||a.snapshot;if(a.isLead()&&a.layout&&b&&a.hasListeners("didUpdate")){let{layoutBox:c,measuredBox:d}=a.layout,{animationType:e}=a.options,f=b.source!==a.layout.source;"size"===e?dK(a=>{let d=f?b.measuredBox[a]:b.layoutBox[a],e=dE(d);d.min=c[a].min,d.max=d.min+e}):eZ(e,b.layoutBox,c)&&dK(d=>{let e=f?b.measuredBox[d]:b.layoutBox[d],g=dE(c[d]);e.max=e.min+g,a.relativeTarget&&!a.currentAnimation&&(a.isProjectionDirty=!0,a.relativeTarget[d].max=a.relativeTarget[d].min+g)});let g=bb();dG(g,c,b.layoutBox);let h=bb();f?dG(h,a.applyTransform(d,!0),b.measuredBox):dG(h,c,b.layoutBox);let i=!et(g),j=!1;if(!a.resumeFrom){let d=a.getClosestProjectingParent();if(d&&!d.resumeFrom){let{snapshot:e,layout:f}=d;if(e&&f){let g=bd();dJ(g,b.layoutBox,e.layoutBox);let h=bd();dJ(h,c,f.layoutBox),ew(g,h)||(j=!0),d.options.layoutRoot&&(a.relativeTarget=h,a.relativeTargetOrigin=g,a.relativeParent=d)}}}a.notifyListeners("didUpdate",{layout:c,snapshot:b,delta:h,layoutDelta:g,hasLayoutChanged:i,hasRelativeLayoutChanged:j})}else if(a.isLead()){let{onExitComplete:b}=a.options;b&&b()}a.options.transition=void 0}function eH(a){ah.value&&eA.nodes++,a.parent&&(a.isProjecting()||(a.isProjectionDirty=a.parent.isProjectionDirty),a.isSharedProjectionDirty||(a.isSharedProjectionDirty=!!(a.isProjectionDirty||a.parent.isProjectionDirty||a.parent.isSharedProjectionDirty)),a.isTransformDirty||(a.isTransformDirty=a.parent.isTransformDirty))}function eI(a){a.isProjectionDirty=a.isSharedProjectionDirty=a.isTransformDirty=!1}function eJ(a){a.clearSnapshot()}function eK(a){a.clearMeasurements()}function eL(a){a.isLayoutDirty=!1}function eM(a){let{visualElement:b}=a.options;b&&b.getProps().onBeforeLayoutMeasure&&b.notify("BeforeLayoutMeasure"),a.resetTransform()}function eN(a){a.finishAnimation(),a.targetDelta=a.relativeTarget=a.target=void 0,a.isProjectionDirty=!0}function eO(a){a.resolveTargetDelta()}function eP(a){a.calcProjection()}function eQ(a){a.resetSkewAndRotation()}function eR(a){a.removeLeadSnapshot()}function eS(a,b,c){a.translate=x(b.translate,0,c),a.scale=x(b.scale,1,c),a.origin=b.origin,a.originPoint=b.originPoint}function eT(a,b,c,d){a.min=x(b.min,c.min,d),a.max=x(b.max,c.max,d)}function eU(a){return a.animationValues&&void 0!==a.animationValues.opacityExit}let eV={duration:.45,ease:[.4,0,.1,1]},eW=a=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(a),eX=eW("applewebkit/")&&!eW("chrome/")?Math.round:ae;function eY(a){a.min=eX(a.min),a.max=eX(a.max)}function eZ(a,b,c){return"position"===a||"preserve-aspect"===a&&!(.2>=Math.abs(ex(b)-ex(c)))}function e$(a){return a!==a.root&&a.scroll?.wasRoot}let e_=eE({attachResizeListener:(a,b)=>dA(a,"resize",b),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),e0={current:void 0},e1=eE({measureScroll:a=>({x:a.scrollLeft,y:a.scrollTop}),defaultParent:()=>{if(!e0.current){let a=new e_({});a.mount(window),a.setOptions({layoutScroll:!0}),e0.current=a}return e0.current},resetTransform:(a,b)=>{a.style.transform=void 0!==b?b:"none"},checkIsScrollRoot:a=>"fixed"===window.getComputedStyle(a).position});function e2(a,b){let c=function(a,b,c){if(a instanceof EventTarget)return[a];if("string"==typeof a){let b=document,c=(void 0)??b.querySelectorAll(a);return c?Array.from(c):[]}return Array.from(a)}(a),d=new AbortController;return[c,{passive:!0,...b,signal:d.signal},()=>d.abort()]}function e3(a){return!("touch"===a.pointerType||dz.x||dz.y)}function e4(a,b,c){let{props:d}=a;a.animationState&&d.whileHover&&a.animationState.setActive("whileHover","Start"===c);let e=d["onHover"+c];e&&aj.postRender(()=>e(b,dC(b)))}class e5 extends dv{mount(){let{current:a}=this.node;a&&(this.unmount=function(a,b,c={}){let[d,e,f]=e2(a,c),g=a=>{if(!e3(a))return;let{target:c}=a,d=b(c,a);if("function"!=typeof d||!c)return;let f=a=>{e3(a)&&(d(a),c.removeEventListener("pointerleave",f))};c.addEventListener("pointerleave",f,e)};return d.forEach(a=>{a.addEventListener("pointerenter",g,e)}),f}(a,(a,b)=>(e4(this.node,b,"Start"),a=>e4(this.node,a,"End"))))}unmount(){}}class e6 extends dv{constructor(){super(...arguments),this.isActive=!1}onFocus(){let a=!1;try{a=this.node.current.matches(":focus-visible")}catch(b){a=!0}a&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=cd(dA(this.node.current,"focus",()=>this.onFocus()),dA(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}var e7=c(552);let e8=(a,b)=>!!b&&(a===b||e8(a,b.parentElement)),e9=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),fa=new WeakSet;function fb(a){return b=>{"Enter"===b.key&&a(b)}}function fc(a,b){a.dispatchEvent(new PointerEvent("pointer"+b,{isPrimary:!0,bubbles:!0}))}function fd(a){return dB(a)&&!(dz.x||dz.y)}function fe(a,b,c){let{props:d}=a;if(a.current instanceof HTMLButtonElement&&a.current.disabled)return;a.animationState&&d.whileTap&&a.animationState.setActive("whileTap","Start"===c);let e=d["onTap"+("End"===c?"":c)];e&&aj.postRender(()=>e(b,dC(b)))}class ff extends dv{mount(){let{current:a}=this.node;a&&(this.unmount=function(a,b,c={}){let[d,e,f]=e2(a,c),g=a=>{let d=a.currentTarget;if(!fd(a))return;fa.add(d);let f=b(d,a),g=(a,b)=>{window.removeEventListener("pointerup",h),window.removeEventListener("pointercancel",i),fa.has(d)&&fa.delete(d),fd(a)&&"function"==typeof f&&f(a,{success:b})},h=a=>{g(a,d===window||d===document||c.useGlobalTarget||e8(d,a.target))},i=a=>{g(a,!1)};window.addEventListener("pointerup",h,e),window.addEventListener("pointercancel",i,e)};return d.forEach(a=>{((c.useGlobalTarget?window:a).addEventListener("pointerdown",g,e),(0,e7.s)(a))&&(a.addEventListener("focus",a=>((a,b)=>{let c=a.currentTarget;if(!c)return;let d=fb(()=>{if(fa.has(c))return;fc(c,"down");let a=fb(()=>{fc(c,"up")});c.addEventListener("keyup",a,b),c.addEventListener("blur",()=>fc(c,"cancel"),b)});c.addEventListener("keydown",d,b),c.addEventListener("blur",()=>c.removeEventListener("keydown",d),b)})(a,e)),e9.has(a.tagName)||-1!==a.tabIndex||a.hasAttribute("tabindex")||(a.tabIndex=0))}),f}(a,(a,b)=>(fe(this.node,b,"Start"),(a,{success:b})=>fe(this.node,a,b?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}let fg=new WeakMap,fh=new WeakMap,fi=a=>{let b=fg.get(a.target);b&&b(a)},fj=a=>{a.forEach(fi)},fk={some:0,all:1};class fl extends dv{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:a={}}=this.node.getProps(),{root:b,margin:c,amount:d="some",once:e}=a,f={root:b?b.current:void 0,rootMargin:c,threshold:"number"==typeof d?d:fk[d]};return function(a,b,c){let d=function({root:a,...b}){let c=a||document;fh.has(c)||fh.set(c,{});let d=fh.get(c),e=JSON.stringify(b);return d[e]||(d[e]=new IntersectionObserver(fj,{root:a,...b})),d[e]}(b);return fg.set(a,c),d.observe(a),()=>{fg.delete(a),d.unobserve(a)}}(this.node.current,f,a=>{let{isIntersecting:b}=a;if(this.isInView===b||(this.isInView=b,e&&!b&&this.hasEnteredView))return;b&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",b);let{onViewportEnter:c,onViewportLeave:d}=this.node.getProps(),f=b?c:d;f&&f(a)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:a,prevProps:b}=this.node;["amount","margin","root"].some(function({viewport:a={}},{viewport:b={}}={}){return c=>a[c]!==b[c]}(a,b))&&this.startObserver()}unmount(){}}let fm=function(a,b){if("undefined"==typeof Proxy)return b7;let c=new Map,d=(c,d)=>b7(c,d,a,b);return new Proxy((a,b)=>d(a,b),{get:(e,f)=>"create"===f?d:(c.has(f)||c.set(f,b7(f,void 0,a,b)),c.get(f))})}({animation:{Feature:dw},exit:{Feature:dy},inView:{Feature:fl},tap:{Feature:ff},focus:{Feature:e6},hover:{Feature:e5},pan:{Feature:d_},drag:{Feature:dZ,ProjectionNode:e1,MeasureLayout:d6},layout:{ProjectionNode:e1,MeasureLayout:d6}},(a,b)=>bL(a)?new bJ(b):new bB(b,{allowProjection:a!==e.Fragment}))},4163:(a,b,c)=>{"use strict";c.d(b,{hO:()=>i,sG:()=>h});var d=c(3210),e=c(1215),f=c(8730),g=c(687),h=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((a,b)=>{let c=(0,f.TL)(`Primitive.${b}`),e=d.forwardRef((a,d)=>{let{asChild:e,...f}=a;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,g.jsx)(e?c:b,{...f,ref:d})});return e.displayName=`Primitive.${b}`,{...a,[b]:e}},{});function i(a,b){a&&e.flushSync(()=>a.dispatchEvent(b))}},4224:(a,b,c)=>{"use strict";c.d(b,{F:()=>g});var d=c(9384);let e=a=>"boolean"==typeof a?`${a}`:0===a?"0":a,f=d.$,g=(a,b)=>c=>{var d;if((null==b?void 0:b.variants)==null)return f(a,null==c?void 0:c.class,null==c?void 0:c.className);let{variants:g,defaultVariants:h}=b,i=Object.keys(g).map(a=>{let b=null==c?void 0:c[a],d=null==h?void 0:h[a];if(null===b)return null;let f=e(b)||e(d);return g[a][f]}),j=c&&Object.entries(c).reduce((a,b)=>{let[c,d]=b;return void 0===d||(a[c]=d),a},{});return f(a,i,null==b||null==(d=b.compoundVariants)?void 0:d.reduce((a,b)=>{let{class:c,className:d,...e}=b;return Object.entries(e).every(a=>{let[b,c]=a;return Array.isArray(c)?c.includes({...h,...j}[b]):({...h,...j})[b]===c})?[...a,c,d]:a},[]),null==c?void 0:c.class,null==c?void 0:c.className)}},4397:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"findHeadInCache",{enumerable:!0,get:function(){return f}});let d=c(3913),e=c(3123);function f(a,b){return function a(b,c,f){if(0===Object.keys(c).length)return[b,f];let g=Object.keys(c).filter(a=>"children"!==a);for(let h of("children"in c&&g.unshift("children"),g)){let[g,i]=c[h];if(g===d.DEFAULT_SEGMENT_KEY)continue;let j=b.parallelRoutes.get(h);if(!j)continue;let k=(0,e.createRouterCacheKey)(g),l=j.get(k);if(!l)continue;let m=a(l,i,f+"/"+k);if(m)return m}return null}(a,b,"")}("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},4398:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(2688).A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},4400:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"invalidateCacheByRouterState",{enumerable:!0,get:function(){return e}});let d=c(3123);function e(a,b,c){for(let e in c[1]){let f=c[1][e][0],g=(0,d.createRouterCacheKey)(f),h=b.parallelRoutes.get(e);if(h){let b=new Map(h);b.delete(g),a.parallelRoutes.set(e,b)}}}("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},4431:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>j,metadata:()=>i});var d=c(7413),e=c(5091),f=c.n(e),g=c(1386),h=c.n(g);c(1135);let i={title:"Himalayan Brew Caf\xe9 - Best Caf\xe9 in Biratnagar | Cozy Coffee Shop Nepal",description:"Experience the finest coffee and authentic Nepali hospitality at Himalayan Brew Caf\xe9 in Biratnagar. Serving premium coffee, traditional momos, and creating memorable moments since 2020.",keywords:"caf\xe9 in Biratnagar, best caf\xe9 Biratnagar, cozy coffee shop Biratnagar, Himalayan Brew, Nepal coffee, momos Biratnagar, coffee shop Nepal",authors:[{name:"Himalayan Brew Caf\xe9"}],openGraph:{title:"Himalayan Brew Caf\xe9 - Where Mountains Meet Coffee",description:"A cozy corner in the heart of Biratnagar, serving authentic Nepali hospitality with the finest coffee and local delicacies.",url:"https://himalayanbrew.com.np",siteName:"Himalayan Brew Caf\xe9",locale:"en_US",type:"website"}};function j({children:a}){return(0,d.jsx)("html",{lang:"en",children:(0,d.jsx)("body",{className:`${f().variable} ${h().variable} font-sans antialiased`,children:a})})}},4479:(a,b,c)=>{"use strict";function d(a){return"object"==typeof a&&null!==a}c.d(b,{G:()=>d})},4493:(a,b,c)=>{"use strict";c.d(b,{Wu:()=>i,ZB:()=>h,Zp:()=>f,aR:()=>g});var d=c(687);c(3210);var e=c(1743);function f({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"card",className:(0,e.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",a),...b})}function g({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"card-header",className:(0,e.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",a),...b})}function h({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"card-title",className:(0,e.cn)("leading-none font-semibold",a),...b})}function i({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"card-content",className:(0,e.cn)("px-6",a),...b})}},4544:(a,b,c)=>{"use strict";c.d(b,{Navigation:()=>d});let d=(0,c(1369).registerClientReference)(function(){throw Error("Attempted to call Navigation() from the server but Navigation is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\navigation.tsx","Navigation")},4642:(a,b)=>{"use strict";function c(a){let b=parseInt(a.slice(0,2),16),c=b>>1&63,d=Array(6);for(let a=0;a<6;a++){let b=c>>5-a&1;d[a]=1===b}return{type:1==(b>>7&1)?"use-cache":"server-action",usedArgs:d,hasRestArgs:1==(1&b)}}function d(a,b){let c=Array(a.length);for(let d=0;d<a.length;d++)(d<6&&b.usedArgs[d]||d>=6&&b.hasRestArgs)&&(c[d]=a[d]);return c}Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{extractInfoFromServerReferenceId:function(){return c},omitUnusedArgs:function(){return d}})},4674:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"normalizePathTrailingSlash",{enumerable:!0,get:function(){return f}});let d=c(4949),e=c(3931),f=a=>{if(!a.startsWith("/"))return a;let{pathname:b,query:c,hash:f}=(0,e.parsePath)(a);return""+(0,d.removeTrailingSlash)(b)+c+f};("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},4947:(a,b,c)=>{"use strict";c.d(b,{Footer:()=>d});let d=(0,c(1369).registerClientReference)(function(){throw Error("Attempted to call Footer() from the server but Footer is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\footer.tsx","Footer")},4949:(a,b)=>{"use strict";function c(a){return a.replace(/\/$/,"")||"/"}Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"removeTrailingSlash",{enumerable:!0,get:function(){return c}})},5037:(a,b,c)=>{"use strict";c.d(b,{Navigation:()=>B});var d=c(687),e=c(3210),f=c(5814),g=c.n(f),h=c(9523),i=c(3166),j=c(1860);let k=(0,c(2688).A)("menu",[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]]);var l=c(2157),m=c(5170),n=c(2743),o=c(1279),p=c(552),q=c(2582);class r extends e.Component{getSnapshotBeforeUpdate(a){let b=this.props.childRef.current;if(b&&a.isPresent&&!this.props.isPresent){let a=b.offsetParent,c=(0,p.s)(a)&&a.offsetWidth||0,d=this.props.sizeRef.current;d.height=b.offsetHeight||0,d.width=b.offsetWidth||0,d.top=b.offsetTop,d.left=b.offsetLeft,d.right=c-d.width-d.left}return null}componentDidUpdate(){}render(){return this.props.children}}function s({children:a,isPresent:b,anchorX:c,root:f}){let g=(0,e.useId)(),h=(0,e.useRef)(null),i=(0,e.useRef)({width:0,height:0,top:0,left:0,right:0}),{nonce:j}=(0,e.useContext)(q.Q);return(0,e.useInsertionEffect)(()=>{let{width:a,height:d,top:e,left:k,right:l}=i.current;if(b||!h.current||!a||!d)return;let m="left"===c?`left: ${k}`:`right: ${l}`;h.current.dataset.motionPopId=g;let n=document.createElement("style");j&&(n.nonce=j);let o=f??document.head;return o.appendChild(n),n.sheet&&n.sheet.insertRule(`
          [data-motion-pop-id="${g}"] {
            position: absolute !important;
            width: ${a}px !important;
            height: ${d}px !important;
            ${m}px !important;
            top: ${e}px !important;
          }
        `),()=>{o.removeChild(n),o.contains(n)&&o.removeChild(n)}},[b]),(0,d.jsx)(r,{isPresent:b,childRef:h,sizeRef:i,children:e.cloneElement(a,{ref:h})})}let t=({children:a,initial:b,isPresent:c,onExitComplete:f,custom:g,presenceAffectsLayout:h,mode:i,anchorX:j,root:k})=>{let l=(0,m.M)(u),n=(0,e.useId)(),p=!0,q=(0,e.useMemo)(()=>(p=!1,{id:n,initial:b,isPresent:c,custom:g,onExitComplete:a=>{for(let b of(l.set(a,!0),l.values()))if(!b)return;f&&f()},register:a=>(l.set(a,!1),()=>l.delete(a))}),[c,l,f]);return h&&p&&(q={...q}),(0,e.useMemo)(()=>{l.forEach((a,b)=>l.set(b,!1))},[c]),e.useEffect(()=>{c||l.size||!f||f()},[c]),"popLayout"===i&&(a=(0,d.jsx)(s,{isPresent:c,anchorX:j,root:k,children:a})),(0,d.jsx)(o.t.Provider,{value:q,children:a})};function u(){return new Map}var v=c(6044);let w=a=>a.key||"";function x(a){let b=[];return e.Children.forEach(a,a=>{(0,e.isValidElement)(a)&&b.push(a)}),b}let y=({children:a,custom:b,initial:c=!0,onExitComplete:f,presenceAffectsLayout:g=!0,mode:h="sync",propagate:i=!1,anchorX:j="left",root:k})=>{let[o,p]=(0,v.xQ)(i),q=(0,e.useMemo)(()=>x(a),[a]),r=i&&!o?[]:q.map(w),s=(0,e.useRef)(!0),u=(0,e.useRef)(q),y=(0,m.M)(()=>new Map),[z,A]=(0,e.useState)(q),[B,C]=(0,e.useState)(q);(0,n.E)(()=>{s.current=!1,u.current=q;for(let a=0;a<B.length;a++){let b=w(B[a]);r.includes(b)?y.delete(b):!0!==y.get(b)&&y.set(b,!1)}},[B,r.length,r.join("-")]);let D=[];if(q!==z){let a=[...q];for(let b=0;b<B.length;b++){let c=B[b],d=w(c);r.includes(d)||(a.splice(b,0,c),D.push(c))}return"wait"===h&&D.length&&(a=D),C(x(a)),A(q),null}let{forceRender:E}=(0,e.useContext)(l.L);return(0,d.jsx)(d.Fragment,{children:B.map(a=>{let e=w(a),l=(!i||!!o)&&(q===B||r.includes(e));return(0,d.jsx)(t,{isPresent:l,initial:(!s.current||!!c)&&void 0,custom:b,presenceAffectsLayout:g,mode:h,root:k,onExitComplete:l?void 0:()=>{if(!y.has(e))return;y.set(e,!0);let a=!0;y.forEach(b=>{b||(a=!1)}),a&&(E?.(),C(u.current),i&&p?.(),f&&f())},anchorX:j,children:a},e)})})};var z=c(4124);let A=[{href:"/",label:"Home"},{href:"/about",label:"About"},{href:"/menu",label:"Menu"},{href:"/gallery",label:"Gallery"},{href:"/testimonials",label:"Testimonials"},{href:"/contact",label:"Contact"}];function B(){let[a,b]=(0,e.useState)(!1);return(0,d.jsxs)("nav",{className:"fixed top-0 left-0 right-0 z-50 bg-cafe-cream/95 backdrop-blur-sm border-b border-cafe-beige",children:[(0,d.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,d.jsxs)("div",{className:"flex justify-between items-center h-16",children:[(0,d.jsxs)(g(),{href:"/",className:"flex items-center space-x-2",children:[(0,d.jsx)(i.A,{className:"h-8 w-8 text-cafe-brown"}),(0,d.jsx)("span",{className:"font-serif font-bold text-xl text-cafe-dark-brown",children:"Himalayan Brew"})]}),(0,d.jsx)("div",{className:"hidden md:flex items-center space-x-8",children:A.map(a=>(0,d.jsx)(g(),{href:a.href,className:"text-cafe-brown hover:text-cafe-dark-brown transition-colors duration-200 font-medium",children:a.label},a.href))}),(0,d.jsx)("div",{className:"md:hidden",children:(0,d.jsx)(h.$,{variant:"ghost",size:"sm",onClick:()=>b(!a),className:"text-cafe-brown hover:text-cafe-dark-brown",children:a?(0,d.jsx)(j.A,{className:"h-6 w-6"}):(0,d.jsx)(k,{className:"h-6 w-6"})})})]})}),(0,d.jsx)(y,{children:a&&(0,d.jsx)(z.P.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},className:"md:hidden bg-cafe-cream border-b border-cafe-beige",children:(0,d.jsx)("div",{className:"px-4 py-4 space-y-4",children:A.map(a=>(0,d.jsx)(g(),{href:a.href,className:"block text-cafe-brown hover:text-cafe-dark-brown transition-colors duration-200 font-medium",onClick:()=>b(!1),children:a.label},a.href))})})})]})}},5076:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{prefetchQueue:function(){return f},prefetchReducer:function(){return g}});let d=c(5144),e=c(5334),f=new d.PromiseQueue(5),g=function(a,b){(0,e.prunePrefetchCache)(a.prefetchCache);let{url:c}=b;return(0,e.getOrCreatePrefetchCacheEntry)({url:c,nextUrl:a.nextUrl,prefetchCache:a.prefetchCache,kind:b.kind,tree:a.tree,allowAliasing:!0}),a};("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},5144:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"PromiseQueue",{enumerable:!0,get:function(){return j}});let d=c(6312),e=c(9656);var f=e._("_maxConcurrency"),g=e._("_runningCount"),h=e._("_queue"),i=e._("_processNext");class j{enqueue(a){let b,c,e=new Promise((a,d)=>{b=a,c=d}),f=async()=>{try{d._(this,g)[g]++;let c=await a();b(c)}catch(a){c(a)}finally{d._(this,g)[g]--,d._(this,i)[i]()}};return d._(this,h)[h].push({promiseFn:e,task:f}),d._(this,i)[i](),e}bump(a){let b=d._(this,h)[h].findIndex(b=>b.promiseFn===a);if(b>-1){let a=d._(this,h)[h].splice(b,1)[0];d._(this,h)[h].unshift(a),d._(this,i)[i](!0)}}constructor(a=5){Object.defineProperty(this,i,{value:k}),Object.defineProperty(this,f,{writable:!0,value:void 0}),Object.defineProperty(this,g,{writable:!0,value:void 0}),Object.defineProperty(this,h,{writable:!0,value:void 0}),d._(this,f)[f]=a,d._(this,g)[g]=0,d._(this,h)[h]=[]}}function k(a){if(void 0===a&&(a=!1),(d._(this,g)[g]<d._(this,f)[f]||a)&&d._(this,h)[h].length>0){var b;null==(b=d._(this,h)[h].shift())||b.task()}}("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},5170:(a,b,c)=>{"use strict";c.d(b,{M:()=>e});var d=c(3210);function e(a){let b=(0,d.useRef)(null);return null===b.current&&(b.current=a()),b.current}},5232:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{handleExternalUrl:function(){return t},navigateReducer:function(){return function a(b,c){let{url:v,isExternalUrl:w,navigateType:x,shouldScroll:y,allowAliasing:z}=c,A={},{hash:B}=v,C=(0,e.createHrefFromUrl)(v),D="push"===x;if((0,q.prunePrefetchCache)(b.prefetchCache),A.preserveCustomHistoryState=!1,A.pendingPush=D,w)return t(b,A,v.toString(),D);if(document.getElementById("__next-page-redirect"))return t(b,A,C,D);let E=(0,q.getOrCreatePrefetchCacheEntry)({url:v,nextUrl:b.nextUrl,tree:b.tree,prefetchCache:b.prefetchCache,allowAliasing:z}),{treeAtTimeOfPrefetch:F,data:G}=E;return m.prefetchQueue.bump(G),G.then(m=>{let{flightData:q,canonicalUrl:w,postponed:x}=m,z=Date.now(),G=!1;if(E.lastUsedTime||(E.lastUsedTime=z,G=!0),E.aliased){let d=new URL(v.href);w&&(d.pathname=w.pathname);let e=(0,s.handleAliasedPrefetchEntry)(z,b,q,d,A);return!1===e?a(b,{...c,allowAliasing:!1}):e}if("string"==typeof q)return t(b,A,q,D);let H=w?(0,e.createHrefFromUrl)(w):C;if(B&&b.canonicalUrl.split("#",1)[0]===H.split("#",1)[0])return A.onlyHashChange=!0,A.canonicalUrl=H,A.shouldScroll=y,A.hashFragment=B,A.scrollableSegments=[],(0,k.handleMutable)(b,A);let I=b.tree,J=b.cache,K=[];for(let a of q){let{pathToSegment:c,seedData:e,head:k,isHeadPartial:m,isRootRender:q}=a,s=a.tree,w=["",...c],y=(0,g.applyRouterStatePatchToTree)(w,I,s,C);if(null===y&&(y=(0,g.applyRouterStatePatchToTree)(w,F,s,C)),null!==y){if(e&&q&&x){let a=(0,p.startPPRNavigation)(z,J,I,s,e,k,m,!1,K);if(null!==a){if(null===a.route)return t(b,A,C,D);y=a.route;let c=a.node;null!==c&&(A.cache=c);let e=a.dynamicRequestTree;if(null!==e){let c=(0,d.fetchServerResponse)(new URL(H,v.origin),{flightRouterState:e,nextUrl:b.nextUrl});(0,p.listenForDynamicRequest)(a,c)}}else y=s}else{if((0,i.isNavigatingToNewRootLayout)(I,y))return t(b,A,C,D);let d=(0,n.createEmptyCacheNode)(),e=!1;for(let b of(E.status!==j.PrefetchCacheEntryStatus.stale||G?e=(0,l.applyFlightData)(z,J,d,a,E):(e=function(a,b,c,d){let e=!1;for(let f of(a.rsc=b.rsc,a.prefetchRsc=b.prefetchRsc,a.loading=b.loading,a.parallelRoutes=new Map(b.parallelRoutes),u(d).map(a=>[...c,...a])))(0,r.clearCacheNodeDataForSegmentPath)(a,b,f),e=!0;return e}(d,J,c,s),E.lastUsedTime=z),(0,h.shouldHardNavigate)(w,I)?(d.rsc=J.rsc,d.prefetchRsc=J.prefetchRsc,(0,f.invalidateCacheBelowFlightSegmentPath)(d,J,c),A.cache=d):e&&(A.cache=d,J=d),u(s))){let a=[...c,...b];a[a.length-1]!==o.DEFAULT_SEGMENT_KEY&&K.push(a)}}I=y}}return A.patchedTree=I,A.canonicalUrl=H,A.scrollableSegments=K,A.hashFragment=B,A.shouldScroll=y,(0,k.handleMutable)(b,A)},()=>b)}}});let d=c(9008),e=c(7391),f=c(8468),g=c(6770),h=c(5951),i=c(9649),j=c(9154),k=c(9435),l=c(6928),m=c(5076),n=c(9752),o=c(3913),p=c(5956),q=c(5334),r=c(7464),s=c(9707);function t(a,b,c,d){return b.mpaNavigation=!0,b.canonicalUrl=c,b.pendingPush=d,b.scrollableSegments=void 0,(0,k.handleMutable)(a,b)}function u(a){let b=[],[c,d]=a;if(0===Object.keys(d).length)return[[c]];for(let[a,e]of Object.entries(d))for(let d of u(e))""===c?b.push([a,...d]):b.push([c,a,...d]);return b}c(593),("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},5334:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{DYNAMIC_STALETIME_MS:function(){return m},STATIC_STALETIME_MS:function(){return n},createSeededPrefetchCacheEntry:function(){return j},getOrCreatePrefetchCacheEntry:function(){return i},prunePrefetchCache:function(){return l}});let d=c(9008),e=c(9154),f=c(5076);function g(a,b,c){let d=a.pathname;return(b&&(d+=a.search),c)?""+c+"%"+d:d}function h(a,b,c){return g(a,b===e.PrefetchKind.FULL,c)}function i(a){let{url:b,nextUrl:c,tree:d,prefetchCache:f,kind:h,allowAliasing:i=!0}=a,j=function(a,b,c,d,f){for(let h of(void 0===b&&(b=e.PrefetchKind.TEMPORARY),[c,null])){let c=g(a,!0,h),i=g(a,!1,h),j=a.search?c:i,k=d.get(j);if(k&&f){if(k.url.pathname===a.pathname&&k.url.search!==a.search)return{...k,aliased:!0};return k}let l=d.get(i);if(f&&a.search&&b!==e.PrefetchKind.FULL&&l&&!l.key.includes("%"))return{...l,aliased:!0}}if(b!==e.PrefetchKind.FULL&&f){for(let b of d.values())if(b.url.pathname===a.pathname&&!b.key.includes("%"))return{...b,aliased:!0}}}(b,h,c,f,i);return j?(j.status=o(j),j.kind!==e.PrefetchKind.FULL&&h===e.PrefetchKind.FULL&&j.data.then(a=>{if(!(Array.isArray(a.flightData)&&a.flightData.some(a=>a.isRootRender&&null!==a.seedData)))return k({tree:d,url:b,nextUrl:c,prefetchCache:f,kind:null!=h?h:e.PrefetchKind.TEMPORARY})}),h&&j.kind===e.PrefetchKind.TEMPORARY&&(j.kind=h),j):k({tree:d,url:b,nextUrl:c,prefetchCache:f,kind:h||e.PrefetchKind.TEMPORARY})}function j(a){let{nextUrl:b,tree:c,prefetchCache:d,url:f,data:g,kind:i}=a,j=g.couldBeIntercepted?h(f,i,b):h(f,i),k={treeAtTimeOfPrefetch:c,data:Promise.resolve(g),kind:i,prefetchTime:Date.now(),lastUsedTime:Date.now(),staleTime:g.staleTime,key:j,status:e.PrefetchCacheEntryStatus.fresh,url:f};return d.set(j,k),k}function k(a){let{url:b,kind:c,tree:g,nextUrl:i,prefetchCache:j}=a,k=h(b,c),l=f.prefetchQueue.enqueue(()=>(0,d.fetchServerResponse)(b,{flightRouterState:g,nextUrl:i,prefetchKind:c}).then(a=>{let c;if(a.couldBeIntercepted&&(c=function(a){let{url:b,nextUrl:c,prefetchCache:d,existingCacheKey:e}=a,f=d.get(e);if(!f)return;let g=h(b,f.kind,c);return d.set(g,{...f,key:g}),d.delete(e),g}({url:b,existingCacheKey:k,nextUrl:i,prefetchCache:j})),a.prerendered){let b=j.get(null!=c?c:k);b&&(b.kind=e.PrefetchKind.FULL,-1!==a.staleTime&&(b.staleTime=a.staleTime))}return a})),m={treeAtTimeOfPrefetch:g,data:l,kind:c,prefetchTime:Date.now(),lastUsedTime:null,staleTime:-1,key:k,status:e.PrefetchCacheEntryStatus.fresh,url:b};return j.set(k,m),m}function l(a){for(let[b,c]of a)o(c)===e.PrefetchCacheEntryStatus.expired&&a.delete(b)}let m=1e3*Number("0"),n=1e3*Number("300");function o(a){let{kind:b,prefetchTime:c,lastUsedTime:d,staleTime:f}=a;return -1!==f?Date.now()<c+f?e.PrefetchCacheEntryStatus.fresh:e.PrefetchCacheEntryStatus.stale:Date.now()<(null!=d?d:c)+m?d?e.PrefetchCacheEntryStatus.reusable:e.PrefetchCacheEntryStatus.fresh:b===e.PrefetchKind.AUTO&&Date.now()<c+n?e.PrefetchCacheEntryStatus.stale:b===e.PrefetchKind.FULL&&Date.now()<c+n?e.PrefetchCacheEntryStatus.reusable:e.PrefetchCacheEntryStatus.expired}("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},5416:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{HTML_LIMITED_BOT_UA_RE:function(){return d.HTML_LIMITED_BOT_UA_RE},HTML_LIMITED_BOT_UA_RE_STRING:function(){return f},getBotType:function(){return i},isBot:function(){return h}});let d=c(5796),e=/google/i,f=d.HTML_LIMITED_BOT_UA_RE.source;function g(a){return d.HTML_LIMITED_BOT_UA_RE.test(a)}function h(a){return e.test(a)||g(a)}function i(a){return e.test(a)?"dom":g(a)?"html":void 0}},5429:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"serverActionReducer",{enumerable:!0,get:function(){return D}});let d=c(1264),e=c(1448),f=c(1563),g=c(7379),h=c(9154),i=c(6361),j=c(7391),k=c(5232),l=c(6770),m=c(9649),n=c(9435),o=c(1500),p=c(9752),q=c(8214),r=c(6493),s=c(2308),t=c(4007),u=c(6875),v=c(7860),w=c(5334),x=c(5942),y=c(6736),z=c(4642);c(593);let A=g.createFromFetch;async function B(a,b,c){let h,j,k,l,{actionId:m,actionArgs:n}=c,o=(0,g.createTemporaryReferenceSet)(),p=(0,z.extractInfoFromServerReferenceId)(m),q="use-cache"===p.type?(0,z.omitUnusedArgs)(n,p):n,r=await (0,g.encodeReply)(q,{temporaryReferences:o}),s=await fetch(a.canonicalUrl,{method:"POST",headers:{Accept:f.RSC_CONTENT_TYPE_HEADER,[f.ACTION_HEADER]:m,[f.NEXT_ROUTER_STATE_TREE_HEADER]:(0,t.prepareFlightRouterStateForRequest)(a.tree),...{},...b?{[f.NEXT_URL]:b}:{}},body:r});if("1"===s.headers.get(f.NEXT_ACTION_NOT_FOUND_HEADER))throw Object.defineProperty(Error('Server Action "'+m+'" was not found on the server. \nRead more: https://nextjs.org/docs/messages/failed-to-find-server-action'),"__NEXT_ERROR_CODE",{value:"E715",enumerable:!1,configurable:!0});let u=s.headers.get("x-action-redirect"),[w,x]=(null==u?void 0:u.split(";"))||[];switch(x){case"push":h=v.RedirectType.push;break;case"replace":h=v.RedirectType.replace;break;default:h=void 0}let y=!!s.headers.get(f.NEXT_IS_PRERENDER_HEADER);try{let a=JSON.parse(s.headers.get("x-action-revalidated")||"[[],0,0]");j={paths:a[0]||[],tag:!!a[1],cookie:a[2]}}catch(a){j=C}let B=w?(0,i.assignLocation)(w,new URL(a.canonicalUrl,window.location.href)):void 0,D=s.headers.get("content-type"),E=!!(D&&D.startsWith(f.RSC_CONTENT_TYPE_HEADER));if(!E&&!B)throw Object.defineProperty(Error(s.status>=400&&"text/plain"===D?await s.text():"An unexpected response was received from the server."),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});if(E){let a=await A(Promise.resolve(s),{callServer:d.callServer,findSourceMapURL:e.findSourceMapURL,temporaryReferences:o});k=B?void 0:a.a,l=(0,t.normalizeFlightData)(a.f)}else k=void 0,l=void 0;return{actionResult:k,actionFlightData:l,redirectLocation:B,redirectType:h,revalidatedParts:j,isPrerender:y}}let C={paths:[],tag:!1,cookie:!1};function D(a,b){let{resolve:c,reject:d}=b,e={},f=a.tree;e.preserveCustomHistoryState=!1;let g=a.nextUrl&&(0,q.hasInterceptionRouteInCurrentTree)(a.tree)?a.nextUrl:null,i=Date.now();return B(a,g,b).then(async q=>{let t,{actionResult:z,actionFlightData:A,redirectLocation:B,redirectType:C,isPrerender:D,revalidatedParts:E}=q;if(B&&(C===v.RedirectType.replace?(a.pushRef.pendingPush=!1,e.pendingPush=!1):(a.pushRef.pendingPush=!0,e.pendingPush=!0),e.canonicalUrl=t=(0,j.createHrefFromUrl)(B,!1)),!A)return(c(z),B)?(0,k.handleExternalUrl)(a,e,B.href,a.pushRef.pendingPush):a;if("string"==typeof A)return c(z),(0,k.handleExternalUrl)(a,e,A,a.pushRef.pendingPush);let F=E.paths.length>0||E.tag||E.cookie;for(let d of A){let{tree:h,seedData:j,head:n,isRootRender:q}=d;if(!q)return console.log("SERVER ACTION APPLY FAILED"),c(z),a;let u=(0,l.applyRouterStatePatchToTree)([""],f,h,t||a.canonicalUrl);if(null===u)return c(z),(0,r.handleSegmentMismatch)(a,b,h);if((0,m.isNavigatingToNewRootLayout)(f,u))return c(z),(0,k.handleExternalUrl)(a,e,t||a.canonicalUrl,a.pushRef.pendingPush);if(null!==j){let b=j[1],c=(0,p.createEmptyCacheNode)();c.rsc=b,c.prefetchRsc=null,c.loading=j[3],(0,o.fillLazyItemsTillLeafWithHead)(i,c,void 0,h,j,n,void 0),e.cache=c,e.prefetchCache=new Map,F&&await (0,s.refreshInactiveParallelSegments)({navigatedAt:i,state:a,updatedTree:u,updatedCache:c,includeNextUrl:!!g,canonicalUrl:e.canonicalUrl||a.canonicalUrl})}e.patchedTree=u,f=u}return B&&t?(F||((0,w.createSeededPrefetchCacheEntry)({url:B,data:{flightData:A,canonicalUrl:void 0,couldBeIntercepted:!1,prerendered:!1,postponed:!1,staleTime:-1},tree:a.tree,prefetchCache:a.prefetchCache,nextUrl:a.nextUrl,kind:D?h.PrefetchKind.FULL:h.PrefetchKind.AUTO}),e.prefetchCache=a.prefetchCache),d((0,u.getRedirectError)((0,y.hasBasePath)(t)?(0,x.removeBasePath)(t):t,C||v.RedirectType.push))):c(z),(0,n.handleMutable)(a,e)},b=>(d(b),a))}("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},5796:(a,b)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"HTML_LIMITED_BOT_UA_RE",{enumerable:!0,get:function(){return c}});let c=/Mediapartners-Google|Chrome-Lighthouse|Slurp|DuckDuckBot|baiduspider|yandex|sogou|bitlybot|tumblr|vkShare|quora link preview|redditbot|ia_archiver|Bingbot|BingPreview|applebot|facebookexternalhit|facebookcatalog|Twitterbot|LinkedInBot|Slackbot|Discordbot|WhatsApp|SkypeUriPreview|Yeti/i},5814:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{default:function(){return q},useLinkStatus:function(){return s}});let d=c(740),e=c(687),f=d._(c(3210)),g=c(195),h=c(2142),i=c(9154),j=c(3038),k=c(9289),l=c(6127);c(148);let m=c(3406),n=c(1794),o=c(3690);function p(a){return"string"==typeof a?a:(0,g.formatUrl)(a)}function q(a){let b,c,d,[g,q]=(0,f.useOptimistic)(m.IDLE_LINK_STATUS),s=(0,f.useRef)(null),{href:t,as:u,children:v,prefetch:w=null,passHref:x,replace:y,shallow:z,scroll:A,onClick:B,onMouseEnter:C,onTouchStart:D,legacyBehavior:E=!1,onNavigate:F,ref:G,unstable_dynamicOnHover:H,...I}=a;b=v,E&&("string"==typeof b||"number"==typeof b)&&(b=(0,e.jsx)("a",{children:b}));let J=f.default.useContext(h.AppRouterContext),K=!1!==w,L=null===w||"auto"===w?i.PrefetchKind.AUTO:i.PrefetchKind.FULL,{href:M,as:N}=f.default.useMemo(()=>{let a=p(t);return{href:a,as:u?p(u):a}},[t,u]);E&&(c=f.default.Children.only(b));let O=E?c&&"object"==typeof c&&c.ref:G,P=f.default.useCallback(a=>(null!==J&&(s.current=(0,m.mountLinkInstance)(a,M,J,L,K,q)),()=>{s.current&&((0,m.unmountLinkForCurrentNavigation)(s.current),s.current=null),(0,m.unmountPrefetchableInstance)(a)}),[K,M,J,L,q]),Q={ref:(0,j.useMergedRef)(P,O),onClick(a){E||"function"!=typeof B||B(a),E&&c.props&&"function"==typeof c.props.onClick&&c.props.onClick(a),J&&(a.defaultPrevented||function(a,b,c,d,e,g,h){let{nodeName:i}=a.currentTarget;if(!("A"===i.toUpperCase()&&function(a){let b=a.currentTarget.getAttribute("target");return b&&"_self"!==b||a.metaKey||a.ctrlKey||a.shiftKey||a.altKey||a.nativeEvent&&2===a.nativeEvent.which}(a)||a.currentTarget.hasAttribute("download"))){if(!(0,n.isLocalURL)(b)){e&&(a.preventDefault(),location.replace(b));return}if(a.preventDefault(),h){let a=!1;if(h({preventDefault:()=>{a=!0}}),a)return}f.default.startTransition(()=>{(0,o.dispatchNavigateAction)(c||b,e?"replace":"push",null==g||g,d.current)})}}(a,M,N,s,y,A,F))},onMouseEnter(a){E||"function"!=typeof C||C(a),E&&c.props&&"function"==typeof c.props.onMouseEnter&&c.props.onMouseEnter(a),J&&K&&(0,m.onNavigationIntent)(a.currentTarget,!0===H)},onTouchStart:function(a){E||"function"!=typeof D||D(a),E&&c.props&&"function"==typeof c.props.onTouchStart&&c.props.onTouchStart(a),J&&K&&(0,m.onNavigationIntent)(a.currentTarget,!0===H)}};return(0,k.isAbsoluteUrl)(N)?Q.href=N:E&&!x&&("a"!==c.type||"href"in c.props)||(Q.href=(0,l.addBasePath)(N)),d=E?f.default.cloneElement(c,Q):(0,e.jsx)("a",{...I,...Q,children:b}),(0,e.jsx)(r.Provider,{value:g,children:d})}c(2708);let r=(0,f.createContext)(m.IDLE_LINK_STATUS),s=()=>(0,f.useContext)(r);("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},5942:(a,b,c)=>{"use strict";function d(a){return a}Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"removeBasePath",{enumerable:!0,get:function(){return d}}),c(6736),("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},5951:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"shouldHardNavigate",{enumerable:!0,get:function(){return function a(b,c){let[f,g]=c,[h,i]=b;return(0,e.matchSegment)(h,f)?!(b.length<=2)&&a((0,d.getNextFlightSegmentPath)(b),g[i]):!!Array.isArray(h)}}});let d=c(4007),e=c(4077);("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},5956:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{abortTask:function(){return o},listenForDynamicRequest:function(){return n},startPPRNavigation:function(){return j},updateCacheNodeOnPopstateRestoration:function(){return function a(b,c){let d=c[1],e=b.parallelRoutes,g=new Map(e);for(let b in d){let c=d[b],h=c[0],i=(0,f.createRouterCacheKey)(h),j=e.get(b);if(void 0!==j){let d=j.get(i);if(void 0!==d){let e=a(d,c),f=new Map(j);f.set(i,e),g.set(b,f)}}}let h=b.rsc,i=r(h)&&"pending"===h.status;return{lazyData:null,rsc:h,head:b.head,prefetchHead:i?b.prefetchHead:[null,null],prefetchRsc:i?b.prefetchRsc:null,loading:b.loading,parallelRoutes:g,navigatedAt:b.navigatedAt}}}});let d=c(3913),e=c(4077),f=c(3123),g=c(9649),h=c(5334),i={route:null,node:null,dynamicRequestTree:null,children:null};function j(a,b,c,g,h,j,m,n,o){return function a(b,c,g,h,j,m,n,o,p,q,r){let s=g[1],t=h[1],u=null!==m?m[2]:null;j||!0===h[4]&&(j=!0);let v=c.parallelRoutes,w=new Map(v),x={},y=null,z=!1,A={};for(let c in t){let g,h=t[c],l=s[c],m=v.get(c),B=null!==u?u[c]:null,C=h[0],D=q.concat([c,C]),E=(0,f.createRouterCacheKey)(C),F=void 0!==l?l[0]:void 0,G=void 0!==m?m.get(E):void 0;if(null!==(g=C===d.DEFAULT_SEGMENT_KEY?void 0!==l?{route:l,node:null,dynamicRequestTree:null,children:null}:k(b,l,h,G,j,void 0!==B?B:null,n,o,D,r):p&&0===Object.keys(h[1]).length?k(b,l,h,G,j,void 0!==B?B:null,n,o,D,r):void 0!==l&&void 0!==F&&(0,e.matchSegment)(C,F)&&void 0!==G&&void 0!==l?a(b,G,l,h,j,B,n,o,p,D,r):k(b,l,h,G,j,void 0!==B?B:null,n,o,D,r))){if(null===g.route)return i;null===y&&(y=new Map),y.set(c,g);let a=g.node;if(null!==a){let b=new Map(m);b.set(E,a),w.set(c,b)}let b=g.route;x[c]=b;let d=g.dynamicRequestTree;null!==d?(z=!0,A[c]=d):A[c]=b}else x[c]=h,A[c]=h}if(null===y)return null;let B={lazyData:null,rsc:c.rsc,prefetchRsc:c.prefetchRsc,head:c.head,prefetchHead:c.prefetchHead,loading:c.loading,parallelRoutes:w,navigatedAt:b};return{route:l(h,x),node:B,dynamicRequestTree:z?l(h,A):null,children:y}}(a,b,c,g,!1,h,j,m,n,[],o)}function k(a,b,c,d,e,j,k,n,o,p){return!e&&(void 0===b||(0,g.isNavigatingToNewRootLayout)(b,c))?i:function a(b,c,d,e,g,i,j,k){let n,o,p,q,r=c[1],s=0===Object.keys(r).length;if(void 0!==d&&d.navigatedAt+h.DYNAMIC_STALETIME_MS>b)n=d.rsc,o=d.loading,p=d.head,q=d.navigatedAt;else if(null===e)return m(b,c,null,g,i,j,k);else if(n=e[1],o=e[3],p=s?g:null,q=b,e[4]||i&&s)return m(b,c,e,g,i,j,k);let t=null!==e?e[2]:null,u=new Map,v=void 0!==d?d.parallelRoutes:null,w=new Map(v),x={},y=!1;if(s)k.push(j);else for(let c in r){let d=r[c],e=null!==t?t[c]:null,h=null!==v?v.get(c):void 0,l=d[0],m=j.concat([c,l]),n=(0,f.createRouterCacheKey)(l),o=a(b,d,void 0!==h?h.get(n):void 0,e,g,i,m,k);u.set(c,o);let p=o.dynamicRequestTree;null!==p?(y=!0,x[c]=p):x[c]=d;let q=o.node;if(null!==q){let a=new Map;a.set(n,q),w.set(c,a)}}return{route:c,node:{lazyData:null,rsc:n,prefetchRsc:null,head:p,prefetchHead:null,loading:o,parallelRoutes:w,navigatedAt:q},dynamicRequestTree:y?l(c,x):null,children:u}}(a,c,d,j,k,n,o,p)}function l(a,b){let c=[a[0],b];return 2 in a&&(c[2]=a[2]),3 in a&&(c[3]=a[3]),4 in a&&(c[4]=a[4]),c}function m(a,b,c,d,e,g,h){let i=l(b,b[1]);return i[3]="refetch",{route:b,node:function a(b,c,d,e,g,h,i){let j=c[1],k=null!==d?d[2]:null,l=new Map;for(let c in j){let d=j[c],m=null!==k?k[c]:null,n=d[0],o=h.concat([c,n]),p=(0,f.createRouterCacheKey)(n),q=a(b,d,void 0===m?null:m,e,g,o,i),r=new Map;r.set(p,q),l.set(c,r)}let m=0===l.size;m&&i.push(h);let n=null!==d?d[1]:null,o=null!==d?d[3]:null;return{lazyData:null,parallelRoutes:l,prefetchRsc:void 0!==n?n:null,prefetchHead:m?e:[null,null],loading:void 0!==o?o:null,rsc:s(),head:m?s():null,navigatedAt:b}}(a,b,c,d,e,g,h),dynamicRequestTree:i,children:null}}function n(a,b){b.then(b=>{let{flightData:c}=b;if("string"!=typeof c){for(let b of c){let{segmentPath:c,tree:d,seedData:g,head:h}=b;g&&function(a,b,c,d,g){let h=a;for(let a=0;a<b.length;a+=2){let c=b[a],d=b[a+1],f=h.children;if(null!==f){let a=f.get(c);if(void 0!==a){let b=a.route[0];if((0,e.matchSegment)(d,b)){h=a;continue}}}return}!function a(b,c,d,g){if(null===b.dynamicRequestTree)return;let h=b.children,i=b.node;if(null===h){null!==i&&(function a(b,c,d,g,h){let i=c[1],j=d[1],k=g[2],l=b.parallelRoutes;for(let b in i){let c=i[b],d=j[b],g=k[b],m=l.get(b),n=c[0],o=(0,f.createRouterCacheKey)(n),q=void 0!==m?m.get(o):void 0;void 0!==q&&(void 0!==d&&(0,e.matchSegment)(n,d[0])&&null!=g?a(q,c,d,g,h):p(c,q,null))}let m=b.rsc,n=g[1];null===m?b.rsc=n:r(m)&&m.resolve(n);let o=b.head;r(o)&&o.resolve(h)}(i,b.route,c,d,g),b.dynamicRequestTree=null);return}let j=c[1],k=d[2];for(let b in c){let c=j[b],d=k[b],f=h.get(b);if(void 0!==f){let b=f.route[0];if((0,e.matchSegment)(c[0],b)&&null!=d)return a(f,c,d,g)}}}(h,c,d,g)}(a,c,d,g,h)}o(a,null)}},b=>{o(a,b)})}function o(a,b){let c=a.node;if(null===c)return;let d=a.children;if(null===d)p(a.route,c,b);else for(let a of d.values())o(a,b);a.dynamicRequestTree=null}function p(a,b,c){let d=a[1],e=b.parallelRoutes;for(let a in d){let b=d[a],g=e.get(a);if(void 0===g)continue;let h=b[0],i=(0,f.createRouterCacheKey)(h),j=g.get(i);void 0!==j&&p(b,j,c)}let g=b.rsc;r(g)&&(null===c?g.resolve(null):g.reject(c));let h=b.head;r(h)&&h.resolve(null)}let q=Symbol();function r(a){return a&&a.tag===q}function s(){let a,b,c=new Promise((c,d)=>{a=c,b=d});return c.status="pending",c.resolve=b=>{"pending"===c.status&&(c.status="fulfilled",c.value=b,a(b))},c.reject=a=>{"pending"===c.status&&(c.status="rejected",c.reason=a,b(a))},c.tag=q,c}("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},6044:(a,b,c)=>{"use strict";c.d(b,{xQ:()=>f});var d=c(3210),e=c(1279);function f(a=!0){let b=(0,d.useContext)(e.t);if(null===b)return[!0,null];let{isPresent:c,onExitComplete:g,register:h}=b,i=(0,d.useId)();(0,d.useEffect)(()=>{if(a)return h(i)},[a]);let j=(0,d.useCallback)(()=>a&&g&&g(i),[i,g,a]);return!c&&g?[!1,j]:[!0]}},6127:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"addBasePath",{enumerable:!0,get:function(){return f}});let d=c(8834),e=c(4674);function f(a,b){return(0,e.normalizePathTrailingSlash)((0,d.addPathPrefix)(a,""))}("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},6229:(a,b,c)=>{"use strict";c.d(b,{GalleryPreview:()=>d});let d=(0,c(1369).registerClientReference)(function(){throw Error("Attempted to call GalleryPreview() from the server but GalleryPreview is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\gallery-preview.tsx","GalleryPreview")},6277:(a,b,c)=>{"use strict";c.d(b,{MenuHighlights:()=>n});var d=c(687),e=c(4124),f=c(4493),g=c(9523),h=c(6834),i=c(4398),j=c(4082);let k=[{id:"hd1",name:"Himalayan Coffee",description:"Our signature blend from Ilam hills, rich and aromatic with notes of chocolate and nuts",price:180,image:"/images/himalayan-coffee.jpg",category:"Hot Drinks",isPopular:!0},{id:"hd2",name:"Masala Chai",description:"Traditional spiced tea with cardamom, cinnamon, and ginger, served with milk",price:120,image:"/images/masala-chai.jpg",category:"Hot Drinks",isPopular:!0,isVegetarian:!0},{id:"hd3",name:"Butter Tea (Po Cha)",description:"Traditional Tibetan butter tea, perfect for cold Biratnagar mornings",price:150,image:"/images/butter-tea.jpg",category:"Hot Drinks",isVegetarian:!0},{id:"hd4",name:"Cappuccino",description:"Classic Italian coffee with steamed milk foam and a sprinkle of cocoa",price:200,image:"/images/cappuccino.jpg",category:"Hot Drinks",isVegetarian:!0},{id:"hd5",name:"Hot Chocolate",description:"Rich and creamy hot chocolate topped with whipped cream",price:220,image:"/images/hot-chocolate.jpg",category:"Hot Drinks",isVegetarian:!0},{id:"cd1",name:"Iced Himalayan Coffee",description:"Our signature coffee served cold with ice and a touch of condensed milk",price:200,image:"/images/iced-coffee.jpg",category:"Cold Drinks",isPopular:!0},{id:"cd2",name:"Mango Lassi",description:"Creamy yogurt drink blended with fresh mango and cardamom",price:180,image:"/images/mango-lassi.jpg",category:"Cold Drinks",isVegetarian:!0},{id:"cd3",name:"Fresh Lime Soda",description:"Refreshing lime juice with soda water and mint leaves",price:120,image:"/images/lime-soda.jpg",category:"Cold Drinks",isVegan:!0},{id:"cd4",name:"Iced Chai Latte",description:"Spiced chai served cold with milk and ice",price:160,image:"/images/iced-chai.jpg",category:"Cold Drinks",isVegetarian:!0},{id:"sn1",name:"Chicken Momo",description:"Traditional Nepali dumplings filled with seasoned chicken, served with spicy sauce",price:280,image:"/images/chicken-momo.jpg",category:"Snacks",isPopular:!0},{id:"sn2",name:"Vegetable Momo",description:"Steamed dumplings filled with fresh vegetables and herbs",price:240,image:"/images/veg-momo.jpg",category:"Snacks",isVegetarian:!0,isPopular:!0},{id:"sn3",name:"Samosa Chat",description:"Crispy samosas topped with yogurt, chutneys, and spices",price:160,image:"/images/samosa-chat.jpg",category:"Snacks",isVegetarian:!0},{id:"sn4",name:"French Fries",description:"Golden crispy fries served with ketchup and mayo",price:180,image:"/images/french-fries.jpg",category:"Snacks",isVegetarian:!0},{id:"bk1",name:"Chocolate Croissant",description:"Buttery croissant filled with rich dark chocolate",price:150,image:"/images/chocolate-croissant.jpg",category:"Bakery",isVegetarian:!0},{id:"bk2",name:"Banana Bread",description:"Homemade banana bread with walnuts, perfect with coffee",price:120,image:"/images/banana-bread.jpg",category:"Bakery",isVegetarian:!0,isPopular:!0},{id:"bk3",name:"Blueberry Muffin",description:"Fresh baked muffin loaded with juicy blueberries",price:140,image:"/images/blueberry-muffin.jpg",category:"Bakery",isVegetarian:!0},{id:"tn1",name:"Dal Bhat Set",description:"Traditional Nepali meal with lentil soup, rice, vegetables, and pickle",price:350,image:"/images/dal-bhat.jpg",category:"Traditional Nepali",isVegetarian:!0,isPopular:!0},{id:"tn2",name:"Sel Roti",description:"Traditional ring-shaped rice bread, crispy outside and soft inside",price:80,image:"/images/sel-roti.jpg",category:"Traditional Nepali",isVegetarian:!0},{id:"bf1",name:"English Breakfast",description:"Eggs, toast, baked beans, and hash browns with coffee",price:420,image:"/images/english-breakfast.jpg",category:"Breakfast",isVegetarian:!0},{id:"bf2",name:"Pancakes",description:"Fluffy pancakes served with maple syrup and butter",price:280,image:"/images/pancakes.jpg",category:"Breakfast",isVegetarian:!0}].filter(a=>a.isPopular);var l=c(5814),m=c.n(l);function n(){let a=k.slice(0,6);return(0,d.jsx)("section",{className:"py-20 bg-cafe-cream",children:(0,d.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,d.jsxs)(e.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.6},viewport:{once:!0},className:"text-center mb-16",children:[(0,d.jsx)("h2",{className:"font-serif text-4xl md:text-5xl font-bold text-cafe-dark-brown mb-6",children:"Menu Highlights"}),(0,d.jsx)("p",{className:"text-xl text-cafe-brown max-w-3xl mx-auto leading-relaxed",children:"Discover our most beloved dishes and drinks, crafted with love and the finest ingredients from Nepal and beyond."})]}),(0,d.jsx)("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12",children:a.map((a,b)=>(0,d.jsx)(e.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.1*b},viewport:{once:!0},children:(0,d.jsxs)(f.Zp,{className:"h-full overflow-hidden border-cafe-beige hover:shadow-cafe-lg transition-all duration-300 group",children:[(0,d.jsxs)("div",{className:"relative aspect-[4/3] overflow-hidden",children:[(0,d.jsx)("img",{src:a.image,alt:a.name,className:"w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"}),(0,d.jsxs)("div",{className:"absolute top-4 left-4 flex gap-2",children:[a.isPopular&&(0,d.jsxs)(h.E,{className:"bg-cafe-gold text-cafe-dark-brown font-semibold",children:[(0,d.jsx)(i.A,{className:"h-3 w-3 mr-1"}),"Popular"]}),a.isVegetarian&&(0,d.jsxs)(h.E,{variant:"secondary",className:"bg-cafe-green text-white",children:[(0,d.jsx)(j.A,{className:"h-3 w-3 mr-1"}),"Veg"]})]})]}),(0,d.jsx)(f.Wu,{className:"p-6",children:(0,d.jsxs)("div",{className:"space-y-3",children:[(0,d.jsxs)("div",{className:"flex justify-between items-start",children:[(0,d.jsx)("h3",{className:"font-serif text-xl font-semibold text-cafe-dark-brown",children:a.name}),(0,d.jsxs)("span",{className:"text-lg font-bold text-cafe-brown",children:["Rs. ",a.price]})]}),(0,d.jsx)("p",{className:"text-cafe-brown leading-relaxed text-sm",children:a.description}),(0,d.jsx)("div",{className:"pt-2",children:(0,d.jsx)(h.E,{variant:"outline",className:"border-cafe-beige text-cafe-brown",children:a.category})})]})})]})},a.id))}),(0,d.jsx)(e.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.3},viewport:{once:!0},className:"text-center",children:(0,d.jsx)(m(),{href:"/menu",children:(0,d.jsx)(g.$,{size:"lg",className:"bg-cafe-brown hover:bg-cafe-dark-brown text-cafe-cream px-8 py-3 text-lg font-semibold shadow-cafe-lg",children:"View Full Menu"})})})]})})}},6312:(a,b,c)=>{"use strict";function d(a,b){if(!Object.prototype.hasOwnProperty.call(a,b))throw TypeError("attempted to use private field on non-instance");return a}c.r(b),c.d(b,{_:()=>d})},6349:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(2688).A)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},6361:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"assignLocation",{enumerable:!0,get:function(){return e}});let d=c(6127);function e(a,b){if(a.startsWith(".")){let c=b.origin+b.pathname;return new URL((c.endsWith("/")?c:c+"/")+a)}return new URL((0,d.addBasePath)(a),b.href)}("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},6439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},6443:(a,b,c)=>{"use strict";c.d(b,{PackagesSection:()=>d});let d=(0,c(1369).registerClientReference)(function(){throw Error("Attempted to call PackagesSection() from the server but PackagesSection is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\packages-section.tsx","PackagesSection")},6493:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"handleSegmentMismatch",{enumerable:!0,get:function(){return e}});let d=c(5232);function e(a,b,c){return(0,d.handleExternalUrl)(a,{},a.canonicalUrl,!0)}("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},6616:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,5227,23)),Promise.resolve().then(c.t.bind(c,6346,23)),Promise.resolve().then(c.t.bind(c,7924,23)),Promise.resolve().then(c.t.bind(c,99,23)),Promise.resolve().then(c.t.bind(c,8243,23)),Promise.resolve().then(c.t.bind(c,8827,23)),Promise.resolve().then(c.t.bind(c,2763,23)),Promise.resolve().then(c.t.bind(c,7173,23)),Promise.resolve().then(c.bind(c,5587))},6713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},6715:(a,b)=>{"use strict";function c(a){let b={};for(let[c,d]of a.entries()){let a=b[c];void 0===a?b[c]=d:Array.isArray(a)?a.push(d):b[c]=[a,d]}return b}function d(a){return"string"==typeof a?a:("number"!=typeof a||isNaN(a))&&"boolean"!=typeof a?"":String(a)}function e(a){let b=new URLSearchParams;for(let[c,e]of Object.entries(a))if(Array.isArray(e))for(let a of e)b.append(c,d(a));else b.set(c,d(e));return b}function f(a){for(var b=arguments.length,c=Array(b>1?b-1:0),d=1;d<b;d++)c[d-1]=arguments[d];for(let b of c){for(let c of b.keys())a.delete(c);for(let[c,d]of b.entries())a.append(c,d)}return a}Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{assign:function(){return f},searchParamsToUrlQuery:function(){return c},urlQueryToSearchParams:function(){return e}})},6736:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"hasBasePath",{enumerable:!0,get:function(){return e}});let d=c(2255);function e(a){return(0,d.pathHasPrefix)(a,"")}("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},6770:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"applyRouterStatePatchToTree",{enumerable:!0,get:function(){return function a(b,c,d,i){let j,[k,l,m,n,o]=c;if(1===b.length){let a=h(c,d);return(0,g.addRefreshMarkerToActiveParallelSegments)(a,i),a}let[p,q]=b;if(!(0,f.matchSegment)(p,k))return null;if(2===b.length)j=h(l[q],d);else if(null===(j=a((0,e.getNextFlightSegmentPath)(b),l[q],d,i)))return null;let r=[b[0],{...l,[q]:j},m,n];return o&&(r[4]=!0),(0,g.addRefreshMarkerToActiveParallelSegments)(r,i),r}}});let d=c(3913),e=c(4007),f=c(4077),g=c(2308);function h(a,b){let[c,e]=a,[g,i]=b;if(g===d.DEFAULT_SEGMENT_KEY&&c!==d.DEFAULT_SEGMENT_KEY)return a;if((0,f.matchSegment)(c,g)){let b={};for(let a in e)void 0!==i[a]?b[a]=h(e[a],i[a]):b[a]=e[a];for(let a in i)b[a]||(b[a]=i[a]);let d=[c,b];return a[2]&&(d[2]=a[2]),a[3]&&(d[3]=a[3]),a[4]&&(d[4]=a[4]),d}return b}("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},6834:(a,b,c)=>{"use strict";c.d(b,{E:()=>i});var d=c(687);c(3210);var e=c(8730),f=c(4224),g=c(1743);let h=(0,f.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function i({className:a,variant:b,asChild:c=!1,...f}){let i=c?e.DX:"span";return(0,d.jsx)(i,{"data-slot":"badge",className:(0,g.cn)(h({variant:b}),a),...f})}},6849:(a,b,c)=>{"use strict";c.d(b,{MenuHighlights:()=>d});let d=(0,c(1369).registerClientReference)(function(){throw Error("Attempted to call MenuHighlights() from the server but MenuHighlights is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\menu-highlights.tsx","MenuHighlights")},6928:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"applyFlightData",{enumerable:!0,get:function(){return f}});let d=c(1500),e=c(3898);function f(a,b,c,f,g){let{tree:h,seedData:i,head:j,isRootRender:k}=f;if(null===i)return!1;if(k){let e=i[1];c.loading=i[3],c.rsc=e,c.prefetchRsc=null,(0,d.fillLazyItemsTillLeafWithHead)(a,c,b,h,i,j,g)}else c.rsc=b.rsc,c.prefetchRsc=b.prefetchRsc,c.parallelRoutes=new Map(b.parallelRoutes),c.loading=b.loading,(0,e.fillCacheWithNewSubTreeData)(a,c,b,f,g);return!0}("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},7022:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"AppRouterAnnouncer",{enumerable:!0,get:function(){return g}});let d=c(3210),e=c(1215),f="next-route-announcer";function g(a){let{tree:b}=a,[c,g]=(0,d.useState)(null);(0,d.useEffect)(()=>(g(function(){var a;let b=document.getElementsByName(f)[0];if(null==b||null==(a=b.shadowRoot)?void 0:a.childNodes[0])return b.shadowRoot.childNodes[0];{let a=document.createElement(f);a.style.cssText="position:absolute";let b=document.createElement("div");return b.ariaLive="assertive",b.id="__next-route-announcer__",b.role="alert",b.style.cssText="position:absolute;border:0;height:1px;margin:-1px;padding:0;width:1px;clip:rect(0 0 0 0);overflow:hidden;white-space:nowrap;word-wrap:normal",a.attachShadow({mode:"open"}).appendChild(b),document.body.appendChild(a),b}}()),()=>{let a=document.getElementsByTagName(f)[0];(null==a?void 0:a.isConnected)&&document.body.removeChild(a)}),[]);let[h,i]=(0,d.useState)(""),j=(0,d.useRef)(void 0);return(0,d.useEffect)(()=>{let a="";if(document.title)a=document.title;else{let b=document.querySelector("h1");b&&(a=b.innerText||b.textContent||"")}void 0!==j.current&&j.current!==a&&i(a),j.current=a},[b]),c?(0,e.createPortal)(h,c):null}("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},7028:(a,b,c)=>{"use strict";c.d(b,{TestimonialsSection:()=>d});let d=(0,c(1369).registerClientReference)(function(){throw Error("Attempted to call TestimonialsSection() from the server but TestimonialsSection is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\testimonials-section.tsx","TestimonialsSection")},7044:(a,b,c)=>{"use strict";c.d(b,{B:()=>d});let d="undefined"!=typeof window},7464:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"clearCacheNodeDataForSegmentPath",{enumerable:!0,get:function(){return function a(b,c,f){let g=f.length<=2,[h,i]=f,j=(0,e.createRouterCacheKey)(i),k=c.parallelRoutes.get(h),l=b.parallelRoutes.get(h);l&&l!==k||(l=new Map(k),b.parallelRoutes.set(h,l));let m=null==k?void 0:k.get(j),n=l.get(j);if(g){n&&n.lazyData&&n!==m||l.set(j,{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:-1});return}if(!n||!m){n||l.set(j,{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:-1});return}return n===m&&(n={lazyData:n.lazyData,rsc:n.rsc,prefetchRsc:n.prefetchRsc,head:n.head,prefetchHead:n.prefetchHead,parallelRoutes:new Map(n.parallelRoutes),loading:n.loading},l.set(j,n)),a(n,m,(0,d.getNextFlightSegmentPath)(f))}}});let d=c(4007),e=c(3123);("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},7936:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"hmrRefreshReducer",{enumerable:!0,get:function(){return d}}),c(9008),c(7391),c(6770),c(9649),c(5232),c(9435),c(6928),c(9752),c(6493),c(8214);let d=function(a,b){return a};("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},7992:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(2688).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},8300:(a,b,c)=>{"use strict";c.d(b,{HeroSection:()=>d});let d=(0,c(1369).registerClientReference)(function(){throw Error("Attempted to call HeroSection() from the server but HeroSection is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\hero-section.tsx","HeroSection")},8340:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(2688).A)("phone",[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]])},8354:a=>{"use strict";a.exports=require("util")},8465:(a,b,c)=>{"use strict";c.d(b,{I:()=>d});let d={name:"Himalayan Brew Caf\xe9",tagline:"Where Mountains Meet Coffee",description:"A cozy corner in the heart of Biratnagar, serving authentic Nepali hospitality with the finest coffee and local delicacies.",story:`Nestled in the vibrant city of Biratnagar, Himalayan Brew Caf\xe9 was born from a passion for bringing people together over exceptional coffee and warm conversations. 

Our journey began in 2020 when our founder, inspired by the rich coffee culture of Nepal's hills and the bustling energy of Biratnagar, decided to create a space that celebrates both tradition and modernity.

We source our coffee beans directly from the hills of Ilam and Gulmi, supporting local farmers and ensuring every cup tells a story of Nepal's coffee heritage. Our caf\xe9 is more than just a place to grab coffee – it's a community hub where students study, friends catch up, and families create memories.

Located in the commercial heart of Biratnagar, we've become a beloved gathering spot for locals and visitors alike. Our commitment to quality, sustainability, and community has made us a cornerstone of Biratnagar's growing caf\xe9 culture.`,values:[{title:"Community First",description:"We believe in fostering connections and supporting our local community through every cup we serve."},{title:"Quality & Authenticity",description:"From bean to cup, we maintain the highest standards while honoring traditional Nepali coffee culture."},{title:"Sustainability",description:"We work directly with local farmers and use eco-friendly practices to protect our beautiful Nepal."},{title:"Warm Hospitality",description:"Every guest is treated like family, embodying the true spirit of Nepali hospitality."}],contact:{address:"Main Road, Biratnagar-13, Morang, Nepal",phone:"+977-21-525-789",email:"<EMAIL>",coordinates:{lat:26.4525,lng:87.2718}},hours:{weekdays:"6:00 AM - 10:00 PM",weekends:"7:00 AM - 11:00 PM",holidays:"8:00 AM - 9:00 PM"},socialMedia:{facebook:"https://facebook.com/himalayanbrew",instagram:"https://instagram.com/himalayanbrew",twitter:"https://twitter.com/himalayanbrew"}}},8468:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"invalidateCacheBelowFlightSegmentPath",{enumerable:!0,get:function(){return function a(b,c,f){let g=f.length<=2,[h,i]=f,j=(0,d.createRouterCacheKey)(i),k=c.parallelRoutes.get(h);if(!k)return;let l=b.parallelRoutes.get(h);if(l&&l!==k||(l=new Map(k),b.parallelRoutes.set(h,l)),g)return void l.delete(j);let m=k.get(j),n=l.get(j);n&&m&&(n===m&&(n={lazyData:n.lazyData,rsc:n.rsc,prefetchRsc:n.prefetchRsc,head:n.head,prefetchHead:n.prefetchHead,parallelRoutes:new Map(n.parallelRoutes)},l.set(j,n)),a(n,m,(0,e.getNextFlightSegmentPath)(f)))}}});let d=c(3123),e=c(4007);("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},8599:(a,b,c)=>{"use strict";c.d(b,{s:()=>g,t:()=>f});var d=c(3210);function e(a,b){if("function"==typeof a)return a(b);null!=a&&(a.current=b)}function f(...a){return b=>{let c=!1,d=a.map(a=>{let d=e(a,b);return c||"function"!=typeof d||(c=!0),d});if(c)return()=>{for(let b=0;b<d.length;b++){let c=d[b];"function"==typeof c?c():e(a[b],null)}}}}function g(...a){return d.useCallback(f(...a),a)}},8627:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"restoreReducer",{enumerable:!0,get:function(){return f}});let d=c(7391),e=c(642);function f(a,b){var c;let{url:f,tree:g}=b,h=(0,d.createHrefFromUrl)(f),i=g||a.tree,j=a.cache;return{canonicalUrl:h,pushRef:{pendingPush:!1,mpaNavigation:!1,preserveCustomHistoryState:!0},focusAndScrollRef:a.focusAndScrollRef,cache:j,prefetchCache:a.prefetchCache,tree:i,nextUrl:null!=(c=(0,e.extractPathFromFlightRouterState)(i))?c:f.pathname}}c(5956),("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},8730:(a,b,c)=>{"use strict";c.d(b,{DX:()=>h,TL:()=>g});var d=c(3210),e=c(8599),f=c(687);function g(a){let b=function(a){let b=d.forwardRef((a,b)=>{let{children:c,...f}=a;if(d.isValidElement(c)){var g;let a,h,i=(g=c,(h=(a=Object.getOwnPropertyDescriptor(g.props,"ref")?.get)&&"isReactWarning"in a&&a.isReactWarning)?g.ref:(h=(a=Object.getOwnPropertyDescriptor(g,"ref")?.get)&&"isReactWarning"in a&&a.isReactWarning)?g.props.ref:g.props.ref||g.ref),j=function(a,b){let c={...b};for(let d in b){let e=a[d],f=b[d];/^on[A-Z]/.test(d)?e&&f?c[d]=(...a)=>{let b=f(...a);return e(...a),b}:e&&(c[d]=e):"style"===d?c[d]={...e,...f}:"className"===d&&(c[d]=[e,f].filter(Boolean).join(" "))}return{...a,...c}}(f,c.props);return c.type!==d.Fragment&&(j.ref=b?(0,e.t)(b,i):i),d.cloneElement(c,j)}return d.Children.count(c)>1?d.Children.only(null):null});return b.displayName=`${a}.SlotClone`,b}(a),c=d.forwardRef((a,c)=>{let{children:e,...g}=a,h=d.Children.toArray(e),i=h.find(j);if(i){let a=i.props.children,e=h.map(b=>b!==i?b:d.Children.count(a)>1?d.Children.only(null):d.isValidElement(a)?a.props.children:null);return(0,f.jsx)(b,{...g,ref:c,children:d.isValidElement(a)?d.cloneElement(a,void 0,e):null})}return(0,f.jsx)(b,{...g,ref:c,children:e})});return c.displayName=`${a}.Slot`,c}var h=g("Slot"),i=Symbol("radix.slottable");function j(a){return d.isValidElement(a)&&"function"==typeof a.type&&"__radixId"in a.type&&a.type.__radixId===i}},8830:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"reducer",{enumerable:!0,get:function(){return d}}),c(9154),c(5232),c(9651),c(8627),c(8866),c(5076),c(7936),c(5429);let d=function(a,b){return a};("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},8834:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"addPathPrefix",{enumerable:!0,get:function(){return e}});let d=c(3931);function e(a,b){if(!a.startsWith("/")||!b)return a;let{pathname:c,query:e,hash:f}=(0,d.parsePath)(a);return""+b+c+e+f}},8866:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"refreshReducer",{enumerable:!0,get:function(){return o}});let d=c(9008),e=c(7391),f=c(6770),g=c(9649),h=c(5232),i=c(9435),j=c(1500),k=c(9752),l=c(6493),m=c(8214),n=c(2308);function o(a,b){let{origin:c}=b,o={},p=a.canonicalUrl,q=a.tree;o.preserveCustomHistoryState=!1;let r=(0,k.createEmptyCacheNode)(),s=(0,m.hasInterceptionRouteInCurrentTree)(a.tree);r.lazyData=(0,d.fetchServerResponse)(new URL(p,c),{flightRouterState:[q[0],q[1],q[2],"refetch"],nextUrl:s?a.nextUrl:null});let t=Date.now();return r.lazyData.then(async c=>{let{flightData:d,canonicalUrl:k}=c;if("string"==typeof d)return(0,h.handleExternalUrl)(a,o,d,a.pushRef.pendingPush);for(let c of(r.lazyData=null,d)){let{tree:d,seedData:i,head:m,isRootRender:u}=c;if(!u)return console.log("REFRESH FAILED"),a;let v=(0,f.applyRouterStatePatchToTree)([""],q,d,a.canonicalUrl);if(null===v)return(0,l.handleSegmentMismatch)(a,b,d);if((0,g.isNavigatingToNewRootLayout)(q,v))return(0,h.handleExternalUrl)(a,o,p,a.pushRef.pendingPush);let w=k?(0,e.createHrefFromUrl)(k):void 0;if(k&&(o.canonicalUrl=w),null!==i){let a=i[1],b=i[3];r.rsc=a,r.prefetchRsc=null,r.loading=b,(0,j.fillLazyItemsTillLeafWithHead)(t,r,void 0,d,i,m,void 0),o.prefetchCache=new Map}await (0,n.refreshInactiveParallelSegments)({navigatedAt:t,state:a,updatedTree:v,updatedCache:r,includeNextUrl:s,canonicalUrl:o.canonicalUrl||a.canonicalUrl}),o.cache=r,o.patchedTree=v,q=v}return(0,i.handleMutable)(a,o)},()=>a)}c(593),("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},8993:()=>{},9018:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{GracefulDegradeBoundary:function(){return f},default:function(){return g}});let d=c(687),e=c(3210);class f extends e.Component{static getDerivedStateFromError(a){return{hasError:!0}}componentDidMount(){let a=this.htmlRef.current;this.state.hasError&&a&&Object.entries(this.htmlAttributes).forEach(b=>{let[c,d]=b;a.setAttribute(c,d)})}render(){let{hasError:a}=this.state;return a?(0,d.jsx)("html",{ref:this.htmlRef,suppressHydrationWarning:!0,dangerouslySetInnerHTML:{__html:this.rootHtml}}):this.props.children}constructor(a){super(a),this.state={hasError:!1},this.rootHtml="",this.htmlAttributes={},this.htmlRef=(0,e.createRef)()}}let g=f;("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},9121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9253:(a,b,c)=>{"use strict";c.d(b,{AboutSection:()=>d});let d=(0,c(1369).registerClientReference)(function(){throw Error("Attempted to call AboutSection() from the server but AboutSection is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\about-section.tsx","AboutSection")},9289:(a,b)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{DecodeError:function(){return o},MiddlewareNotFoundError:function(){return s},MissingStaticPage:function(){return r},NormalizeError:function(){return p},PageNotFoundError:function(){return q},SP:function(){return m},ST:function(){return n},WEB_VITALS:function(){return c},execOnce:function(){return d},getDisplayName:function(){return i},getLocationOrigin:function(){return g},getURL:function(){return h},isAbsoluteUrl:function(){return f},isResSent:function(){return j},loadGetInitialProps:function(){return l},normalizeRepeatedSlashes:function(){return k},stringifyError:function(){return t}});let c=["CLS","FCP","FID","INP","LCP","TTFB"];function d(a){let b,c=!1;return function(){for(var d=arguments.length,e=Array(d),f=0;f<d;f++)e[f]=arguments[f];return c||(c=!0,b=a(...e)),b}}let e=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,f=a=>e.test(a);function g(){let{protocol:a,hostname:b,port:c}=window.location;return a+"//"+b+(c?":"+c:"")}function h(){let{href:a}=window.location,b=g();return a.substring(b.length)}function i(a){return"string"==typeof a?a:a.displayName||a.name||"Unknown"}function j(a){return a.finished||a.headersSent}function k(a){let b=a.split("?");return b[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(b[1]?"?"+b.slice(1).join("?"):"")}async function l(a,b){let c=b.res||b.ctx&&b.ctx.res;if(!a.getInitialProps)return b.ctx&&b.Component?{pageProps:await l(b.Component,b.ctx)}:{};let d=await a.getInitialProps(b);if(c&&j(c))return d;if(!d)throw Object.defineProperty(Error('"'+i(a)+'.getInitialProps()" should resolve to an object. But found "'+d+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return d}let m="undefined"!=typeof performance,n=m&&["mark","measure","getEntriesByName"].every(a=>"function"==typeof performance[a]);class o extends Error{}class p extends Error{}class q extends Error{constructor(a){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+a}}class r extends Error{constructor(a,b){super(),this.message="Failed to load static file for page: "+a+" "+b}}class s extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function t(a){return JSON.stringify({message:a.message,stack:a.stack})}},9294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9384:(a,b,c)=>{"use strict";function d(){for(var a,b,c=0,d="",e=arguments.length;c<e;c++)(a=arguments[c])&&(b=function a(b){var c,d,e="";if("string"==typeof b||"number"==typeof b)e+=b;else if("object"==typeof b)if(Array.isArray(b)){var f=b.length;for(c=0;c<f;c++)b[c]&&(d=a(b[c]))&&(e&&(e+=" "),e+=d)}else for(d in b)b[d]&&(e&&(e+=" "),e+=d);return e}(a))&&(d&&(d+=" "),d+=b);return d}c.d(b,{$:()=>d})},9435:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"handleMutable",{enumerable:!0,get:function(){return f}});let d=c(642);function e(a){return void 0!==a}function f(a,b){var c,f;let g=null==(c=b.shouldScroll)||c,h=a.nextUrl;if(e(b.patchedTree)){let c=(0,d.computeChangedPath)(a.tree,b.patchedTree);c?h=c:h||(h=a.canonicalUrl)}return{canonicalUrl:e(b.canonicalUrl)?b.canonicalUrl===a.canonicalUrl?a.canonicalUrl:b.canonicalUrl:a.canonicalUrl,pushRef:{pendingPush:e(b.pendingPush)?b.pendingPush:a.pushRef.pendingPush,mpaNavigation:e(b.mpaNavigation)?b.mpaNavigation:a.pushRef.mpaNavigation,preserveCustomHistoryState:e(b.preserveCustomHistoryState)?b.preserveCustomHistoryState:a.pushRef.preserveCustomHistoryState},focusAndScrollRef:{apply:!!g&&(!!e(null==b?void 0:b.scrollableSegments)||a.focusAndScrollRef.apply),onlyHashChange:b.onlyHashChange||!1,hashFragment:g?b.hashFragment&&""!==b.hashFragment?decodeURIComponent(b.hashFragment.slice(1)):a.focusAndScrollRef.hashFragment:null,segmentPaths:g?null!=(f=null==b?void 0:b.scrollableSegments)?f:a.focusAndScrollRef.segmentPaths:[]},cache:b.cache?b.cache:a.cache,prefetchCache:b.prefetchCache?b.prefetchCache:a.prefetchCache,tree:e(b.patchedTree)?b.patchedTree:a.tree,nextUrl:h}}("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},9523:(a,b,c)=>{"use strict";c.d(b,{$:()=>i});var d=c(687);c(3210);var e=c(8730),f=c(4224),g=c(1743);let h=(0,f.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function i({className:a,variant:b,size:c,asChild:f=!1,...i}){let j=f?e.DX:"button";return(0,d.jsx)(j,{"data-slot":"button",className:(0,g.cn)(h({variant:b,size:c,className:a})),...i})}},9638:(a,b,c)=>{"use strict";c.d(b,{TestimonialsSection:()=>V});var d=c(687),e=c(4124),f=c(4493),g=c(9523),h=c(3210);function i(a){return"[object Object]"===Object.prototype.toString.call(a)||Array.isArray(a)}function j(a,b){let c=Object.keys(a),d=Object.keys(b);return c.length===d.length&&JSON.stringify(Object.keys(a.breakpoints||{}))===JSON.stringify(Object.keys(b.breakpoints||{}))&&c.every(c=>{let d=a[c],e=b[c];return"function"==typeof d?`${d}`==`${e}`:i(d)&&i(e)?j(d,e):d===e})}function k(a){return a.concat().sort((a,b)=>a.name>b.name?1:-1).map(a=>a.options)}function l(a){return"number"==typeof a}function m(a){return"string"==typeof a}function n(a){return"boolean"==typeof a}function o(a){return"[object Object]"===Object.prototype.toString.call(a)}function p(a){return Math.abs(a)}function q(a){return Math.sign(a)}function r(a){return v(a).map(Number)}function s(a){return a[t(a)]}function t(a){return Math.max(0,a.length-1)}function u(a,b=0){return Array.from(Array(a),(a,c)=>b+c)}function v(a){return Object.keys(a)}function w(a,b){return void 0!==b.MouseEvent&&a instanceof b.MouseEvent}function x(){let a=[],b={add:function(c,d,e,f={passive:!0}){let g;return"addEventListener"in c?(c.addEventListener(d,e,f),g=()=>c.removeEventListener(d,e,f)):(c.addListener(e),g=()=>c.removeListener(e)),a.push(g),b},clear:function(){a=a.filter(a=>a())}};return b}function y(a=0,b=0){let c=p(a-b);function d(c){return c<a||c>b}return{length:c,max:b,min:a,constrain:function(c){return d(c)?c<a?a:b:c},reachedAny:d,reachedMax:function(a){return a>b},reachedMin:function(b){return b<a},removeOffset:function(a){return c?a-c*Math.ceil((a-b)/c):a}}}function z(a){let b=a;function c(a){return l(a)?a:a.get()}return{get:function(){return b},set:function(a){b=c(a)},add:function(a){b+=c(a)},subtract:function(a){b-=c(a)}}}function A(a,b){let c="x"===a.scroll?function(a){return`translate3d(${a}px,0px,0px)`}:function(a){return`translate3d(0px,${a}px,0px)`},d=b.style,e=null,f=!1;return{clear:function(){!f&&(d.transform="",b.getAttribute("style")||b.removeAttribute("style"))},to:function(b){if(f)return;let g=Math.round(100*a.direction(b))/100;g!==e&&(d.transform=c(g),e=g)},toggleActive:function(a){f=!a}}}let B={align:"center",axis:"x",container:null,slides:null,containScroll:"trimSnaps",direction:"ltr",slidesToScroll:1,inViewThreshold:0,breakpoints:{},dragFree:!1,dragThreshold:10,loop:!1,skipSnaps:!1,duration:25,startIndex:0,active:!0,watchDrag:!0,watchResize:!0,watchSlides:!0,watchFocus:!0};function C(a,b,c){let d,e,f,g,h,i=a.ownerDocument,j=i.defaultView,k=function(a){function b(a,b){return function a(b,c){return[b,c].reduce((b,c)=>(v(c).forEach(d=>{let e=b[d],f=c[d],g=o(e)&&o(f);b[d]=g?a(e,f):f}),b),{})}(a,b||{})}return{mergeOptions:b,optionsAtMedia:function(c){let d=c.breakpoints||{},e=v(d).filter(b=>a.matchMedia(b).matches).map(a=>d[a]).reduce((a,c)=>b(a,c),{});return b(c,e)},optionsMediaQueries:function(b){return b.map(a=>v(a.breakpoints||{})).reduce((a,b)=>a.concat(b),[]).map(a.matchMedia)}}}(j),D=(h=[],{init:function(a,b){return(h=b.filter(({options:a})=>!1!==k.optionsAtMedia(a).active)).forEach(b=>b.init(a,k)),b.reduce((a,b)=>Object.assign(a,{[b.name]:b}),{})},destroy:function(){h=h.filter(a=>a.destroy())}}),E=x(),F=function(){let a,b={},c={init:function(b){a=b},emit:function(d){return(b[d]||[]).forEach(b=>b(a,d)),c},off:function(a,d){return b[a]=(b[a]||[]).filter(a=>a!==d),c},on:function(a,d){return b[a]=(b[a]||[]).concat([d]),c},clear:function(){b={}}};return c}(),{mergeOptions:G,optionsAtMedia:H,optionsMediaQueries:I}=k,{on:J,off:K,emit:L}=F,M=!1,N=G(B,C.globalOptions),O=G(N),P=[];function Q(b,c){if(M)return;O=H(N=G(N,b)),P=c||P;let{container:h,slides:k}=O;f=(m(h)?a.querySelector(h):h)||a.children[0];let o=m(k)?f.querySelectorAll(k):k;g=[].slice.call(o||f.children),d=function b(c){let d=function(a,b,c,d,e,f,g){let h,i,{align:j,axis:k,direction:o,startIndex:B,loop:C,duration:D,dragFree:E,dragThreshold:F,inViewThreshold:G,slidesToScroll:H,skipSnaps:I,containScroll:J,watchResize:K,watchSlides:L,watchDrag:M,watchFocus:N}=f,O={measure:function(a){let{offsetTop:b,offsetLeft:c,offsetWidth:d,offsetHeight:e}=a;return{top:b,right:c+d,bottom:b+e,left:c,width:d,height:e}}},P=O.measure(b),Q=c.map(O.measure),R=function(a,b){let c="rtl"===b,d="y"===a,e=!d&&c?-1:1;return{scroll:d?"y":"x",cross:d?"x":"y",startEdge:d?"top":c?"right":"left",endEdge:d?"bottom":c?"left":"right",measureSize:function(a){let{height:b,width:c}=a;return d?b:c},direction:function(a){return a*e}}}(k,o),S=R.measureSize(P),T={measure:function(a){return a/100*S}},U=function(a,b){let c={start:function(){return 0},center:function(a){return(b-a)/2},end:function(a){return b-a}};return{measure:function(d,e){return m(a)?c[a](d):a(b,d,e)}}}(j,S),V=!C&&!!J,{slideSizes:W,slideSizesWithGaps:X,startGap:Y,endGap:Z}=function(a,b,c,d,e,f){let{measureSize:g,startEdge:h,endEdge:i}=a,j=c[0]&&e,k=function(){if(!j)return 0;let a=c[0];return p(b[h]-a[h])}(),l=j?parseFloat(f.getComputedStyle(s(d)).getPropertyValue(`margin-${i}`)):0,m=c.map(g),n=c.map((a,b,c)=>{let d=b===t(c);return b?d?m[b]+l:c[b+1][h]-a[h]:m[b]+k}).map(p);return{slideSizes:m,slideSizesWithGaps:n,startGap:k,endGap:l}}(R,P,Q,c,C||!!J,e),$=function(a,b,c,d,e,f,g,h,i){let{startEdge:j,endEdge:k,direction:m}=a,n=l(c);return{groupSlides:function(a){return n?r(a).filter(a=>a%c==0).map(b=>a.slice(b,b+c)):a.length?r(a).reduce((c,i,l)=>{let n=s(c)||0,o=i===t(a),q=e[j]-f[n][j],r=e[j]-f[i][k],u=d||0!==n?0:m(g),v=p(r-(!d&&o?m(h):0)-(q+u));return l&&v>b+2&&c.push(i),o&&c.push(a.length),c},[]).map((b,c,d)=>{let e=Math.max(d[c-1]||0);return a.slice(e,b)}):[]}}}(R,S,H,C,P,Q,Y,Z,0),{snaps:_,snapsAligned:aa}=function(a,b,c,d,e){let{startEdge:f,endEdge:g}=a,{groupSlides:h}=e,i=h(d).map(a=>s(a)[g]-a[0][f]).map(p).map(b.measure),j=d.map(a=>c[f]-a[f]).map(a=>-p(a)),k=h(j).map(a=>a[0]).map((a,b)=>a+i[b]);return{snaps:j,snapsAligned:k}}(R,U,P,Q,$),ab=-s(_)+s(X),{snapsContained:ac,scrollContainLimit:ad}=function(a,b,c,d,e){let f=y(-b+a,0),g=c.map((a,b)=>{let{min:d,max:e}=f,g=f.constrain(a),h=b===t(c);return b?h||function(a,b){return 1>=p(a-b)}(d,g)?d:function(a,b){return 1>=p(a-b)}(e,g)?e:g:e}).map(a=>parseFloat(a.toFixed(3))),h=function(){let a=g[0],b=s(g);return y(g.lastIndexOf(a),g.indexOf(b)+1)}();return{snapsContained:function(){if(b<=a+2)return[f.max];if("keepSnaps"===d)return g;let{min:c,max:e}=h;return g.slice(c,e)}(),scrollContainLimit:h}}(S,ab,aa,J,0),ae=V?ac:aa,{limit:af}=function(a,b,c){let d=b[0];return{limit:y(c?d-a:s(b),d)}}(ab,ae,C),ag=function a(b,c,d){let{constrain:e}=y(0,b),f=b+1,g=h(c);function h(a){return d?p((f+a)%f):e(a)}function i(){return a(b,g,d)}let j={get:function(){return g},set:function(a){return g=h(a),j},add:function(a){return i().set(g+a)},clone:i};return j}(t(ae),B,C),ah=ag.clone(),ai=r(c),aj=function(a,b,c,d){let e=x(),f=1e3/60,g=null,h=0,i=0;function j(a){if(!i)return;g||(g=a,c(),c());let e=a-g;for(g=a,h+=e;h>=f;)c(),h-=f;d(h/f),i&&(i=b.requestAnimationFrame(j))}function k(){b.cancelAnimationFrame(i),g=null,h=0,i=0}return{init:function(){e.add(a,"visibilitychange",()=>{a.hidden&&(g=null,h=0)})},destroy:function(){k(),e.clear()},start:function(){i||(i=b.requestAnimationFrame(j))},stop:k,update:c,render:d}}(d,e,()=>(({dragHandler:a,scrollBody:b,scrollBounds:c,options:{loop:d}})=>{d||c.constrain(a.pointerDown()),b.seek()})(ax),a=>(({scrollBody:a,translate:b,location:c,offsetLocation:d,previousLocation:e,scrollLooper:f,slideLooper:g,dragHandler:h,animation:i,eventHandler:j,scrollBounds:k,options:{loop:l}},m)=>{let n=a.settled(),o=!k.shouldConstrain(),p=l?n:n&&o,q=p&&!h.pointerDown();q&&i.stop();let r=c.get()*m+e.get()*(1-m);d.set(r),l&&(f.loop(a.direction()),g.loop()),b.to(d.get()),q&&j.emit("settle"),p||j.emit("scroll")})(ax,a)),ak=ae[ag.get()],al=z(ak),am=z(ak),an=z(ak),ao=z(ak),ap=function(a,b,c,d,e,f){let g=0,h=0,i=e,j=.68,k=a.get(),l=0;function m(a){return i=a,o}function n(a){return j=a,o}let o={direction:function(){return h},duration:function(){return i},velocity:function(){return g},seek:function(){let b=d.get()-a.get(),e=0;return i?(c.set(a),g+=b/i,g*=j,k+=g,a.add(g),e=k-l):(g=0,c.set(d),a.set(d),e=b),h=q(e),l=k,o},settled:function(){return .001>p(d.get()-b.get())},useBaseFriction:function(){return n(.68)},useBaseDuration:function(){return m(e)},useFriction:n,useDuration:m};return o}(al,an,am,ao,D,.68),aq=function(a,b,c,d,e){let{reachedAny:f,removeOffset:g,constrain:h}=d;function i(a){return a.concat().sort((a,b)=>p(a)-p(b))[0]}function j(b,d){let e=[b,b+c,b-c];if(!a)return b;if(!d)return i(e);let f=e.filter(a=>q(a)===d);return f.length?i(f):s(e)-c}return{byDistance:function(c,d){let i=e.get()+c,{index:k,distance:l}=function(c){let d=a?g(c):h(c),{index:e}=b.map((a,b)=>({diff:j(a-d,0),index:b})).sort((a,b)=>p(a.diff)-p(b.diff))[0];return{index:e,distance:d}}(i),m=!a&&f(i);if(!d||m)return{index:k,distance:c};let n=c+j(b[k]-l,0);return{index:k,distance:n}},byIndex:function(a,c){let d=j(b[a]-e.get(),c);return{index:a,distance:d}},shortcut:j}}(C,ae,ab,af,ao),ar=function(a,b,c,d,e,f,g){function h(e){let h=e.distance,i=e.index!==b.get();f.add(h),h&&(d.duration()?a.start():(a.update(),a.render(1),a.update())),i&&(c.set(b.get()),b.set(e.index),g.emit("select"))}return{distance:function(a,b){h(e.byDistance(a,b))},index:function(a,c){let d=b.clone().set(a);h(e.byIndex(d.get(),c))}}}(aj,ag,ah,ap,aq,ao,g),as=function(a){let{max:b,length:c}=a;return{get:function(a){return c?-((a-b)/c):0}}}(af),at=x(),au=function(a,b,c,d){let e,f={},g=null,h=null,i=!1;return{init:function(){e=new IntersectionObserver(a=>{i||(a.forEach(a=>{f[b.indexOf(a.target)]=a}),g=null,h=null,c.emit("slidesInView"))},{root:a.parentElement,threshold:d}),b.forEach(a=>e.observe(a))},destroy:function(){e&&e.disconnect(),i=!0},get:function(a=!0){if(a&&g)return g;if(!a&&h)return h;let b=v(f).reduce((b,c)=>{let d=parseInt(c),{isIntersecting:e}=f[d];return(a&&e||!a&&!e)&&b.push(d),b},[]);return a&&(g=b),a||(h=b),b}}}(b,c,g,G),{slideRegistry:av}=function(a,b,c,d,e,f){let{groupSlides:g}=e,{min:h,max:i}=d;return{slideRegistry:function(){let d=g(f);return 1===c.length?[f]:a&&"keepSnaps"!==b?d.slice(h,i).map((a,b,c)=>{let d=b===t(c);return b?d?u(t(f)-s(c)[0]+1,s(c)[0]):a:u(s(c[0])+1)}):d}()}}(V,J,ae,ad,$,ai),aw=function(a,b,c,d,e,f,g,h){let i={passive:!0,capture:!0},j=0;function k(a){"Tab"===a.code&&(j=new Date().getTime())}return{init:function(m){h&&(f.add(document,"keydown",k,!1),b.forEach((b,k)=>{f.add(b,"focus",b=>{(n(h)||h(m,b))&&function(b){if(new Date().getTime()-j>10)return;g.emit("slideFocusStart"),a.scrollLeft=0;let f=c.findIndex(a=>a.includes(b));l(f)&&(e.useDuration(0),d.index(f,0),g.emit("slideFocus"))}(k)},i)}))}}}(a,c,av,ar,ap,at,g,N),ax={ownerDocument:d,ownerWindow:e,eventHandler:g,containerRect:P,slideRects:Q,animation:aj,axis:R,dragHandler:function(a,b,c,d,e,f,g,h,i,j,k,l,m,o,r,s,t,u,v){let{cross:z,direction:A}=a,B=["INPUT","SELECT","TEXTAREA"],C={passive:!1},D=x(),E=x(),F=y(50,225).constrain(o.measure(20)),G={mouse:300,touch:400},H={mouse:500,touch:600},I=r?43:25,J=!1,K=0,L=0,M=!1,N=!1,O=!1,P=!1;function Q(a){if(!w(a,d)&&a.touches.length>=2)return R(a);let b=f.readPoint(a),c=f.readPoint(a,z),g=p(b-K),i=p(c-L);if(!N&&!P&&(!a.cancelable||!(N=g>i)))return R(a);let k=f.pointerMove(a);g>s&&(O=!0),j.useFriction(.3).useDuration(.75),h.start(),e.add(A(k)),a.preventDefault()}function R(a){let b=k.byDistance(0,!1).index!==l.get(),c=f.pointerUp(a)*(r?H:G)[P?"mouse":"touch"],d=function(a,b){let c=l.add(-1*q(a)),d=k.byDistance(a,!r).distance;return r||p(a)<F?d:t&&b?.5*d:k.byIndex(c.get(),0).distance}(A(c),b),e=function(a,b){var c,d;if(0===a||0===b||p(a)<=p(b))return 0;let e=(c=p(a),d=p(b),p(c-d));return p(e/a)}(c,d);N=!1,M=!1,E.clear(),j.useDuration(I-10*e).useFriction(.68+e/50),i.distance(d,!r),P=!1,m.emit("pointerUp")}function S(a){O&&(a.stopPropagation(),a.preventDefault(),O=!1)}return{init:function(a){v&&D.add(b,"dragstart",a=>a.preventDefault(),C).add(b,"touchmove",()=>void 0,C).add(b,"touchend",()=>void 0).add(b,"touchstart",h).add(b,"mousedown",h).add(b,"touchcancel",R).add(b,"contextmenu",R).add(b,"click",S,!0);function h(h){(n(v)||v(a,h))&&function(a){let h=w(a,d);if((P=h,O=r&&h&&!a.buttons&&J,J=p(e.get()-g.get())>=2,!h||0===a.button)&&!function(a){let b=a.nodeName||"";return B.includes(b)}(a.target)){M=!0,f.pointerDown(a),j.useFriction(0).useDuration(0),e.set(g);let d=P?c:b;E.add(d,"touchmove",Q,C).add(d,"touchend",R).add(d,"mousemove",Q,C).add(d,"mouseup",R),K=f.readPoint(a),L=f.readPoint(a,z),m.emit("pointerDown")}}(h)}},destroy:function(){D.clear(),E.clear()},pointerDown:function(){return M}}}(R,a,d,e,ao,function(a,b){let c,d;function e(a){return a.timeStamp}function f(c,d){let e=d||a.scroll,f=`client${"x"===e?"X":"Y"}`;return(w(c,b)?c:c.touches[0])[f]}return{pointerDown:function(a){return c=a,d=a,f(a)},pointerMove:function(a){let b=f(a)-f(d),g=e(a)-e(c)>170;return d=a,g&&(c=a),b},pointerUp:function(a){if(!c||!d)return 0;let b=f(d)-f(c),g=e(a)-e(c),h=e(a)-e(d)>170,i=b/g;return g&&!h&&p(i)>.1?i:0},readPoint:f}}(R,e),al,aj,ar,ap,aq,ag,g,T,E,F,I,0,M),eventStore:at,percentOfView:T,index:ag,indexPrevious:ah,limit:af,location:al,offsetLocation:an,previousLocation:am,options:f,resizeHandler:function(a,b,c,d,e,f,g){let h,i,j=[a].concat(d),k=[],l=!1;function m(a){return e.measureSize(g.measure(a))}return{init:function(e){f&&(i=m(a),k=d.map(m),h=new ResizeObserver(c=>{(n(f)||f(e,c))&&function(c){for(let f of c){if(l)return;let c=f.target===a,g=d.indexOf(f.target),h=c?i:k[g];if(p(m(c?a:d[g])-h)>=.5){e.reInit(),b.emit("resize");break}}}(c)}),c.requestAnimationFrame(()=>{j.forEach(a=>h.observe(a))}))},destroy:function(){l=!0,h&&h.disconnect()}}}(b,g,e,c,R,K,O),scrollBody:ap,scrollBounds:function(a,b,c,d,e){let f=e.measure(10),g=e.measure(50),h=y(.1,.99),i=!1;function j(){return!i&&!!a.reachedAny(c.get())&&!!a.reachedAny(b.get())}return{shouldConstrain:j,constrain:function(e){if(!j())return;let i=a.reachedMin(b.get())?"min":"max",k=p(a[i]-b.get()),l=c.get()-b.get(),m=h.constrain(k/g);c.subtract(l*m),!e&&p(l)<f&&(c.set(a.constrain(c.get())),d.useDuration(25).useBaseFriction())},toggleActive:function(a){i=!a}}}(af,an,ao,ap,T),scrollLooper:function(a,b,c,d){let{reachedMin:e,reachedMax:f}=y(b.min+.1,b.max+.1);return{loop:function(b){if(!(1===b?f(c.get()):-1===b&&e(c.get())))return;let g=-1*b*a;d.forEach(a=>a.add(g))}}}(ab,af,an,[al,an,am,ao]),scrollProgress:as,scrollSnapList:ae.map(as.get),scrollSnaps:ae,scrollTarget:aq,scrollTo:ar,slideLooper:function(a,b,c,d,e,f,g,h,i){let j=r(e),k=r(e).reverse(),l=o(n(k,g[0]),c,!1).concat(o(n(j,b-g[0]-1),-c,!0));function m(a,b){return a.reduce((a,b)=>a-e[b],b)}function n(a,b){return a.reduce((a,c)=>m(a,b)>0?a.concat([c]):a,[])}function o(e,g,j){let k=f.map((a,c)=>({start:a-d[c]+.5+g,end:a+b-.5+g}));return e.map(b=>{let d=j?0:-c,e=j?c:0,f=k[b][j?"end":"start"];return{index:b,loopPoint:f,slideLocation:z(-1),translate:A(a,i[b]),target:()=>h.get()>f?d:e}})}return{canLoop:function(){return l.every(({index:a})=>.1>=m(j.filter(b=>b!==a),b))},clear:function(){l.forEach(a=>a.translate.clear())},loop:function(){l.forEach(a=>{let{target:b,translate:c,slideLocation:d}=a,e=b();e!==d.get()&&(c.to(e),d.set(e))})},loopPoints:l}}(R,S,ab,W,X,_,ae,an,c),slideFocus:aw,slidesHandler:(i=!1,{init:function(a){L&&(h=new MutationObserver(b=>{!i&&(n(L)||L(a,b))&&function(b){for(let c of b)if("childList"===c.type){a.reInit(),g.emit("slidesChanged");break}}(b)})).observe(b,{childList:!0})},destroy:function(){h&&h.disconnect(),i=!0}}),slidesInView:au,slideIndexes:ai,slideRegistry:av,slidesToScroll:$,target:ao,translate:A(R,b)};return ax}(a,f,g,i,j,c,F);return c.loop&&!d.slideLooper.canLoop()?b(Object.assign({},c,{loop:!1})):d}(O),I([N,...P.map(({options:a})=>a)]).forEach(a=>E.add(a,"change",R)),O.active&&(d.translate.to(d.location.get()),d.animation.init(),d.slidesInView.init(),d.slideFocus.init(V),d.eventHandler.init(V),d.resizeHandler.init(V),d.slidesHandler.init(V),d.options.loop&&d.slideLooper.loop(),f.offsetParent&&g.length&&d.dragHandler.init(V),e=D.init(V,P))}function R(a,b){let c=U();S(),Q(G({startIndex:c},a),b),F.emit("reInit")}function S(){d.dragHandler.destroy(),d.eventStore.clear(),d.translate.clear(),d.slideLooper.clear(),d.resizeHandler.destroy(),d.slidesHandler.destroy(),d.slidesInView.destroy(),d.animation.destroy(),D.destroy(),E.clear()}function T(a,b,c){O.active&&!M&&(d.scrollBody.useBaseFriction().useDuration(!0===b?0:O.duration),d.scrollTo.index(a,c||0))}function U(){return d.index.get()}let V={canScrollNext:function(){return d.index.add(1).get()!==U()},canScrollPrev:function(){return d.index.add(-1).get()!==U()},containerNode:function(){return f},internalEngine:function(){return d},destroy:function(){M||(M=!0,E.clear(),S(),F.emit("destroy"),F.clear())},off:K,on:J,emit:L,plugins:function(){return e},previousScrollSnap:function(){return d.indexPrevious.get()},reInit:R,rootNode:function(){return a},scrollNext:function(a){T(d.index.add(1).get(),a,-1)},scrollPrev:function(a){T(d.index.add(-1).get(),a,1)},scrollProgress:function(){return d.scrollProgress.get(d.offsetLocation.get())},scrollSnapList:function(){return d.scrollSnapList},scrollTo:T,selectedScrollSnap:U,slideNodes:function(){return g},slidesInView:function(){return d.slidesInView.get()},slidesNotInView:function(){return d.slidesInView.get(!1)}};return Q(b,c),setTimeout(()=>F.emit("init"),0),V}function D(a={},b=[]){let c=(0,h.useRef)(a),d=(0,h.useRef)(b),[e,f]=(0,h.useState)(),[g,i]=(0,h.useState)(),l=(0,h.useCallback)(()=>{e&&e.reInit(c.current,d.current)},[e]);return(0,h.useEffect)(()=>{j(c.current,a)||(c.current=a,l())},[a,l]),(0,h.useEffect)(()=>{!function(a,b){if(a.length!==b.length)return!1;let c=k(a),d=k(b);return c.every((a,b)=>j(a,d[b]))}(d.current,b)&&(d.current=b,l())},[b,l]),(0,h.useEffect)(()=>{if("undefined"!=typeof window&&window.document&&window.document.createElement&&g){C.globalOptions=D.globalOptions;let a=C(g,c.current,d.current);return f(a),()=>a.destroy()}f(void 0)},[g,f]),[i,e]}C.globalOptions=void 0,D.globalOptions=void 0;var E=c(2688);let F=(0,E.A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]),G=(0,E.A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]]);var H=c(1743);let I=h.createContext(null);function J(){let a=h.useContext(I);if(!a)throw Error("useCarousel must be used within a <Carousel />");return a}function K({orientation:a="horizontal",opts:b,setApi:c,plugins:e,className:f,children:g,...i}){let[j,k]=D({...b,axis:"horizontal"===a?"x":"y"},e),[l,m]=h.useState(!1),[n,o]=h.useState(!1),p=h.useCallback(a=>{a&&(m(a.canScrollPrev()),o(a.canScrollNext()))},[]),q=h.useCallback(()=>{k?.scrollPrev()},[k]),r=h.useCallback(()=>{k?.scrollNext()},[k]),s=h.useCallback(a=>{"ArrowLeft"===a.key?(a.preventDefault(),q()):"ArrowRight"===a.key&&(a.preventDefault(),r())},[q,r]);return h.useEffect(()=>{k&&c&&c(k)},[k,c]),h.useEffect(()=>{if(k)return p(k),k.on("reInit",p),k.on("select",p),()=>{k?.off("select",p)}},[k,p]),(0,d.jsx)(I.Provider,{value:{carouselRef:j,api:k,opts:b,orientation:a||(b?.axis==="y"?"vertical":"horizontal"),scrollPrev:q,scrollNext:r,canScrollPrev:l,canScrollNext:n},children:(0,d.jsx)("div",{onKeyDownCapture:s,className:(0,H.cn)("relative",f),role:"region","aria-roledescription":"carousel","data-slot":"carousel",...i,children:g})})}function L({className:a,...b}){let{carouselRef:c,orientation:e}=J();return(0,d.jsx)("div",{ref:c,className:"overflow-hidden","data-slot":"carousel-content",children:(0,d.jsx)("div",{className:(0,H.cn)("flex","horizontal"===e?"-ml-4":"-mt-4 flex-col",a),...b})})}function M({className:a,...b}){let{orientation:c}=J();return(0,d.jsx)("div",{role:"group","aria-roledescription":"slide","data-slot":"carousel-item",className:(0,H.cn)("min-w-0 shrink-0 grow-0 basis-full","horizontal"===c?"pl-4":"pt-4",a),...b})}function N({className:a,variant:b="outline",size:c="icon",...e}){let{orientation:f,scrollPrev:h,canScrollPrev:i}=J();return(0,d.jsxs)(g.$,{"data-slot":"carousel-previous",variant:b,size:c,className:(0,H.cn)("absolute size-8 rounded-full","horizontal"===f?"top-1/2 -left-12 -translate-y-1/2":"-top-12 left-1/2 -translate-x-1/2 rotate-90",a),disabled:!i,onClick:h,...e,children:[(0,d.jsx)(F,{}),(0,d.jsx)("span",{className:"sr-only",children:"Previous slide"})]})}function O({className:a,variant:b="outline",size:c="icon",...e}){let{orientation:f,scrollNext:h,canScrollNext:i}=J();return(0,d.jsxs)(g.$,{"data-slot":"carousel-next",variant:b,size:c,className:(0,H.cn)("absolute size-8 rounded-full","horizontal"===f?"top-1/2 -right-12 -translate-y-1/2":"-bottom-12 left-1/2 -translate-x-1/2 rotate-90",a),disabled:!i,onClick:h,...e,children:[(0,d.jsx)(G,{}),(0,d.jsx)("span",{className:"sr-only",children:"Next slide"})]})}let P=(0,E.A)("quote",[["path",{d:"M16 3a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2 1 1 0 0 1 1 1v1a2 2 0 0 1-2 2 1 1 0 0 0-1 1v2a1 1 0 0 0 1 1 6 6 0 0 0 6-6V5a2 2 0 0 0-2-2z",key:"rib7q0"}],["path",{d:"M5 3a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2 1 1 0 0 1 1 1v1a2 2 0 0 1-2 2 1 1 0 0 0-1 1v2a1 1 0 0 0 1 1 6 6 0 0 0 6-6V5a2 2 0 0 0-2-2z",key:"1ymkrd"}]]);var Q=c(4398);let R=[{id:"t1",name:"Priya Sharma",role:"Local Student",content:"Himalayan Brew has become my second home! The coffee is amazing and the atmosphere is perfect for studying. The staff always remembers my order - iced chai latte with extra foam. It's the best caf\xe9 in Biratnagar!",rating:5,image:"/images/testimonial-priya.jpg",date:"2024-01-15"},{id:"t2",name:"Rajesh Thapa",role:"Business Owner",content:"I've been coming here for business meetings for over a year. The ambiance is professional yet cozy, and their Himalayan coffee is exceptional. My clients are always impressed with this place.",rating:5,image:"/images/testimonial-rajesh.jpg",date:"2024-01-10"},{id:"t3",name:"Sarah Johnson",role:"Tourist from Australia",content:"Found this gem while exploring Biratnagar! The butter tea was an authentic experience and the momos were the best I had in Nepal. The staff was so welcoming and helped me understand local culture.",rating:5,image:"/images/testimonial-sarah.jpg",date:"2024-01-08"},{id:"t4",name:"Amit Rai",role:"Software Engineer",content:"Perfect spot for remote work! Fast WiFi, comfortable seating, and endless coffee refills. The banana bread is addictive. I practically live here during my work-from-caf\xe9 days.",rating:5,image:"/images/testimonial-amit.jpg",date:"2024-01-05"},{id:"t5",name:"Sunita Devi",role:"Teacher",content:"Love bringing my family here on weekends. The kids enjoy the hot chocolate while we adults savor the masala chai. It's become our family tradition. Great place for all ages!",rating:5,image:"/images/testimonial-sunita.jpg",date:"2024-01-03"},{id:"t6",name:"David Chen",role:"Travel Blogger",content:"Himalayan Brew captures the essence of Nepali hospitality perfectly. The coffee quality rivals any international chain, but with authentic local charm. A must-visit in Biratnagar!",rating:5,image:"/images/testimonial-david.jpg",date:"2023-12-28"},{id:"t7",name:"Kamala Gurung",role:"Local Artist",content:"This caf\xe9 has soul! I love how they support local artists by displaying our work. The creative atmosphere and excellent coffee make it my favorite spot for inspiration.",rating:5,image:"/images/testimonial-kamala.jpg",date:"2023-12-25"},{id:"t8",name:"Michael Roberts",role:"NGO Worker",content:"Been working in Nepal for 3 years and this is hands down the best caf\xe9 I've found. The dal bhat set is authentic and delicious. Feels like eating at a Nepali friend's home.",rating:5,image:"/images/testimonial-michael.jpg",date:"2023-12-20"}],S=R.slice(0,3);R.reduce((a,b)=>a+b.rating,0),R.length;var T=c(5814),U=c.n(T);function V(){return(0,d.jsx)("section",{className:"py-20 bg-cafe-beige",children:(0,d.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,d.jsxs)(e.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.6},viewport:{once:!0},className:"text-center mb-16",children:[(0,d.jsx)("h2",{className:"font-serif text-4xl md:text-5xl font-bold text-cafe-dark-brown mb-6",children:"What Our Guests Say"}),(0,d.jsx)("p",{className:"text-xl text-cafe-brown max-w-3xl mx-auto leading-relaxed",children:"Don't just take our word for it. Here's what our wonderful customers have to say about their experience at Himalayan Brew Caf\xe9."})]}),(0,d.jsx)(e.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.2},viewport:{once:!0},className:"mb-12",children:(0,d.jsxs)(K,{opts:{align:"start",loop:!0},className:"w-full",children:[(0,d.jsx)(L,{className:"-ml-2 md:-ml-4",children:S.map((a,b)=>(0,d.jsx)(M,{className:"pl-2 md:pl-4 md:basis-1/2 lg:basis-1/3",children:(0,d.jsx)(f.Zp,{className:"h-full border-cafe-sand hover:shadow-cafe-lg transition-shadow duration-300",children:(0,d.jsxs)(f.Wu,{className:"p-6 space-y-4",children:[(0,d.jsx)("div",{className:"flex justify-center",children:(0,d.jsx)("div",{className:"w-12 h-12 bg-cafe-gold rounded-full flex items-center justify-center",children:(0,d.jsx)(P,{className:"h-6 w-6 text-cafe-dark-brown"})})}),(0,d.jsx)("div",{className:"flex justify-center gap-1",children:[...Array(a.rating)].map((a,b)=>(0,d.jsx)(Q.A,{className:"h-5 w-5 fill-cafe-gold text-cafe-gold"},b))}),(0,d.jsxs)("blockquote",{className:"text-cafe-brown leading-relaxed text-center italic",children:['"',a.content,'"']}),(0,d.jsxs)("div",{className:"text-center pt-4 border-t border-cafe-sand",children:[(0,d.jsx)("div",{className:"w-16 h-16 mx-auto mb-3 rounded-full overflow-hidden",children:(0,d.jsx)("img",{src:a.image,alt:a.name,className:"w-full h-full object-cover"})}),(0,d.jsx)("h4",{className:"font-serif font-semibold text-cafe-dark-brown",children:a.name}),(0,d.jsx)("p",{className:"text-sm text-cafe-brown",children:a.role}),(0,d.jsx)("p",{className:"text-xs text-cafe-brown mt-1",children:new Date(a.date).toLocaleDateString()})]})]})})},a.id))}),(0,d.jsx)(N,{className:"border-cafe-brown text-cafe-brown hover:bg-cafe-brown hover:text-cafe-cream"}),(0,d.jsx)(O,{className:"border-cafe-brown text-cafe-brown hover:bg-cafe-brown hover:text-cafe-cream"})]})}),(0,d.jsxs)(e.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.4},viewport:{once:!0},className:"grid grid-cols-2 md:grid-cols-4 gap-8 mb-12",children:[(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"text-3xl md:text-4xl font-bold text-cafe-dark-brown mb-2",children:"500+"}),(0,d.jsx)("div",{className:"text-cafe-brown font-medium",children:"Happy Customers"})]}),(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"text-3xl md:text-4xl font-bold text-cafe-dark-brown mb-2",children:"4.9"}),(0,d.jsx)("div",{className:"text-cafe-brown font-medium",children:"Average Rating"})]}),(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"text-3xl md:text-4xl font-bold text-cafe-dark-brown mb-2",children:"3+"}),(0,d.jsx)("div",{className:"text-cafe-brown font-medium",children:"Years Serving"})]}),(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"text-3xl md:text-4xl font-bold text-cafe-dark-brown mb-2",children:"50+"}),(0,d.jsx)("div",{className:"text-cafe-brown font-medium",children:"Menu Items"})]})]}),(0,d.jsx)(e.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.6},viewport:{once:!0},className:"text-center",children:(0,d.jsx)(U(),{href:"/testimonials",children:(0,d.jsx)(g.$,{size:"lg",className:"bg-cafe-brown hover:bg-cafe-dark-brown text-cafe-cream px-8 py-3 text-lg font-semibold shadow-cafe-lg",children:"Read All Reviews"})})})]})})}},9649:(a,b)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"isNavigatingToNewRootLayout",{enumerable:!0,get:function(){return function a(b,c){let d=b[0],e=c[0];if(Array.isArray(d)&&Array.isArray(e)){if(d[0]!==e[0]||d[2]!==e[2])return!0}else if(d!==e)return!0;if(b[4])return!c[4];if(c[4])return!0;let f=Object.values(b[1])[0],g=Object.values(c[1])[0];return!f||!g||a(f,g)}}}),("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},9651:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"serverPatchReducer",{enumerable:!0,get:function(){return k}});let d=c(7391),e=c(6770),f=c(9649),g=c(5232),h=c(6928),i=c(9435),j=c(9752);function k(a,b){let{serverResponse:{flightData:c,canonicalUrl:k},navigatedAt:l}=b,m={};if(m.preserveCustomHistoryState=!1,"string"==typeof c)return(0,g.handleExternalUrl)(a,m,c,a.pushRef.pendingPush);let n=a.tree,o=a.cache;for(let b of c){let{segmentPath:c,tree:i}=b,p=(0,e.applyRouterStatePatchToTree)(["",...c],n,i,a.canonicalUrl);if(null===p)return a;if((0,f.isNavigatingToNewRootLayout)(n,p))return(0,g.handleExternalUrl)(a,m,a.canonicalUrl,a.pushRef.pendingPush);let q=k?(0,d.createHrefFromUrl)(k):void 0;q&&(m.canonicalUrl=q);let r=(0,j.createEmptyCacheNode)();(0,h.applyFlightData)(l,o,r,b),m.patchedTree=p,m.cache=r,o=r,n=p}return(0,i.handleMutable)(a,m)}("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},9656:(a,b,c)=>{"use strict";c.r(b),c.d(b,{_:()=>e});var d=0;function e(a){return"__private_"+d+++"_"+a}},9707:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{addSearchParamsToPageSegments:function(){return l},handleAliasedPrefetchEntry:function(){return k}});let d=c(3913),e=c(9752),f=c(6770),g=c(7391),h=c(3123),i=c(3898),j=c(9435);function k(a,b,c,k,m){let n,o=b.tree,p=b.cache,q=(0,g.createHrefFromUrl)(k);if("string"==typeof c)return!1;for(let b of c){if(!function a(b){if(!b)return!1;let c=b[2];if(b[3])return!0;for(let b in c)if(a(c[b]))return!0;return!1}(b.seedData))continue;let c=b.tree;c=l(c,Object.fromEntries(k.searchParams));let{seedData:g,isRootRender:j,pathToSegment:m}=b,r=["",...m];c=l(c,Object.fromEntries(k.searchParams));let s=(0,f.applyRouterStatePatchToTree)(r,o,c,q),t=(0,e.createEmptyCacheNode)();if(j&&g){let b=g[1];t.loading=g[3],t.rsc=b,function a(b,c,e,f,g){if(0!==Object.keys(f[1]).length)for(let i in f[1]){let j,k=f[1][i],l=k[0],m=(0,h.createRouterCacheKey)(l),n=null!==g&&void 0!==g[2][i]?g[2][i]:null;if(null!==n){let a=n[1],c=n[3];j={lazyData:null,rsc:l.includes(d.PAGE_SEGMENT_KEY)?null:a,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:c,navigatedAt:b}}else j={lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:-1};let o=c.parallelRoutes.get(i);o?o.set(m,j):c.parallelRoutes.set(i,new Map([[m,j]])),a(b,j,e,k,n)}}(a,t,p,c,g)}else t.rsc=p.rsc,t.prefetchRsc=p.prefetchRsc,t.loading=p.loading,t.parallelRoutes=new Map(p.parallelRoutes),(0,i.fillCacheWithNewSubTreeDataButOnlyLoading)(a,t,p,b);s&&(o=s,p=t,n=!0)}return!!n&&(m.patchedTree=o,m.cache=p,m.canonicalUrl=q,m.hashFragment=k.hash,(0,j.handleMutable)(b,m))}function l(a,b){let[c,e,...f]=a;if(c.includes(d.PAGE_SEGMENT_KEY))return[(0,d.addSearchParamsIfPageSegment)(c,b),e,...f];let g={};for(let[a,c]of Object.entries(e))g[a]=l(c,b);return[c,g,...f]}("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},9752:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{createEmptyCacheNode:function(){return G},createPrefetchURL:function(){return E},default:function(){return K},isExternalURL:function(){return D}});let d=c(4985),e=c(740),f=c(687),g=e._(c(3210)),h=c(2142),i=c(9154),j=c(7391),k=c(449),l=c(9129),m=c(5656),n=d._(c(5227)),o=c(5416),p=c(6127),q=c(7022),r=c(7086),s=c(4397),t=c(9330),u=c(5942),v=c(6736),w=c(642),x=c(2776),y=c(3690),z=c(6875),A=c(7860);c(3406);let B=d._(c(9018)),C={};function D(a){return a.origin!==window.location.origin}function E(a){let b;if((0,o.isBot)(window.navigator.userAgent))return null;try{b=new URL((0,p.addBasePath)(a),window.location.href)}catch(b){throw Object.defineProperty(Error("Cannot prefetch '"+a+"' because it cannot be converted to a URL."),"__NEXT_ERROR_CODE",{value:"E234",enumerable:!1,configurable:!0})}return D(b)?null:b}function F(a){let{appRouterState:b}=a;return(0,g.useInsertionEffect)(()=>{let{tree:a,pushRef:c,canonicalUrl:d}=b,e={...c.preserveCustomHistoryState?window.history.state:{},__NA:!0,__PRIVATE_NEXTJS_INTERNALS_TREE:a};c.pendingPush&&(0,j.createHrefFromUrl)(new URL(window.location.href))!==d?(c.pendingPush=!1,window.history.pushState(e,"",d)):window.history.replaceState(e,"",d)},[b]),(0,g.useEffect)(()=>{},[b.nextUrl,b.tree]),null}function G(){return{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:-1}}function H(a){null==a&&(a={});let b=window.history.state,c=null==b?void 0:b.__NA;c&&(a.__NA=c);let d=null==b?void 0:b.__PRIVATE_NEXTJS_INTERNALS_TREE;return d&&(a.__PRIVATE_NEXTJS_INTERNALS_TREE=d),a}function I(a){let{headCacheNode:b}=a,c=null!==b?b.head:null,d=null!==b?b.prefetchHead:null,e=null!==d?d:c;return(0,g.useDeferredValue)(c,e)}function J(a){let b,{actionQueue:c,assetPrefix:d,globalError:e,gracefullyDegrade:j}=a,n=(0,l.useActionQueue)(c),{canonicalUrl:o}=n,{searchParams:p,pathname:x}=(0,g.useMemo)(()=>{let a=new URL(o,"http://n");return{searchParams:a.searchParams,pathname:(0,v.hasBasePath)(a.pathname)?(0,u.removeBasePath)(a.pathname):a.pathname}},[o]);(0,g.useEffect)(()=>{function a(a){var b;a.persisted&&(null==(b=window.history.state)?void 0:b.__PRIVATE_NEXTJS_INTERNALS_TREE)&&(C.pendingMpaPath=void 0,(0,l.dispatchAppRouterAction)({type:i.ACTION_RESTORE,url:new URL(window.location.href),tree:window.history.state.__PRIVATE_NEXTJS_INTERNALS_TREE}))}return window.addEventListener("pageshow",a),()=>{window.removeEventListener("pageshow",a)}},[]),(0,g.useEffect)(()=>{function a(a){let b="reason"in a?a.reason:a.error;if((0,A.isRedirectError)(b)){a.preventDefault();let c=(0,z.getURLFromRedirectError)(b);(0,z.getRedirectTypeFromError)(b)===A.RedirectType.push?y.publicAppRouterInstance.push(c,{}):y.publicAppRouterInstance.replace(c,{})}}return window.addEventListener("error",a),window.addEventListener("unhandledrejection",a),()=>{window.removeEventListener("error",a),window.removeEventListener("unhandledrejection",a)}},[]);let{pushRef:D}=n;if(D.mpaNavigation){if(C.pendingMpaPath!==o){let a=window.location;D.pendingPush?a.assign(o):a.replace(o),C.pendingMpaPath=o}throw t.unresolvedThenable}(0,g.useEffect)(()=>{let a=window.history.pushState.bind(window.history),b=window.history.replaceState.bind(window.history),c=a=>{var b;let c=window.location.href,d=null==(b=window.history.state)?void 0:b.__PRIVATE_NEXTJS_INTERNALS_TREE;(0,g.startTransition)(()=>{(0,l.dispatchAppRouterAction)({type:i.ACTION_RESTORE,url:new URL(null!=a?a:c,c),tree:d})})};window.history.pushState=function(b,d,e){return(null==b?void 0:b.__NA)||(null==b?void 0:b._N)||(b=H(b),e&&c(e)),a(b,d,e)},window.history.replaceState=function(a,d,e){return(null==a?void 0:a.__NA)||(null==a?void 0:a._N)||(a=H(a),e&&c(e)),b(a,d,e)};let d=a=>{if(a.state){if(!a.state.__NA)return void window.location.reload();(0,g.startTransition)(()=>{(0,y.dispatchTraverseAction)(window.location.href,a.state.__PRIVATE_NEXTJS_INTERNALS_TREE)})}};return window.addEventListener("popstate",d),()=>{window.history.pushState=a,window.history.replaceState=b,window.removeEventListener("popstate",d)}},[]);let{cache:E,tree:G,nextUrl:J,focusAndScrollRef:K}=n,L=(0,g.useMemo)(()=>(0,s.findHeadInCache)(E,G[1]),[E,G]),M=(0,g.useMemo)(()=>(0,w.getSelectedParams)(G),[G]),O=(0,g.useMemo)(()=>({parentTree:G,parentCacheNode:E,parentSegmentPath:null,url:o}),[G,E,o]),P=(0,g.useMemo)(()=>({tree:G,focusAndScrollRef:K,nextUrl:J}),[G,K,J]);if(null!==L){let[a,c]=L;b=(0,f.jsx)(I,{headCacheNode:a},c)}else b=null;let Q=(0,f.jsxs)(r.RedirectBoundary,{children:[b,E.rsc,(0,f.jsx)(q.AppRouterAnnouncer,{tree:G})]});return Q=j?(0,f.jsx)(B.default,{children:Q}):(0,f.jsx)(m.ErrorBoundary,{errorComponent:e[0],errorStyles:e[1],children:Q}),(0,f.jsxs)(f.Fragment,{children:[(0,f.jsx)(F,{appRouterState:n}),(0,f.jsx)(N,{}),(0,f.jsx)(k.PathParamsContext.Provider,{value:M,children:(0,f.jsx)(k.PathnameContext.Provider,{value:x,children:(0,f.jsx)(k.SearchParamsContext.Provider,{value:p,children:(0,f.jsx)(h.GlobalLayoutRouterContext.Provider,{value:P,children:(0,f.jsx)(h.AppRouterContext.Provider,{value:y.publicAppRouterInstance,children:(0,f.jsx)(h.LayoutRouterContext.Provider,{value:O,children:Q})})})})})})]})}function K(a){let{actionQueue:b,globalErrorState:c,assetPrefix:d,gracefullyDegrade:e}=a;(0,x.useNavFailureHandler)();let g=(0,f.jsx)(J,{actionQueue:b,assetPrefix:d,globalError:c,gracefullyDegrade:e});return e?g:(0,f.jsx)(m.ErrorBoundary,{errorComponent:n.default,children:g})}let L=new Set,M=new Set;function N(){let[,a]=g.default.useState(0),b=L.size;return(0,g.useEffect)(()=>{let c=()=>a(a=>a+1);return M.add(c),b!==L.size&&c(),()=>{M.delete(c)}},[b,a]),[...L].map((a,b)=>(0,f.jsx)("link",{rel:"stylesheet",href:""+a,precedence:"next"},b))}globalThis._N_E_STYLE_LOAD=function(a){let b=L.size;return L.add(a),L.size!==b&&M.forEach(a=>a()),Promise.resolve()},("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)}};var b=require("../webpack-runtime.js");b.C(a);var c=b.X(0,[985,453],()=>b(b.s=2789));module.exports=c})();