"use client";

import { motion } from "framer-motion";
import { Card, CardContent } from "@/components/ui/card";
import { Heart, Users, Leaf, Award } from "lucide-react";
import { cafeInfo } from "@/data/cafe-info";

const valueIcons = {
  "Community First": Users,
  "Quality & Authenticity": Award,
  "Sustainability": Leaf,
  "Warm Hospitality": Heart,
};

export function AboutSection() {
  return (
    <section className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="font-serif text-4xl md:text-5xl font-bold text-cafe-dark-brown mb-6">
            About Our Story
          </h2>
          <p className="text-xl text-cafe-brown max-w-3xl mx-auto leading-relaxed">
            Discover the heart and soul behind Himalayan Brew Café, where every cup tells a story of passion, community, and authentic Nepali hospitality.
          </p>
        </motion.div>

        {/* Story Content */}
        <div className="grid lg:grid-cols-2 gap-12 items-center mb-20">
          {/* Text Content */}
          <motion.div
            initial={{ opacity: 0, x: -30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="space-y-6"
          >
            <h3 className="font-serif text-3xl font-semibold text-cafe-dark-brown">
              Our Journey
            </h3>
            <div className="prose prose-lg text-cafe-brown">
              <p className="leading-relaxed">
                Nestled in the vibrant city of Biratnagar, Himalayan Brew Café was born from a passion for bringing people together over exceptional coffee and warm conversations.
              </p>
              <p className="leading-relaxed">
                Our journey began in 2020 when our founder, inspired by the rich coffee culture of Nepal&apos;s hills and the bustling energy of Biratnagar, decided to create a space that celebrates both tradition and modernity.
              </p>
              <p className="leading-relaxed">
                We source our coffee beans directly from the hills of Ilam and Gulmi, supporting local farmers and ensuring every cup tells a story of Nepal&apos;s coffee heritage.
              </p>
            </div>
          </motion.div>

          {/* Image */}
          <motion.div
            initial={{ opacity: 0, x: 30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="relative"
          >
            <div className="aspect-square rounded-2xl overflow-hidden shadow-cafe-lg">
              <img
                src="https://images.unsplash.com/photo-1559925393-8be0ec4767c8?w=600&h=600&fit=crop"
                alt="Himalayan Brew Café story"
                className="w-full h-full object-cover"
              />
            </div>
            <div className="absolute -bottom-6 -right-6 w-24 h-24 bg-cafe-gold rounded-full flex items-center justify-center shadow-cafe-lg">
              <Heart className="h-12 w-12 text-cafe-dark-brown" />
            </div>
          </motion.div>
        </div>

        {/* Values Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mb-12"
        >
          <h3 className="font-serif text-3xl font-semibold text-cafe-dark-brown mb-4">
            Our Values
          </h3>
          <p className="text-lg text-cafe-brown max-w-2xl mx-auto">
            These core values guide everything we do, from sourcing our beans to serving our community.
          </p>
        </motion.div>

        {/* Values Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
          {cafeInfo.values.map((value, index) => {
            const IconComponent = valueIcons[value.title as keyof typeof valueIcons];
            
            return (
              <motion.div
                key={value.title}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <Card className="h-full border-cafe-beige hover:shadow-cafe-lg transition-shadow duration-300">
                  <CardContent className="p-6 text-center space-y-4">
                    <div className="w-16 h-16 bg-cafe-beige rounded-full flex items-center justify-center mx-auto">
                      <IconComponent className="h-8 w-8 text-cafe-brown" />
                    </div>
                    <h4 className="font-serif text-xl font-semibold text-cafe-dark-brown">
                      {value.title}
                    </h4>
                    <p className="text-cafe-brown leading-relaxed">
                      {value.description}
                    </p>
                  </CardContent>
                </Card>
              </motion.div>
            );
          })}
        </div>
      </div>
    </section>
  );
}
