"use client";

import { motion } from "framer-motion";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { 
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";
import { Star, Quote } from "lucide-react";
import { featuredTestimonials } from "@/data/testimonials";
import Link from "next/link";

export function TestimonialsSection() {
  return (
    <section className="py-20 bg-cafe-beige">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="font-serif text-4xl md:text-5xl font-bold text-cafe-dark-brown mb-6">
            What Our Guests Say
          </h2>
          <p className="text-xl text-cafe-brown max-w-3xl mx-auto leading-relaxed">
            Don't just take our word for it. Here's what our wonderful customers have to say about their experience at Himalayan Brew Café.
          </p>
        </motion.div>

        {/* Testimonials Carousel */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          viewport={{ once: true }}
          className="mb-12"
        >
          <Carousel
            opts={{
              align: "start",
              loop: true,
            }}
            className="w-full"
          >
            <CarouselContent className="-ml-2 md:-ml-4">
              {featuredTestimonials.map((testimonial, index) => (
                <CarouselItem key={testimonial.id} className="pl-2 md:pl-4 md:basis-1/2 lg:basis-1/3">
                  <Card className="h-full border-cafe-sand hover:shadow-cafe-lg transition-shadow duration-300">
                    <CardContent className="p-6 space-y-4">
                      {/* Quote Icon */}
                      <div className="flex justify-center">
                        <div className="w-12 h-12 bg-cafe-gold rounded-full flex items-center justify-center">
                          <Quote className="h-6 w-6 text-cafe-dark-brown" />
                        </div>
                      </div>

                      {/* Rating */}
                      <div className="flex justify-center gap-1">
                        {[...Array(testimonial.rating)].map((_, i) => (
                          <Star key={i} className="h-5 w-5 fill-cafe-gold text-cafe-gold" />
                        ))}
                      </div>

                      {/* Content */}
                      <blockquote className="text-cafe-brown leading-relaxed text-center italic">
                        "{testimonial.content}"
                      </blockquote>

                      {/* Author */}
                      <div className="text-center pt-4 border-t border-cafe-sand">
                        <div className="w-16 h-16 mx-auto mb-3 rounded-full overflow-hidden">
                          <img
                            src={testimonial.image}
                            alt={testimonial.name}
                            className="w-full h-full object-cover"
                          />
                        </div>
                        <h4 className="font-serif font-semibold text-cafe-dark-brown">
                          {testimonial.name}
                        </h4>
                        <p className="text-sm text-cafe-brown">
                          {testimonial.role}
                        </p>
                        <p className="text-xs text-cafe-brown mt-1">
                          {new Date(testimonial.date).toLocaleDateString()}
                        </p>
                      </div>
                    </CardContent>
                  </Card>
                </CarouselItem>
              ))}
            </CarouselContent>
            <CarouselPrevious className="border-cafe-brown text-cafe-brown hover:bg-cafe-brown hover:text-cafe-cream" />
            <CarouselNext className="border-cafe-brown text-cafe-brown hover:bg-cafe-brown hover:text-cafe-cream" />
          </Carousel>
        </motion.div>

        {/* Stats */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          viewport={{ once: true }}
          className="grid grid-cols-2 md:grid-cols-4 gap-8 mb-12"
        >
          <div className="text-center">
            <div className="text-3xl md:text-4xl font-bold text-cafe-dark-brown mb-2">
              500+
            </div>
            <div className="text-cafe-brown font-medium">
              Happy Customers
            </div>
          </div>
          <div className="text-center">
            <div className="text-3xl md:text-4xl font-bold text-cafe-dark-brown mb-2">
              4.9
            </div>
            <div className="text-cafe-brown font-medium">
              Average Rating
            </div>
          </div>
          <div className="text-center">
            <div className="text-3xl md:text-4xl font-bold text-cafe-dark-brown mb-2">
              3+
            </div>
            <div className="text-cafe-brown font-medium">
              Years Serving
            </div>
          </div>
          <div className="text-center">
            <div className="text-3xl md:text-4xl font-bold text-cafe-dark-brown mb-2">
              50+
            </div>
            <div className="text-cafe-brown font-medium">
              Menu Items
            </div>
          </div>
        </motion.div>

        {/* CTA Button */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.6 }}
          viewport={{ once: true }}
          className="text-center"
        >
          <Link href="/testimonials">
            <Button 
              size="lg" 
              className="bg-cafe-brown hover:bg-cafe-dark-brown text-cafe-cream px-8 py-3 text-lg font-semibold shadow-cafe-lg"
            >
              Read All Reviews
            </Button>
          </Link>
        </motion.div>
      </div>
    </section>
  );
}
