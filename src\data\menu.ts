export interface MenuItem {
  id: string;
  name: string;
  description: string;
  price: number;
  image: string;
  category: string;
  isPopular?: boolean;
  isVegetarian?: boolean;
  isVegan?: boolean;
}

export const menuCategories = [
  "Hot Drinks",
  "Cold Drinks", 
  "Snacks",
  "Bakery",
  "Traditional Nepali",
  "Breakfast"
];

export const menuItems: MenuItem[] = [
  // Hot Drinks
  {
    id: "hd1",
    name: "Himalayan Coffee",
    description: "Our signature blend from Ilam hills, rich and aromatic with notes of chocolate and nuts",
    price: 180,
    image: "https://images.unsplash.com/photo-1509042239860-f550ce710b93?w=400&h=300&fit=crop",
    category: "Hot Drinks",
    isPopular: true
  },
  {
    id: "hd2",
    name: "Masala Chai",
    description: "Traditional spiced tea with cardamom, cinnamon, and ginger, served with milk",
    price: 120,
    image: "https://images.unsplash.com/photo-1571934811356-5cc061b6821f?w=400&h=300&fit=crop",
    category: "Hot Drinks",
    isPopular: true,
    isVegetarian: true
  },
  {
    id: "hd3",
    name: "Butter Tea (Po Cha)",
    description: "Traditional Tibetan butter tea, perfect for cold Biratnagar mornings",
    price: 150,
    image: "https://images.unsplash.com/photo-1544787219-7f47ccb76574?w=400&h=300&fit=crop",
    category: "Hot Drinks",
    isVegetarian: true
  },
  {
    id: "hd4",
    name: "Cappuccino",
    description: "Classic Italian coffee with steamed milk foam and a sprinkle of cocoa",
    price: 200,
    image: "https://images.unsplash.com/photo-1572442388796-11668a67e53d?w=400&h=300&fit=crop",
    category: "Hot Drinks",
    isVegetarian: true
  },
  {
    id: "hd5",
    name: "Hot Chocolate",
    description: "Rich and creamy hot chocolate topped with whipped cream",
    price: 220,
    image: "https://images.unsplash.com/photo-1542990253-0d0f5be5f0ed?w=400&h=300&fit=crop",
    category: "Hot Drinks",
    isVegetarian: true
  },

  // Cold Drinks
  {
    id: "cd1",
    name: "Iced Himalayan Coffee",
    description: "Our signature coffee served cold with ice and a touch of condensed milk",
    price: 200,
    image: "https://images.unsplash.com/photo-1461023058943-07fcbe16d735?w=400&h=300&fit=crop",
    category: "Cold Drinks",
    isPopular: true
  },
  {
    id: "cd2",
    name: "Mango Lassi",
    description: "Creamy yogurt drink blended with fresh mango and cardamom",
    price: 180,
    image: "https://images.unsplash.com/photo-1553909489-cd47e0ef937f?w=400&h=300&fit=crop",
    category: "Cold Drinks",
    isVegetarian: true
  },
  {
    id: "cd3",
    name: "Fresh Lime Soda",
    description: "Refreshing lime juice with soda water and mint leaves",
    price: 120,
    image: "https://images.unsplash.com/photo-1556679343-c7306c1976bc?w=400&h=300&fit=crop",
    category: "Cold Drinks",
    isVegan: true
  },
  {
    id: "cd4",
    name: "Iced Chai Latte",
    description: "Spiced chai served cold with milk and ice",
    price: 160,
    image: "https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=300&fit=crop",
    category: "Cold Drinks",
    isVegetarian: true
  },

  // Snacks
  {
    id: "sn1",
    name: "Chicken Momo",
    description: "Traditional Nepali dumplings filled with seasoned chicken, served with spicy sauce",
    price: 280,
    image: "https://images.unsplash.com/photo-1496116218417-1a781b1c416c?w=400&h=300&fit=crop",
    category: "Snacks",
    isPopular: true
  },
  {
    id: "sn2",
    name: "Vegetable Momo",
    description: "Steamed dumplings filled with fresh vegetables and herbs",
    price: 240,
    image: "https://images.unsplash.com/photo-1563379091339-03246963d96a?w=400&h=300&fit=crop",
    category: "Snacks",
    isVegetarian: true,
    isPopular: true
  },
  {
    id: "sn3",
    name: "Samosa Chat",
    description: "Crispy samosas topped with yogurt, chutneys, and spices",
    price: 160,
    image: "https://images.unsplash.com/photo-1601050690597-df0568f70950?w=400&h=300&fit=crop",
    category: "Snacks",
    isVegetarian: true
  },
  {
    id: "sn4",
    name: "French Fries",
    description: "Golden crispy fries served with ketchup and mayo",
    price: 180,
    image: "https://images.unsplash.com/photo-1573080496219-bb080dd4f877?w=400&h=300&fit=crop",
    category: "Snacks",
    isVegetarian: true
  },

  // Bakery
  {
    id: "bk1",
    name: "Chocolate Croissant",
    description: "Buttery croissant filled with rich dark chocolate",
    price: 150,
    image: "https://images.unsplash.com/photo-1555507036-ab794f4afe5e?w=400&h=300&fit=crop",
    category: "Bakery",
    isVegetarian: true
  },
  {
    id: "bk2",
    name: "Banana Bread",
    description: "Homemade banana bread with walnuts, perfect with coffee",
    price: 120,
    image: "https://images.unsplash.com/photo-1586985289688-ca3cf47d3e6e?w=400&h=300&fit=crop",
    category: "Bakery",
    isVegetarian: true,
    isPopular: true
  },
  {
    id: "bk3",
    name: "Blueberry Muffin",
    description: "Fresh baked muffin loaded with juicy blueberries",
    price: 140,
    image: "https://images.unsplash.com/photo-1607958996333-41aef7caefaa?w=400&h=300&fit=crop",
    category: "Bakery",
    isVegetarian: true
  },

  // Traditional Nepali
  {
    id: "tn1",
    name: "Dal Bhat Set",
    description: "Traditional Nepali meal with lentil soup, rice, vegetables, and pickle",
    price: 350,
    image: "https://images.unsplash.com/photo-1585937421612-70a008356fbe?w=400&h=300&fit=crop",
    category: "Traditional Nepali",
    isVegetarian: true,
    isPopular: true
  },
  {
    id: "tn2",
    name: "Sel Roti",
    description: "Traditional ring-shaped rice bread, crispy outside and soft inside",
    price: 80,
    image: "https://images.unsplash.com/photo-1578985545062-69928b1d9587?w=400&h=300&fit=crop",
    category: "Traditional Nepali",
    isVegetarian: true
  },

  // Breakfast
  {
    id: "bf1",
    name: "English Breakfast",
    description: "Eggs, toast, baked beans, and hash browns with coffee",
    price: 420,
    image: "https://images.unsplash.com/photo-1525351484163-7529414344d8?w=400&h=300&fit=crop",
    category: "Breakfast",
    isVegetarian: true
  },
  {
    id: "bf2",
    name: "Pancakes",
    description: "Fluffy pancakes served with maple syrup and butter",
    price: 280,
    image: "https://images.unsplash.com/photo-1567620905732-2d1ec7ab7445?w=400&h=300&fit=crop",
    category: "Breakfast",
    isVegetarian: true
  }
];

export const popularItems = menuItems.filter(item => item.isPopular);

export const getItemsByCategory = (category: string) => {
  return menuItems.filter(item => item.category === category);
};
