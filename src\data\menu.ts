export interface MenuItem {
  id: string;
  name: string;
  description: string;
  price: number;
  image: string;
  category: string;
  isPopular?: boolean;
  isVegetarian?: boolean;
  isVegan?: boolean;
}

export const menuCategories = [
  "Hot Drinks",
  "Cold Drinks", 
  "Snacks",
  "Bakery",
  "Traditional Nepali",
  "Breakfast"
];

export const menuItems: MenuItem[] = [
  // Hot Drinks
  {
    id: "hd1",
    name: "Himalayan Coffee",
    description: "Our signature blend from Ilam hills, rich and aromatic with notes of chocolate and nuts",
    price: 180,
    image: "/images/himalayan-coffee.jpg",
    category: "Hot Drinks",
    isPopular: true
  },
  {
    id: "hd2", 
    name: "Masala Chai",
    description: "Traditional spiced tea with cardamom, cinnamon, and ginger, served with milk",
    price: 120,
    image: "/images/masala-chai.jpg",
    category: "Hot Drinks",
    isPopular: true,
    isVegetarian: true
  },
  {
    id: "hd3",
    name: "Butter Tea (Po Cha)",
    description: "Traditional Tibetan butter tea, perfect for cold Biratnagar mornings",
    price: 150,
    image: "/images/butter-tea.jpg", 
    category: "Hot Drinks",
    isVegetarian: true
  },
  {
    id: "hd4",
    name: "Cappuccino",
    description: "Classic Italian coffee with steamed milk foam and a sprinkle of cocoa",
    price: 200,
    image: "/images/cappuccino.jpg",
    category: "Hot Drinks",
    isVegetarian: true
  },
  {
    id: "hd5",
    name: "Hot Chocolate",
    description: "Rich and creamy hot chocolate topped with whipped cream",
    price: 220,
    image: "/images/hot-chocolate.jpg",
    category: "Hot Drinks",
    isVegetarian: true
  },

  // Cold Drinks
  {
    id: "cd1",
    name: "Iced Himalayan Coffee",
    description: "Our signature coffee served cold with ice and a touch of condensed milk",
    price: 200,
    image: "/images/iced-coffee.jpg",
    category: "Cold Drinks",
    isPopular: true
  },
  {
    id: "cd2",
    name: "Mango Lassi",
    description: "Creamy yogurt drink blended with fresh mango and cardamom",
    price: 180,
    image: "/images/mango-lassi.jpg",
    category: "Cold Drinks",
    isVegetarian: true
  },
  {
    id: "cd3",
    name: "Fresh Lime Soda",
    description: "Refreshing lime juice with soda water and mint leaves",
    price: 120,
    image: "/images/lime-soda.jpg",
    category: "Cold Drinks",
    isVegan: true
  },
  {
    id: "cd4",
    name: "Iced Chai Latte",
    description: "Spiced chai served cold with milk and ice",
    price: 160,
    image: "/images/iced-chai.jpg",
    category: "Cold Drinks",
    isVegetarian: true
  },

  // Snacks
  {
    id: "sn1",
    name: "Chicken Momo",
    description: "Traditional Nepali dumplings filled with seasoned chicken, served with spicy sauce",
    price: 280,
    image: "/images/chicken-momo.jpg",
    category: "Snacks",
    isPopular: true
  },
  {
    id: "sn2",
    name: "Vegetable Momo",
    description: "Steamed dumplings filled with fresh vegetables and herbs",
    price: 240,
    image: "/images/veg-momo.jpg",
    category: "Snacks",
    isVegetarian: true,
    isPopular: true
  },
  {
    id: "sn3",
    name: "Samosa Chat",
    description: "Crispy samosas topped with yogurt, chutneys, and spices",
    price: 160,
    image: "/images/samosa-chat.jpg",
    category: "Snacks",
    isVegetarian: true
  },
  {
    id: "sn4",
    name: "French Fries",
    description: "Golden crispy fries served with ketchup and mayo",
    price: 180,
    image: "/images/french-fries.jpg",
    category: "Snacks",
    isVegetarian: true
  },

  // Bakery
  {
    id: "bk1",
    name: "Chocolate Croissant",
    description: "Buttery croissant filled with rich dark chocolate",
    price: 150,
    image: "/images/chocolate-croissant.jpg",
    category: "Bakery",
    isVegetarian: true
  },
  {
    id: "bk2",
    name: "Banana Bread",
    description: "Homemade banana bread with walnuts, perfect with coffee",
    price: 120,
    image: "/images/banana-bread.jpg",
    category: "Bakery",
    isVegetarian: true,
    isPopular: true
  },
  {
    id: "bk3",
    name: "Blueberry Muffin",
    description: "Fresh baked muffin loaded with juicy blueberries",
    price: 140,
    image: "/images/blueberry-muffin.jpg",
    category: "Bakery",
    isVegetarian: true
  },

  // Traditional Nepali
  {
    id: "tn1",
    name: "Dal Bhat Set",
    description: "Traditional Nepali meal with lentil soup, rice, vegetables, and pickle",
    price: 350,
    image: "/images/dal-bhat.jpg",
    category: "Traditional Nepali",
    isVegetarian: true,
    isPopular: true
  },
  {
    id: "tn2",
    name: "Sel Roti",
    description: "Traditional ring-shaped rice bread, crispy outside and soft inside",
    price: 80,
    image: "/images/sel-roti.jpg",
    category: "Traditional Nepali",
    isVegetarian: true
  },

  // Breakfast
  {
    id: "bf1",
    name: "English Breakfast",
    description: "Eggs, toast, baked beans, and hash browns with coffee",
    price: 420,
    image: "/images/english-breakfast.jpg",
    category: "Breakfast",
    isVegetarian: true
  },
  {
    id: "bf2",
    name: "Pancakes",
    description: "Fluffy pancakes served with maple syrup and butter",
    price: 280,
    image: "/images/pancakes.jpg",
    category: "Breakfast",
    isVegetarian: true
  }
];

export const popularItems = menuItems.filter(item => item.isPopular);

export const getItemsByCategory = (category: string) => {
  return menuItems.filter(item => item.category === category);
};
