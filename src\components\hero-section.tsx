"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Coffee, MapPin, Clock } from "lucide-react";
import { motion } from "framer-motion";
import { cafeInfo } from "@/data/cafe-info";

export function HeroSection() {
  return (
    <section className="relative min-h-screen flex items-center justify-center bg-cafe-pattern">
      {/* Background Image Overlay */}
      <div
        className="absolute inset-0 bg-cover bg-center bg-no-repeat"
        style={{
          backgroundImage: "url('https://images.unsplash.com/photo-1554118811-1e0d58224f24?w=1920&h=1080&fit=crop')",
          backgroundBlendMode: "overlay",
        }}
      >
        <div className="absolute inset-0 bg-cafe-cream/80"></div>
      </div>

      {/* Content */}
      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="space-y-8"
        >
          {/* Logo and Title */}
          <div className="space-y-4">
            <motion.div
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="flex justify-center"
            >
              <Coffee className="h-16 w-16 text-cafe-brown" />
            </motion.div>
            
            <h1 className="font-serif text-5xl md:text-7xl font-bold text-cafe-dark-brown">
              {cafeInfo.name}
            </h1>
            
            <p className="text-xl md:text-2xl text-cafe-brown font-medium">
              {cafeInfo.tagline}
            </p>
          </div>

          {/* Description */}
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="max-w-3xl mx-auto text-lg text-cafe-brown leading-relaxed"
          >
            {cafeInfo.description}
          </motion.p>

          {/* CTA Buttons */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.6 }}
            className="flex flex-col sm:flex-row gap-4 justify-center items-center"
          >
            <Button 
              size="lg" 
              className="bg-cafe-brown hover:bg-cafe-dark-brown text-cafe-cream px-8 py-3 text-lg font-semibold shadow-cafe-lg"
            >
              View Our Menu
            </Button>
            <Button 
              variant="outline" 
              size="lg"
              className="border-cafe-brown text-cafe-brown hover:bg-cafe-brown hover:text-cafe-cream px-8 py-3 text-lg font-semibold"
            >
              Visit Gallery
            </Button>
          </motion.div>

          {/* Quick Info */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.8 }}
            className="flex flex-col md:flex-row gap-8 justify-center items-center text-cafe-brown"
          >
            <div className="flex items-center gap-2">
              <MapPin className="h-5 w-5" />
              <span className="font-medium">Main Road, Biratnagar</span>
            </div>
            <div className="flex items-center gap-2">
              <Clock className="h-5 w-5" />
              <span className="font-medium">6:00 AM - 10:00 PM</span>
            </div>
            <div className="flex items-center gap-2">
              <Coffee className="h-5 w-5" />
              <span className="font-medium">Fresh Coffee Daily</span>
            </div>
          </motion.div>
        </motion.div>
      </div>

      {/* Scroll Indicator */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.6, delay: 1 }}
        className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
      >
        <motion.div
          animate={{ y: [0, 10, 0] }}
          transition={{ duration: 2, repeat: Infinity }}
          className="w-6 h-10 border-2 border-cafe-brown rounded-full flex justify-center"
        >
          <motion.div
            animate={{ y: [0, 12, 0] }}
            transition={{ duration: 2, repeat: Infinity }}
            className="w-1 h-3 bg-cafe-brown rounded-full mt-2"
          />
        </motion.div>
      </motion.div>
    </section>
  );
}
