export interface Package {
  id: string;
  name: string;
  description: string;
  originalPrice: number;
  discountedPrice: number;
  savings: number;
  items: string[];
  validUntil: string;
  isPopular?: boolean;
  image: string;
}

export const packages: Package[] = [
  {
    id: "p1",
    name: "Morning Bliss Combo",
    description: "Perfect start to your day with our signature coffee and fresh pastry",
    originalPrice: 350,
    discountedPrice: 280,
    savings: 70,
    items: [
      "Himalayan Coffee (Regular)",
      "Choice of Fresh Pastry",
      "Free WiFi Access"
    ],
    validUntil: "2024-12-31",
    isPopular: true,
    image: "https://images.unsplash.com/photo-1495474472287-4d71bcdd2085?w=400&h=300&fit=crop"
  },
  {
    id: "p2",
    name: "Study Buddy Package",
    description: "Ideal for students - unlimited coffee refills and snacks for productive study sessions",
    originalPrice: 600,
    discountedPrice: 450,
    savings: 150,
    items: [
      "Unlimited Coffee Refills (4 hours)",
      "Choice of Snack",
      "Reserved Study Table",
      "Free WiFi & Power Outlet"
    ],
    validUntil: "2024-12-31",
    isPopular: true,
    image: "https://images.unsplash.com/photo-1522202176988-66273c2fd55f?w=400&h=300&fit=crop"
  },
  {
    id: "p3",
    name: "Momo Mania",
    description: "For momo lovers - try both chicken and vegetable momos with drinks",
    originalPrice: 640,
    discountedPrice: 520,
    savings: 120,
    items: [
      "Chicken Momo (Half Plate)",
      "Vegetable Momo (Half Plate)",
      "2 Masala Chai",
      "Complimentary Pickle"
    ],
    validUntil: "2024-12-31",
    image: "https://images.unsplash.com/photo-1496116218417-1a781b1c416c?w=400&h=300&fit=crop"
  },
  {
    id: "p4",
    name: "Date Night Special",
    description: "Romantic evening package for couples with premium coffee and desserts",
    originalPrice: 800,
    discountedPrice: 650,
    savings: 150,
    items: [
      "2 Premium Coffee (Cappuccino/Latte)",
      "Chocolate Croissant to Share",
      "Reserved Corner Table",
      "Complimentary Dessert"
    ],
    validUntil: "2024-12-31",
    image: "/images/packages/date-night.jpg"
  },
  {
    id: "p5",
    name: "Family Feast",
    description: "Perfect for family gatherings with traditional Nepali meal and drinks for 4",
    originalPrice: 1400,
    discountedPrice: 1100,
    savings: 300,
    items: [
      "2 Dal Bhat Sets",
      "1 Chicken Momo",
      "1 Vegetable Momo",
      "4 Drinks (Tea/Coffee/Lassi)",
      "Family Table Reservation"
    ],
    validUntil: "2024-12-31",
    isPopular: true,
    image: "/images/packages/family-feast.jpg"
  },
  {
    id: "p6",
    name: "Business Meeting Package",
    description: "Professional setting with premium coffee and light snacks for your meetings",
    originalPrice: 1200,
    discountedPrice: 950,
    savings: 250,
    items: [
      "Reserved Meeting Table (2 hours)",
      "Premium Coffee for 4 people",
      "Assorted Pastries",
      "Notepad & Pen",
      "Free WiFi & Presentation Setup"
    ],
    validUntil: "2024-12-31",
    image: "/images/packages/business-meeting.jpg"
  },
  {
    id: "p7",
    name: "Weekend Brunch",
    description: "Leisurely weekend brunch with hearty breakfast and unlimited coffee",
    originalPrice: 750,
    discountedPrice: 600,
    savings: 150,
    items: [
      "English Breakfast OR Pancakes",
      "Unlimited Coffee (2 hours)",
      "Fresh Fruit Juice",
      "Weekend Newspaper",
      "Relaxed Atmosphere"
    ],
    validUntil: "2024-12-31",
    image: "/images/packages/weekend-brunch.jpg"
  },
  {
    id: "p8",
    name: "Cultural Experience",
    description: "Taste authentic Nepal with traditional foods and drinks",
    originalPrice: 550,
    discountedPrice: 450,
    savings: 100,
    items: [
      "Butter Tea (Po Cha)",
      "Sel Roti (2 pieces)",
      "Traditional Pickle",
      "Cultural Story Session",
      "Photo with Traditional Dress"
    ],
    validUntil: "2024-12-31",
    image: "/images/packages/cultural-experience.jpg"
  }
];

export const popularPackages = packages.filter(pkg => pkg.isPopular);

export const getPackageById = (id: string) => {
  return packages.find(pkg => pkg.id === id);
};

export const calculateSavings = (originalPrice: number, discountedPrice: number) => {
  return originalPrice - discountedPrice;
};

export const calculateDiscountPercentage = (originalPrice: number, discountedPrice: number) => {
  return Math.round(((originalPrice - discountedPrice) / originalPrice) * 100);
};
