"use client";

import { motion } from "framer-motion";
import { Card, CardContent } from "@/components/ui/card";
import { MapPin, Phone, Mail, Clock } from "lucide-react";
import { ContactForm } from "./contact-form";
import { cafeInfo } from "@/data/cafe-info";

export function ContactSection() {
  return (
    <section className="py-20 bg-cafe-cream">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="font-serif text-4xl md:text-5xl font-bold text-cafe-dark-brown mb-6">
            Visit Us Today
          </h2>
          <p className="text-xl text-cafe-brown max-w-3xl mx-auto leading-relaxed">
            We&apos;d love to welcome you to our cozy café. Drop by for a cup of coffee or get in touch with us for any inquiries.
          </p>
        </motion.div>

        <div className="grid lg:grid-cols-2 gap-12">
          {/* Contact Form */}
          <motion.div
            initial={{ opacity: 0, x: -30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <ContactForm />
          </motion.div>

          {/* Contact Information */}
          <motion.div
            initial={{ opacity: 0, x: 30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            viewport={{ once: true }}
            className="space-y-6"
          >
            {/* Contact Details */}
            <Card className="border-cafe-beige shadow-cafe">
              <CardContent className="p-6 space-y-6">
                <h3 className="font-serif text-2xl font-semibold text-cafe-dark-brown">
                  Contact Details
                </h3>
                
                <div className="space-y-4">
                  <div className="flex items-start gap-4">
                    <div className="w-12 h-12 bg-cafe-beige rounded-full flex items-center justify-center flex-shrink-0">
                      <MapPin className="h-6 w-6 text-cafe-brown" />
                    </div>
                    <div>
                      <h4 className="font-semibold text-cafe-dark-brown mb-1">Address</h4>
                      <p className="text-cafe-brown">{cafeInfo.contact.address}</p>
                    </div>
                  </div>

                  <div className="flex items-start gap-4">
                    <div className="w-12 h-12 bg-cafe-beige rounded-full flex items-center justify-center flex-shrink-0">
                      <Phone className="h-6 w-6 text-cafe-brown" />
                    </div>
                    <div>
                      <h4 className="font-semibold text-cafe-dark-brown mb-1">Phone</h4>
                      <a 
                        href={`tel:${cafeInfo.contact.phone}`}
                        className="text-cafe-brown hover:text-cafe-dark-brown transition-colors"
                      >
                        {cafeInfo.contact.phone}
                      </a>
                    </div>
                  </div>

                  <div className="flex items-start gap-4">
                    <div className="w-12 h-12 bg-cafe-beige rounded-full flex items-center justify-center flex-shrink-0">
                      <Mail className="h-6 w-6 text-cafe-brown" />
                    </div>
                    <div>
                      <h4 className="font-semibold text-cafe-dark-brown mb-1">Email</h4>
                      <a 
                        href={`mailto:${cafeInfo.contact.email}`}
                        className="text-cafe-brown hover:text-cafe-dark-brown transition-colors"
                      >
                        {cafeInfo.contact.email}
                      </a>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Opening Hours */}
            <Card className="border-cafe-beige shadow-cafe">
              <CardContent className="p-6 space-y-6">
                <h3 className="font-serif text-2xl font-semibold text-cafe-dark-brown">
                  Opening Hours
                </h3>
                
                <div className="space-y-4">
                  <div className="flex items-start gap-4">
                    <div className="w-12 h-12 bg-cafe-beige rounded-full flex items-center justify-center flex-shrink-0">
                      <Clock className="h-6 w-6 text-cafe-brown" />
                    </div>
                    <div className="flex-1">
                      <div className="flex justify-between items-center mb-2">
                        <span className="font-semibold text-cafe-dark-brown">Monday - Friday</span>
                        <span className="text-cafe-brown">{cafeInfo.hours.weekdays}</span>
                      </div>
                      <div className="flex justify-between items-center mb-2">
                        <span className="font-semibold text-cafe-dark-brown">Saturday - Sunday</span>
                        <span className="text-cafe-brown">{cafeInfo.hours.weekends}</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="font-semibold text-cafe-dark-brown">Public Holidays</span>
                        <span className="text-cafe-brown">{cafeInfo.hours.holidays}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Map Placeholder */}
            <Card className="border-cafe-beige shadow-cafe">
              <CardContent className="p-0">
                <div className="aspect-video bg-cafe-beige rounded-lg overflow-hidden">
                  <div className="w-full h-full flex items-center justify-center text-cafe-brown">
                    <div className="text-center">
                      <MapPin className="h-12 w-12 mx-auto mb-2" />
                      <p className="font-semibold">Interactive Map</p>
                      <p className="text-sm">Find us on Main Road, Biratnagar</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </div>
    </section>
  );
}
