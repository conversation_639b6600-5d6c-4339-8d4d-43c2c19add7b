[{"C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\app\\layout.tsx": "1", "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\app\\page.tsx": "2", "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\about-section.tsx": "3", "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\contact-form.tsx": "4", "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\contact-section.tsx": "5", "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\footer.tsx": "6", "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\gallery-preview.tsx": "7", "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\hero-section.tsx": "8", "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\menu-highlights.tsx": "9", "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\navigation.tsx": "10", "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\packages-section.tsx": "11", "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\testimonials-section.tsx": "12", "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\ui\\accordion.tsx": "13", "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\ui\\badge.tsx": "14", "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\ui\\button.tsx": "15", "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\ui\\card.tsx": "16", "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\ui\\carousel.tsx": "17", "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\ui\\dialog.tsx": "18", "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\ui\\form.tsx": "19", "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\ui\\input.tsx": "20", "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\ui\\label.tsx": "21", "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\ui\\textarea.tsx": "22", "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\data\\cafe-info.ts": "23", "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\data\\gallery.ts": "24", "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\data\\menu.ts": "25", "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\data\\packages.ts": "26", "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\data\\testimonials.ts": "27", "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\lib\\utils.ts": "28"}, {"size": 1460, "mtime": 1753249865662, "results": "29", "hashOfConfig": "30"}, {"size": 896, "mtime": 1753250144737, "results": "31", "hashOfConfig": "30"}, {"size": 5410, "mtime": 1753250256404, "results": "32", "hashOfConfig": "30"}, {"size": 7556, "mtime": 1753250279846, "results": "33", "hashOfConfig": "30"}, {"size": 6620, "mtime": 1753250293927, "results": "34", "hashOfConfig": "30"}, {"size": 7145, "mtime": 1753250099674, "results": "35", "hashOfConfig": "30"}, {"size": 4817, "mtime": 1753250371697, "results": "36", "hashOfConfig": "30"}, {"size": 4515, "mtime": 1753249902113, "results": "37", "hashOfConfig": "30"}, {"size": 4515, "mtime": 1753249943572, "results": "38", "hashOfConfig": "30"}, {"size": 2875, "mtime": 1753249883045, "results": "39", "hashOfConfig": "30"}, {"size": 5956, "mtime": 1753250307699, "results": "40", "hashOfConfig": "30"}, {"size": 6478, "mtime": 1753250383616, "results": "41", "hashOfConfig": "30"}, {"size": 2053, "mtime": 1753249512148, "results": "42", "hashOfConfig": "30"}, {"size": 1631, "mtime": 1753249984384, "results": "43", "hashOfConfig": "30"}, {"size": 2123, "mtime": 1753249512064, "results": "44", "hashOfConfig": "30"}, {"size": 1989, "mtime": 1753249512030, "results": "45", "hashOfConfig": "30"}, {"size": 5556, "mtime": 1753249512203, "results": "46", "hashOfConfig": "30"}, {"size": 3982, "mtime": 1753249512163, "results": "47", "hashOfConfig": "30"}, {"size": 3759, "mtime": 1753249512128, "results": "48", "hashOfConfig": "30"}, {"size": 967, "mtime": 1753249512138, "results": "49", "hashOfConfig": "30"}, {"size": 611, "mtime": 1753249512134, "results": "50", "hashOfConfig": "30"}, {"size": 759, "mtime": 1753249512141, "results": "51", "hashOfConfig": "30"}, {"size": 2364, "mtime": 1753249731910, "results": "52", "hashOfConfig": "30"}, {"size": 5537, "mtime": 1753249804964, "results": "53", "hashOfConfig": "30"}, {"size": 5511, "mtime": 1753249760162, "results": "54", "hashOfConfig": "30"}, {"size": 4428, "mtime": 1753249830992, "results": "55", "hashOfConfig": "30"}, {"size": 3405, "mtime": 1753249778802, "results": "56", "hashOfConfig": "30"}, {"size": 166, "mtime": 1753249479644, "results": "57", "hashOfConfig": "30"}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1vl3o1q", {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\about-section.tsx", ["142"], [], "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\contact-form.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\contact-section.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\footer.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\gallery-preview.tsx", ["143", "144"], [], "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\hero-section.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\menu-highlights.tsx", ["145"], [], "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\navigation.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\packages-section.tsx", ["146"], [], "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\testimonials-section.tsx", ["147"], [], "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\ui\\accordion.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\ui\\badge.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\ui\\button.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\ui\\card.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\ui\\carousel.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\ui\\dialog.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\ui\\form.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\ui\\input.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\ui\\label.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\ui\\textarea.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\data\\cafe-info.ts", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\data\\gallery.ts", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\data\\menu.ts", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\data\\packages.ts", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\data\\testimonials.ts", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\lib\\utils.ts", [], [], {"ruleId": "148", "severity": 1, "message": "149", "line": 70, "column": 15, "nodeType": "150", "endLine": 74, "endColumn": 17}, {"ruleId": "148", "severity": 1, "message": "149", "line": 48, "column": 21, "nodeType": "150", "endLine": 52, "endColumn": 23}, {"ruleId": "148", "severity": 1, "message": "149", "line": 74, "column": 21, "nodeType": "150", "endLine": 78, "endColumn": 23}, {"ruleId": "148", "severity": 1, "message": "149", "line": 47, "column": 19, "nodeType": "150", "endLine": 51, "endColumn": 21}, {"ruleId": "148", "severity": 1, "message": "149", "line": 63, "column": 21, "nodeType": "150", "endLine": 67, "endColumn": 23}, {"ruleId": "148", "severity": 1, "message": "149", "line": 79, "column": 27, "nodeType": "150", "endLine": 83, "endColumn": 29}, "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement"]