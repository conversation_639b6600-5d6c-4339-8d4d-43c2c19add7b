[{"C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\app\\layout.tsx": "1", "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\app\\page.tsx": "2", "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\about-section.tsx": "3", "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\contact-form.tsx": "4", "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\contact-section.tsx": "5", "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\footer.tsx": "6", "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\gallery-preview.tsx": "7", "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\hero-section.tsx": "8", "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\menu-highlights.tsx": "9", "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\navigation.tsx": "10", "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\packages-section.tsx": "11", "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\testimonials-section.tsx": "12", "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\ui\\accordion.tsx": "13", "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\ui\\badge.tsx": "14", "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\ui\\button.tsx": "15", "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\ui\\card.tsx": "16", "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\ui\\carousel.tsx": "17", "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\ui\\dialog.tsx": "18", "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\ui\\form.tsx": "19", "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\ui\\input.tsx": "20", "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\ui\\label.tsx": "21", "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\ui\\textarea.tsx": "22", "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\data\\cafe-info.ts": "23", "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\data\\gallery.ts": "24", "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\data\\menu.ts": "25", "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\data\\packages.ts": "26", "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\data\\testimonials.ts": "27", "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\lib\\utils.ts": "28"}, {"size": 1460, "mtime": 1753249865662, "results": "29", "hashOfConfig": "30"}, {"size": 896, "mtime": 1753250144737, "results": "31", "hashOfConfig": "30"}, {"size": 5400, "mtime": 1753249923523, "results": "32", "hashOfConfig": "30"}, {"size": 7546, "mtime": 1753250076860, "results": "33", "hashOfConfig": "30"}, {"size": 6615, "mtime": 1753250122243, "results": "34", "hashOfConfig": "30"}, {"size": 7145, "mtime": 1753250099674, "results": "35", "hashOfConfig": "30"}, {"size": 4929, "mtime": 1753250053198, "results": "36", "hashOfConfig": "30"}, {"size": 4515, "mtime": 1753249902113, "results": "37", "hashOfConfig": "30"}, {"size": 4515, "mtime": 1753249943572, "results": "38", "hashOfConfig": "30"}, {"size": 2875, "mtime": 1753249883045, "results": "39", "hashOfConfig": "30"}, {"size": 5951, "mtime": 1753250010260, "results": "40", "hashOfConfig": "30"}, {"size": 6463, "mtime": 1753250033821, "results": "41", "hashOfConfig": "30"}, {"size": 2053, "mtime": 1753249512148, "results": "42", "hashOfConfig": "30"}, {"size": 1631, "mtime": 1753249984384, "results": "43", "hashOfConfig": "30"}, {"size": 2123, "mtime": 1753249512064, "results": "44", "hashOfConfig": "30"}, {"size": 1989, "mtime": 1753249512030, "results": "45", "hashOfConfig": "30"}, {"size": 5556, "mtime": 1753249512203, "results": "46", "hashOfConfig": "30"}, {"size": 3982, "mtime": 1753249512163, "results": "47", "hashOfConfig": "30"}, {"size": 3759, "mtime": 1753249512128, "results": "48", "hashOfConfig": "30"}, {"size": 967, "mtime": 1753249512138, "results": "49", "hashOfConfig": "30"}, {"size": 611, "mtime": 1753249512134, "results": "50", "hashOfConfig": "30"}, {"size": 759, "mtime": 1753249512141, "results": "51", "hashOfConfig": "30"}, {"size": 2364, "mtime": 1753249731910, "results": "52", "hashOfConfig": "30"}, {"size": 5537, "mtime": 1753249804964, "results": "53", "hashOfConfig": "30"}, {"size": 5511, "mtime": 1753249760162, "results": "54", "hashOfConfig": "30"}, {"size": 4428, "mtime": 1753249830992, "results": "55", "hashOfConfig": "30"}, {"size": 3405, "mtime": 1753249778802, "results": "56", "hashOfConfig": "30"}, {"size": 166, "mtime": 1753249479644, "results": "57", "hashOfConfig": "30"}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1vl3o1q", {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\about-section.tsx", ["142", "143", "144"], [], "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\contact-form.tsx", ["145", "146"], [], "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\contact-section.tsx", ["147"], [], "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\footer.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\gallery-preview.tsx", ["148", "149", "150", "151", "152"], [], "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\hero-section.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\menu-highlights.tsx", ["153"], [], "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\navigation.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\packages-section.tsx", ["154", "155"], [], "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\testimonials-section.tsx", ["156", "157", "158", "159", "160", "161"], [], "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\ui\\accordion.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\ui\\badge.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\ui\\button.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\ui\\card.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\ui\\carousel.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\ui\\dialog.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\ui\\form.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\ui\\input.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\ui\\label.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\ui\\textarea.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\data\\cafe-info.ts", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\data\\gallery.ts", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\data\\menu.ts", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\data\\packages.ts", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\data\\testimonials.ts", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\lib\\utils.ts", [], [], {"ruleId": "162", "severity": 2, "message": "163", "line": 53, "column": 105, "nodeType": "164", "messageId": "165", "suggestions": "166"}, {"ruleId": "162", "severity": 2, "message": "163", "line": 56, "column": 157, "nodeType": "164", "messageId": "165", "suggestions": "167"}, {"ruleId": "168", "severity": 1, "message": "169", "line": 70, "column": 15, "nodeType": "170", "endLine": 74, "endColumn": 17}, {"ruleId": "162", "severity": 2, "message": "163", "line": 73, "column": 41, "nodeType": "164", "messageId": "165", "suggestions": "171"}, {"ruleId": "162", "severity": 2, "message": "163", "line": 93, "column": 60, "nodeType": "164", "messageId": "165", "suggestions": "172"}, {"ruleId": "162", "severity": 2, "message": "163", "line": 25, "column": 15, "nodeType": "164", "messageId": "165", "suggestions": "173"}, {"ruleId": "174", "severity": 1, "message": "175", "line": 7, "column": 18, "nodeType": null, "messageId": "176", "endLine": 7, "endColumn": 19}, {"ruleId": "174", "severity": 1, "message": "177", "line": 12, "column": 10, "nodeType": null, "messageId": "176", "endLine": 12, "endColumn": 23}, {"ruleId": "174", "severity": 1, "message": "178", "line": 12, "column": 25, "nodeType": null, "messageId": "176", "endLine": 12, "endColumn": 41}, {"ruleId": "168", "severity": 1, "message": "169", "line": 50, "column": 21, "nodeType": "170", "endLine": 54, "endColumn": 23}, {"ruleId": "168", "severity": 1, "message": "169", "line": 76, "column": 21, "nodeType": "170", "endLine": 80, "endColumn": 23}, {"ruleId": "168", "severity": 1, "message": "169", "line": 47, "column": 19, "nodeType": "170", "endLine": 51, "endColumn": 21}, {"ruleId": "168", "severity": 1, "message": "169", "line": 63, "column": 21, "nodeType": "170", "endLine": 67, "endColumn": 23}, {"ruleId": "162", "severity": 2, "message": "163", "line": 97, "column": 29, "nodeType": "164", "messageId": "165", "suggestions": "179"}, {"ruleId": "162", "severity": 2, "message": "163", "line": 33, "column": 16, "nodeType": "164", "messageId": "165", "suggestions": "180"}, {"ruleId": "162", "severity": 2, "message": "163", "line": 33, "column": 50, "nodeType": "164", "messageId": "165", "suggestions": "181"}, {"ruleId": "174", "severity": 1, "message": "182", "line": 53, "column": 55, "nodeType": null, "messageId": "176", "endLine": 53, "endColumn": 60}, {"ruleId": "162", "severity": 2, "message": "183", "line": 73, "column": 25, "nodeType": "164", "messageId": "165", "suggestions": "184"}, {"ruleId": "162", "severity": 2, "message": "183", "line": 73, "column": 47, "nodeType": "164", "messageId": "165", "suggestions": "185"}, {"ruleId": "168", "severity": 1, "message": "169", "line": 79, "column": 27, "nodeType": "170", "endLine": 83, "endColumn": 29}, "react/no-unescaped-entities", "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", "JSXText", "unescapedEntityAlts", ["186", "187", "188", "189"], ["190", "191", "192", "193"], "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", ["194", "195", "196", "197"], ["198", "199", "200", "201"], ["202", "203", "204", "205"], "@typescript-eslint/no-unused-vars", "'X' is defined but never used.", "unusedVar", "'selectedImage' is assigned a value but never used.", "'setSelectedImage' is assigned a value but never used.", ["206", "207", "208", "209"], ["210", "211", "212", "213"], ["214", "215", "216", "217"], "'index' is defined but never used.", "`\"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.", ["218", "219", "220", "221"], ["222", "223", "224", "225"], {"messageId": "226", "data": "227", "fix": "228", "desc": "229"}, {"messageId": "226", "data": "230", "fix": "231", "desc": "232"}, {"messageId": "226", "data": "233", "fix": "234", "desc": "235"}, {"messageId": "226", "data": "236", "fix": "237", "desc": "238"}, {"messageId": "226", "data": "239", "fix": "240", "desc": "229"}, {"messageId": "226", "data": "241", "fix": "242", "desc": "232"}, {"messageId": "226", "data": "243", "fix": "244", "desc": "235"}, {"messageId": "226", "data": "245", "fix": "246", "desc": "238"}, {"messageId": "226", "data": "247", "fix": "248", "desc": "229"}, {"messageId": "226", "data": "249", "fix": "250", "desc": "232"}, {"messageId": "226", "data": "251", "fix": "252", "desc": "235"}, {"messageId": "226", "data": "253", "fix": "254", "desc": "238"}, {"messageId": "226", "data": "255", "fix": "256", "desc": "229"}, {"messageId": "226", "data": "257", "fix": "258", "desc": "232"}, {"messageId": "226", "data": "259", "fix": "260", "desc": "235"}, {"messageId": "226", "data": "261", "fix": "262", "desc": "238"}, {"messageId": "226", "data": "263", "fix": "264", "desc": "229"}, {"messageId": "226", "data": "265", "fix": "266", "desc": "232"}, {"messageId": "226", "data": "267", "fix": "268", "desc": "235"}, {"messageId": "226", "data": "269", "fix": "270", "desc": "238"}, {"messageId": "226", "data": "271", "fix": "272", "desc": "229"}, {"messageId": "226", "data": "273", "fix": "274", "desc": "232"}, {"messageId": "226", "data": "275", "fix": "276", "desc": "235"}, {"messageId": "226", "data": "277", "fix": "278", "desc": "238"}, {"messageId": "226", "data": "279", "fix": "280", "desc": "229"}, {"messageId": "226", "data": "281", "fix": "282", "desc": "232"}, {"messageId": "226", "data": "283", "fix": "284", "desc": "235"}, {"messageId": "226", "data": "285", "fix": "286", "desc": "238"}, {"messageId": "226", "data": "287", "fix": "288", "desc": "229"}, {"messageId": "226", "data": "289", "fix": "290", "desc": "232"}, {"messageId": "226", "data": "291", "fix": "292", "desc": "235"}, {"messageId": "226", "data": "293", "fix": "294", "desc": "238"}, {"messageId": "226", "data": "295", "fix": "296", "desc": "297"}, {"messageId": "226", "data": "298", "fix": "299", "desc": "300"}, {"messageId": "226", "data": "301", "fix": "302", "desc": "303"}, {"messageId": "226", "data": "304", "fix": "305", "desc": "306"}, {"messageId": "226", "data": "307", "fix": "308", "desc": "297"}, {"messageId": "226", "data": "309", "fix": "310", "desc": "300"}, {"messageId": "226", "data": "311", "fix": "312", "desc": "303"}, {"messageId": "226", "data": "313", "fix": "314", "desc": "306"}, "replaceWithAlt", {"alt": "315"}, {"range": "316", "text": "317"}, "Replace with `&apos;`.", {"alt": "318"}, {"range": "319", "text": "320"}, "Replace with `&lsquo;`.", {"alt": "321"}, {"range": "322", "text": "323"}, "Replace with `&#39;`.", {"alt": "324"}, {"range": "325", "text": "326"}, "Replace with `&rsquo;`.", {"alt": "315"}, {"range": "327", "text": "328"}, {"alt": "318"}, {"range": "329", "text": "330"}, {"alt": "321"}, {"range": "331", "text": "332"}, {"alt": "324"}, {"range": "333", "text": "334"}, {"alt": "315"}, {"range": "335", "text": "336"}, {"alt": "318"}, {"range": "337", "text": "338"}, {"alt": "321"}, {"range": "339", "text": "340"}, {"alt": "324"}, {"range": "341", "text": "342"}, {"alt": "315"}, {"range": "343", "text": "344"}, {"alt": "318"}, {"range": "345", "text": "346"}, {"alt": "321"}, {"range": "347", "text": "348"}, {"alt": "324"}, {"range": "349", "text": "350"}, {"alt": "315"}, {"range": "351", "text": "352"}, {"alt": "318"}, {"range": "353", "text": "354"}, {"alt": "321"}, {"range": "355", "text": "356"}, {"alt": "324"}, {"range": "357", "text": "358"}, {"alt": "315"}, {"range": "359", "text": "360"}, {"alt": "318"}, {"range": "361", "text": "362"}, {"alt": "321"}, {"range": "363", "text": "364"}, {"alt": "324"}, {"range": "365", "text": "366"}, {"alt": "315"}, {"range": "367", "text": "368"}, {"alt": "318"}, {"range": "369", "text": "370"}, {"alt": "321"}, {"range": "371", "text": "372"}, {"alt": "324"}, {"range": "373", "text": "374"}, {"alt": "315"}, {"range": "375", "text": "376"}, {"alt": "318"}, {"range": "377", "text": "378"}, {"alt": "321"}, {"range": "379", "text": "380"}, {"alt": "324"}, {"range": "381", "text": "382"}, {"alt": "383"}, {"range": "384", "text": "385"}, "Replace with `&quot;`.", {"alt": "386"}, {"range": "387", "text": "388"}, "Replace with `&ldquo;`.", {"alt": "389"}, {"range": "390", "text": "391"}, "Replace with `&#34;`.", {"alt": "392"}, {"range": "393", "text": "394"}, "Replace with `&rdquo;`.", {"alt": "383"}, {"range": "395", "text": "396"}, {"alt": "386"}, {"range": "397", "text": "398"}, {"alt": "389"}, {"range": "399", "text": "400"}, {"alt": "392"}, {"range": "401", "text": "402"}, "&apos;", [2042, 2281], "\n                Our journey began in 2020 when our founder, inspired by the rich coffee culture of Nepal&apos;s hills and the bustling energy of Biratnagar, decided to create a space that celebrates both tradition and modernity.\n              ", "&lsquo;", [2042, 2281], "\n                Our journey began in 2020 when our founder, inspired by the rich coffee culture of Nepal&lsquo;s hills and the bustling energy of Biratnagar, decided to create a space that celebrates both tradition and modernity.\n              ", "&#39;", [2042, 2281], "\n                Our journey began in 2020 when our founder, inspired by the rich coffee culture of Nepal&#39;s hills and the bustling energy of Biratnagar, decided to create a space that celebrates both tradition and modernity.\n              ", "&rsquo;", [2042, 2281], "\n                Our journey began in 2020 when our founder, inspired by the rich coffee culture of Nepal&rsquo;s hills and the bustling energy of Biratnagar, decided to create a space that celebrates both tradition and modernity.\n              ", [2331, 2522], "\n                We source our coffee beans directly from the hills of Ilam and Gulmi, supporting local farmers and ensuring every cup tells a story of Nepal&apos;s coffee heritage.\n              ", [2331, 2522], "\n                We source our coffee beans directly from the hills of Ilam and Gulmi, supporting local farmers and ensuring every cup tells a story of Nepal&lsquo;s coffee heritage.\n              ", [2331, 2522], "\n                We source our coffee beans directly from the hills of Ilam and Gulmi, supporting local farmers and ensuring every cup tells a story of Nepal&#39;s coffee heritage.\n              ", [2331, 2522], "\n                We source our coffee beans directly from the hills of Ilam and Gulmi, supporting local farmers and ensuring every cup tells a story of Nepal&rsquo;s coffee heritage.\n              ", [2254, 2340], "\n          Thank you for reaching out. We&apos;ll get back to you within 24 hours.\n        ", [2254, 2340], "\n          Thank you for reaching out. We&lsquo;ll get back to you within 24 hours.\n        ", [2254, 2340], "\n          Thank you for reaching out. We&#39;ll get back to you within 24 hours.\n        ", [2254, 2340], "\n          Thank you for reaching out. We&rsquo;ll get back to you within 24 hours.\n        ", [2892, 2986], "\n          Have a question or want to make a reservation? We&apos;d love to hear from you!\n        ", [2892, 2986], "\n          Have a question or want to make a reservation? We&lsquo;d love to hear from you!\n        ", [2892, 2986], "\n          Have a question or want to make a reservation? We&#39;d love to hear from you!\n        ", [2892, 2986], "\n          Have a question or want to make a reservation? We&rsquo;d love to hear from you!\n        ", [905, 1042], "\n            We&apos;d love to welcome you to our cozy café. Drop by for a cup of coffee or get in touch with us for any inquiries.\n          ", [905, 1042], "\n            We&lsquo;d love to welcome you to our cozy café. Drop by for a cup of coffee or get in touch with us for any inquiries.\n          ", [905, 1042], "\n            We&#39;d love to welcome you to our cozy café. Drop by for a cup of coffee or get in touch with us for any inquiries.\n          ", [905, 1042], "\n            We&rsquo;d love to welcome you to our cozy café. Drop by for a cup of coffee or get in touch with us for any inquiries.\n          ", [4158, 4222], "\n                        What&apos;s Included:\n                      ", [4158, 4222], "\n                        What&lsquo;s Included:\n                      ", [4158, 4222], "\n                        What&#39;s Included:\n                      ", [4158, 4222], "\n                        What&rsquo;s Included:\n                      ", [1076, 1227], "\n            Don&apos;t just take our word for it. Here's what our wonderful customers have to say about their experience at Himalayan Brew Café.\n          ", [1076, 1227], "\n            Don&lsquo;t just take our word for it. Here's what our wonderful customers have to say about their experience at Himalayan Brew Café.\n          ", [1076, 1227], "\n            Don&#39;t just take our word for it. Here's what our wonderful customers have to say about their experience at Himalayan Brew Café.\n          ", [1076, 1227], "\n            Don&rsquo;t just take our word for it. Here's what our wonderful customers have to say about their experience at Himalayan Brew Café.\n          ", [1076, 1227], "\n            Don't just take our word for it. Here&apos;s what our wonderful customers have to say about their experience at Himalayan Brew Café.\n          ", [1076, 1227], "\n            Don't just take our word for it. Here&lsquo;s what our wonderful customers have to say about their experience at Himalayan Brew Café.\n          ", [1076, 1227], "\n            Don't just take our word for it. Here&#39;s what our wonderful customers have to say about their experience at Himalayan Brew Café.\n          ", [1076, 1227], "\n            Don't just take our word for it. Here&rsquo;s what our wonderful customers have to say about their experience at Himalayan Brew Café.\n          ", "&quot;", [2891, 2917], "\n                        &quot;", "&ldquo;", [2891, 2917], "\n                        &ldquo;", "&#34;", [2891, 2917], "\n                        &#34;", "&rdquo;", [2891, 2917], "\n                        &rdquo;", [2938, 2962], "&quot;\n                      ", [2938, 2962], "&ldquo;\n                      ", [2938, 2962], "&#34;\n                      ", [2938, 2962], "&rdquo;\n                      "]