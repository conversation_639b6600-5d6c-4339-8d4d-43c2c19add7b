"use client";

import Link from "next/link";
import { Coffee, MapPin, Phone, Mail, Clock, Facebook, Instagram, Twitter } from "lucide-react";
import { cafeInfo } from "@/data/cafe-info";

export function Footer() {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-cafe-dark-brown text-cafe-cream">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Brand Section */}
          <div className="space-y-4">
            <Link href="/" className="flex items-center space-x-2">
              <Coffee className="h-8 w-8 text-cafe-gold" />
              <span className="font-serif font-bold text-xl">
                Himalayan Brew
              </span>
            </Link>
            <p className="text-cafe-beige leading-relaxed">
              {cafeInfo.description}
            </p>
            <div className="flex space-x-4">
              <a
                href={cafeInfo.socialMedia.facebook}
                target="_blank"
                rel="noopener noreferrer"
                className="w-10 h-10 bg-cafe-brown rounded-full flex items-center justify-center hover:bg-cafe-gold transition-colors duration-200"
              >
                <Facebook className="h-5 w-5" />
              </a>
              <a
                href={cafeInfo.socialMedia.instagram}
                target="_blank"
                rel="noopener noreferrer"
                className="w-10 h-10 bg-cafe-brown rounded-full flex items-center justify-center hover:bg-cafe-gold transition-colors duration-200"
              >
                <Instagram className="h-5 w-5" />
              </a>
              <a
                href={cafeInfo.socialMedia.twitter}
                target="_blank"
                rel="noopener noreferrer"
                className="w-10 h-10 bg-cafe-brown rounded-full flex items-center justify-center hover:bg-cafe-gold transition-colors duration-200"
              >
                <Twitter className="h-5 w-5" />
              </a>
            </div>
          </div>

          {/* Quick Links */}
          <div className="space-y-4">
            <h3 className="font-serif text-lg font-semibold text-cafe-gold">
              Quick Links
            </h3>
            <ul className="space-y-2">
              <li>
                <Link href="/" className="text-cafe-beige hover:text-cafe-gold transition-colors duration-200">
                  Home
                </Link>
              </li>
              <li>
                <Link href="/about" className="text-cafe-beige hover:text-cafe-gold transition-colors duration-200">
                  About Us
                </Link>
              </li>
              <li>
                <Link href="/menu" className="text-cafe-beige hover:text-cafe-gold transition-colors duration-200">
                  Menu
                </Link>
              </li>
              <li>
                <Link href="/gallery" className="text-cafe-beige hover:text-cafe-gold transition-colors duration-200">
                  Gallery
                </Link>
              </li>
              <li>
                <Link href="/testimonials" className="text-cafe-beige hover:text-cafe-gold transition-colors duration-200">
                  Testimonials
                </Link>
              </li>
              <li>
                <Link href="/contact" className="text-cafe-beige hover:text-cafe-gold transition-colors duration-200">
                  Contact
                </Link>
              </li>
            </ul>
          </div>

          {/* Contact Info */}
          <div className="space-y-4">
            <h3 className="font-serif text-lg font-semibold text-cafe-gold">
              Contact Info
            </h3>
            <ul className="space-y-3">
              <li className="flex items-start gap-3">
                <MapPin className="h-5 w-5 text-cafe-gold mt-0.5 flex-shrink-0" />
                <span className="text-cafe-beige">
                  {cafeInfo.contact.address}
                </span>
              </li>
              <li className="flex items-center gap-3">
                <Phone className="h-5 w-5 text-cafe-gold flex-shrink-0" />
                <a 
                  href={`tel:${cafeInfo.contact.phone}`}
                  className="text-cafe-beige hover:text-cafe-gold transition-colors duration-200"
                >
                  {cafeInfo.contact.phone}
                </a>
              </li>
              <li className="flex items-center gap-3">
                <Mail className="h-5 w-5 text-cafe-gold flex-shrink-0" />
                <a 
                  href={`mailto:${cafeInfo.contact.email}`}
                  className="text-cafe-beige hover:text-cafe-gold transition-colors duration-200"
                >
                  {cafeInfo.contact.email}
                </a>
              </li>
            </ul>
          </div>

          {/* Opening Hours */}
          <div className="space-y-4">
            <h3 className="font-serif text-lg font-semibold text-cafe-gold">
              Opening Hours
            </h3>
            <ul className="space-y-2">
              <li className="flex items-start gap-3">
                <Clock className="h-5 w-5 text-cafe-gold mt-0.5 flex-shrink-0" />
                <div className="text-cafe-beige">
                  <div className="font-medium">Monday - Friday</div>
                  <div className="text-sm">{cafeInfo.hours.weekdays}</div>
                </div>
              </li>
              <li className="flex items-start gap-3">
                <Clock className="h-5 w-5 text-cafe-gold mt-0.5 flex-shrink-0" />
                <div className="text-cafe-beige">
                  <div className="font-medium">Saturday - Sunday</div>
                  <div className="text-sm">{cafeInfo.hours.weekends}</div>
                </div>
              </li>
              <li className="flex items-start gap-3">
                <Clock className="h-5 w-5 text-cafe-gold mt-0.5 flex-shrink-0" />
                <div className="text-cafe-beige">
                  <div className="font-medium">Public Holidays</div>
                  <div className="text-sm">{cafeInfo.hours.holidays}</div>
                </div>
              </li>
            </ul>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-cafe-brown mt-12 pt-8 flex flex-col md:flex-row justify-between items-center">
          <p className="text-cafe-beige text-sm">
            © {currentYear} Himalayan Brew Café. All rights reserved.
          </p>
          <div className="flex space-x-6 mt-4 md:mt-0">
            <Link href="/privacy" className="text-cafe-beige hover:text-cafe-gold text-sm transition-colors duration-200">
              Privacy Policy
            </Link>
            <Link href="/terms" className="text-cafe-beige hover:text-cafe-gold text-sm transition-colors duration-200">
              Terms of Service
            </Link>
          </div>
        </div>
      </div>
    </footer>
  );
}
