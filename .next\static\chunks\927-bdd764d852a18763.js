"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[927],{221:(e,t,n)=>{n.d(t,{u:()=>h});var r=n(2177);let i=(e,t,n)=>{if(e&&"reportValidity"in e){let i=(0,r.Jt)(n,t);e.setCustomValidity(i&&i.message||""),e.reportValidity()}},o=(e,t)=>{for(let n in t.fields){let r=t.fields[n];r&&r.ref&&"reportValidity"in r.ref?i(r.ref,n,e):r&&r.refs&&r.refs.forEach(t=>i(t,n,e))}},s=(e,t)=>{t.shouldUseNativeValidation&&o(e,t);let n={};for(let i in e){let o=(0,r.Jt)(t.fields,i),s=Object.assign(e[i]||{},{ref:o&&o.ref});if(a(t.names||Object.keys(e),i)){let e=Object.assign({},(0,r.Jt)(n,i));(0,r.hZ)(e,"root",s),(0,r.hZ)(n,i,e)}else(0,r.hZ)(n,i,s)}return n},a=(e,t)=>{let n=l(t);return e.some(e=>l(e).match(`^${n}\\.\\d+`))};function l(e){return e.replace(/\]|\[/g,"")}var u=n(8753),c=n(3793);function d(e,t){try{var n=e()}catch(e){return t(e)}return n&&n.then?n.then(void 0,t):n}function h(e,t,n){if(void 0===n&&(n={}),"_def"in e&&"object"==typeof e._def&&"typeName"in e._def)return function(i,a,l){try{return Promise.resolve(d(function(){return Promise.resolve(e["sync"===n.mode?"parse":"parseAsync"](i,t)).then(function(e){return l.shouldUseNativeValidation&&o({},l),{errors:{},values:n.raw?Object.assign({},i):e}})},function(e){if(Array.isArray(null==e?void 0:e.issues))return{values:{},errors:s(function(e,t){for(var n={};e.length;){var i=e[0],o=i.code,s=i.message,a=i.path.join(".");if(!n[a])if("unionErrors"in i){var l=i.unionErrors[0].errors[0];n[a]={message:l.message,type:l.code}}else n[a]={message:s,type:o};if("unionErrors"in i&&i.unionErrors.forEach(function(t){return t.errors.forEach(function(t){return e.push(t)})}),t){var u=n[a].types,c=u&&u[i.code];n[a]=(0,r.Gb)(a,t,n,o,c?[].concat(c,i.message):i.message)}e.shift()}return n}(e.errors,!l.shouldUseNativeValidation&&"all"===l.criteriaMode),l)};throw e}))}catch(e){return Promise.reject(e)}};if("_zod"in e&&"object"==typeof e._zod)return function(i,a,l){try{return Promise.resolve(d(function(){return Promise.resolve(("sync"===n.mode?u.qg:u.EJ)(e,i,t)).then(function(e){return l.shouldUseNativeValidation&&o({},l),{errors:{},values:n.raw?Object.assign({},i):e}})},function(e){if(e instanceof c.a$)return{values:{},errors:s(function(e,t){for(var n={};e.length;){var i=e[0],o=i.code,s=i.message,a=i.path.join(".");if(!n[a])if("invalid_union"===i.code){var l=i.errors[0][0];n[a]={message:l.message,type:l.code}}else n[a]={message:s,type:o};if("invalid_union"===i.code&&i.errors.forEach(function(t){return t.forEach(function(t){return e.push(t)})}),t){var u=n[a].types,c=u&&u[i.code];n[a]=(0,r.Gb)(a,t,n,o,c?[].concat(c,i.message):i.message)}e.shift()}return n}(e.issues,!l.shouldUseNativeValidation&&"all"===l.criteriaMode),l)};throw e}))}catch(e){return Promise.reject(e)}};throw Error("Invalid input: not a Zod schema")}},224:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("quote",[["path",{d:"M16 3a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2 1 1 0 0 1 1 1v1a2 2 0 0 1-2 2 1 1 0 0 0-1 1v2a1 1 0 0 0 1 1 6 6 0 0 0 6-6V5a2 2 0 0 0-2-2z",key:"rib7q0"}],["path",{d:"M5 3a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2 1 1 0 0 1 1 1v1a2 2 0 0 1-2 2 1 1 0 0 0-1 1v2a1 1 0 0 0 1 1 6 6 0 0 0 6-6V5a2 2 0 0 0-2-2z",key:"1ymkrd"}]])},238:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("gift",[["rect",{x:"3",y:"8",width:"18",height:"4",rx:"1",key:"bkv52"}],["path",{d:"M12 8v13",key:"1c76mn"}],["path",{d:"M19 12v7a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2v-7",key:"6wjy6b"}],["path",{d:"M7.5 8a2.5 2.5 0 0 1 0-5A4.8 8 0 0 1 12 8a4.8 8 0 0 1 4.5-5 2.5 2.5 0 0 1 0 5",key:"1ihvrl"}]])},488:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("facebook",[["path",{d:"M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z",key:"1jg4f8"}]])},556:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("twitter",[["path",{d:"M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z",key:"pff0z6"}]])},646:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},760:(e,t,n)=>{n.d(t,{N:()=>y});var r=n(5155),i=n(2115),o=n(869),s=n(2885),a=n(7494),l=n(845),u=n(7351),c=n(1508);class d extends i.Component{getSnapshotBeforeUpdate(e){let t=this.props.childRef.current;if(t&&e.isPresent&&!this.props.isPresent){let e=t.offsetParent,n=(0,u.s)(e)&&e.offsetWidth||0,r=this.props.sizeRef.current;r.height=t.offsetHeight||0,r.width=t.offsetWidth||0,r.top=t.offsetTop,r.left=t.offsetLeft,r.right=n-r.width-r.left}return null}componentDidUpdate(){}render(){return this.props.children}}function h(e){let{children:t,isPresent:n,anchorX:o,root:s}=e,a=(0,i.useId)(),l=(0,i.useRef)(null),u=(0,i.useRef)({width:0,height:0,top:0,left:0,right:0}),{nonce:h}=(0,i.useContext)(c.Q);return(0,i.useInsertionEffect)(()=>{let{width:e,height:t,top:r,left:i,right:c}=u.current;if(n||!l.current||!e||!t)return;l.current.dataset.motionPopId=a;let d=document.createElement("style");h&&(d.nonce=h);let f=null!=s?s:document.head;return f.appendChild(d),d.sheet&&d.sheet.insertRule('\n          [data-motion-pop-id="'.concat(a,'"] {\n            position: absolute !important;\n            width: ').concat(e,"px !important;\n            height: ").concat(t,"px !important;\n            ").concat("left"===o?"left: ".concat(i):"right: ".concat(c),"px !important;\n            top: ").concat(r,"px !important;\n          }\n        ")),()=>{f.removeChild(d),f.contains(d)&&f.removeChild(d)}},[n]),(0,r.jsx)(d,{isPresent:n,childRef:l,sizeRef:u,children:i.cloneElement(t,{ref:l})})}let f=e=>{let{children:t,initial:n,isPresent:o,onExitComplete:a,custom:u,presenceAffectsLayout:c,mode:d,anchorX:f,root:m}=e,g=(0,s.M)(p),v=(0,i.useId)(),y=!0,b=(0,i.useMemo)(()=>(y=!1,{id:v,initial:n,isPresent:o,custom:u,onExitComplete:e=>{for(let t of(g.set(e,!0),g.values()))if(!t)return;a&&a()},register:e=>(g.set(e,!1),()=>g.delete(e))}),[o,g,a]);return c&&y&&(b={...b}),(0,i.useMemo)(()=>{g.forEach((e,t)=>g.set(t,!1))},[o]),i.useEffect(()=>{o||g.size||!a||a()},[o]),"popLayout"===d&&(t=(0,r.jsx)(h,{isPresent:o,anchorX:f,root:m,children:t})),(0,r.jsx)(l.t.Provider,{value:b,children:t})};function p(){return new Map}var m=n(2082);let g=e=>e.key||"";function v(e){let t=[];return i.Children.forEach(e,e=>{(0,i.isValidElement)(e)&&t.push(e)}),t}let y=e=>{let{children:t,custom:n,initial:l=!0,onExitComplete:u,presenceAffectsLayout:c=!0,mode:d="sync",propagate:h=!1,anchorX:p="left",root:y}=e,[b,x]=(0,m.xQ)(h),w=(0,i.useMemo)(()=>v(t),[t]),k=h&&!b?[]:w.map(g),A=(0,i.useRef)(!0),E=(0,i.useRef)(w),S=(0,s.M)(()=>new Map),[_,P]=(0,i.useState)(w),[T,M]=(0,i.useState)(w);(0,a.E)(()=>{A.current=!1,E.current=w;for(let e=0;e<T.length;e++){let t=g(T[e]);k.includes(t)?S.delete(t):!0!==S.get(t)&&S.set(t,!1)}},[T,k.length,k.join("-")]);let C=[];if(w!==_){let e=[...w];for(let t=0;t<T.length;t++){let n=T[t],r=g(n);k.includes(r)||(e.splice(t,0,n),C.push(n))}return"wait"===d&&C.length&&(e=C),M(v(e)),P(w),null}let{forceRender:D}=(0,i.useContext)(o.L);return(0,r.jsx)(r.Fragment,{children:T.map(e=>{let t=g(e),i=(!h||!!b)&&(w===T||k.includes(t));return(0,r.jsx)(f,{isPresent:i,initial:(!A.current||!!l)&&void 0,custom:n,presenceAffectsLayout:c,mode:d,root:y,onExitComplete:i?void 0:()=>{if(!S.has(t))return;S.set(t,!0);let e=!0;S.forEach(t=>{t||(e=!1)}),e&&(null==D||D(),M(E.current),h&&(null==x||x()),u&&u())},anchorX:p,children:e},t)})})}},845:(e,t,n)=>{n.d(t,{t:()=>r});let r=(0,n(2115).createContext)(null)},869:(e,t,n)=>{n.d(t,{L:()=>r});let r=(0,n(2115).createContext)({})},968:(e,t,n)=>{n.d(t,{b:()=>a});var r=n(2115),i=n(3655),o=n(5155),s=r.forwardRef((e,t)=>(0,o.jsx)(i.sG.label,{...e,ref:t,onMouseDown:t=>{var n;t.target.closest("button, input, select, textarea")||(null==(n=e.onMouseDown)||n.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));s.displayName="Label";var a=s},1508:(e,t,n)=>{n.d(t,{Q:()=>r});let r=(0,n(2115).createContext)({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"})},1976:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},2082:(e,t,n)=>{n.d(t,{xQ:()=>o});var r=n(2115),i=n(845);function o(e=!0){let t=(0,r.useContext)(i.t);if(null===t)return[!0,null];let{isPresent:n,onExitComplete:s,register:a}=t,l=(0,r.useId)();(0,r.useEffect)(()=>{if(e)return a(l)},[e]);let u=(0,r.useCallback)(()=>e&&s&&s(l),[l,s,e]);return!n&&s?[!1,u]:[!0]}},2085:(e,t,n)=>{n.d(t,{F:()=>s});var r=n(2596);let i=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,o=r.$,s=(e,t)=>n=>{var r;if((null==t?void 0:t.variants)==null)return o(e,null==n?void 0:n.class,null==n?void 0:n.className);let{variants:s,defaultVariants:a}=t,l=Object.keys(s).map(e=>{let t=null==n?void 0:n[e],r=null==a?void 0:a[e];if(null===t)return null;let o=i(t)||i(r);return s[e][o]}),u=n&&Object.entries(n).reduce((e,t)=>{let[n,r]=t;return void 0===r||(e[n]=r),e},{});return o(e,l,null==t||null==(r=t.compoundVariants)?void 0:r.reduce((e,t)=>{let{class:n,className:r,...i}=t;return Object.entries(i).every(e=>{let[t,n]=e;return Array.isArray(n)?n.includes({...a,...u}[t]):({...a,...u})[t]===n})?[...e,n,r]:e},[]),null==n?void 0:n.class,null==n?void 0:n.className)}},2138:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},2177:(e,t,n)=>{n.d(t,{Gb:()=>T,Jt:()=>m,Op:()=>k,hZ:()=>g,lN:()=>S,mN:()=>ea,xI:()=>P,xW:()=>w});var r=n(2115),i=e=>e instanceof Date,o=e=>null==e,s=e=>!o(e)&&!Array.isArray(e)&&"object"==typeof e&&!i(e),a=e=>s(e)&&e.target?"checkbox"===e.target.type?e.target.checked:e.target.value:e,l=(e,t)=>e.has((e=>e.substring(0,e.search(/\.\d+(\.|$)/))||e)(t)),u="undefined"!=typeof window&&void 0!==window.HTMLElement&&"undefined"!=typeof document;function c(e){let t,n=Array.isArray(e),r="undefined"!=typeof FileList&&e instanceof FileList;if(e instanceof Date)t=new Date(e);else if(!(!(u&&(e instanceof Blob||r))&&(n||s(e))))return e;else if(t=n?[]:{},n||(e=>{let t=e.constructor&&e.constructor.prototype;return s(t)&&t.hasOwnProperty("isPrototypeOf")})(e))for(let n in e)e.hasOwnProperty(n)&&(t[n]=c(e[n]));else t=e;return t}var d=e=>/^\w*$/.test(e),h=e=>void 0===e,f=e=>Array.isArray(e)?e.filter(Boolean):[],p=e=>f(e.replace(/["|']|\]/g,"").split(/\.|\[/)),m=(e,t,n)=>{if(!t||!s(e))return n;let r=(d(t)?[t]:p(t)).reduce((e,t)=>o(e)?e:e[t],e);return h(r)||r===e?h(e[t])?n:e[t]:r},g=(e,t,n)=>{let r=-1,i=d(t)?[t]:p(t),o=i.length,a=o-1;for(;++r<o;){let t=i[r],o=n;if(r!==a){let n=e[t];o=s(n)||Array.isArray(n)?n:isNaN(+i[r+1])?{}:[]}if("__proto__"===t||"constructor"===t||"prototype"===t)return;e[t]=o,e=e[t]}};let v={BLUR:"blur",FOCUS_OUT:"focusout",CHANGE:"change"},y={onBlur:"onBlur",onChange:"onChange",onSubmit:"onSubmit",onTouched:"onTouched",all:"all"},b={max:"max",min:"min",maxLength:"maxLength",minLength:"minLength",pattern:"pattern",required:"required",validate:"validate"},x=r.createContext(null);x.displayName="HookFormContext";let w=()=>r.useContext(x),k=e=>{let{children:t,...n}=e;return r.createElement(x.Provider,{value:n},t)};var A=(e,t,n,r=!0)=>{let i={defaultValues:t._defaultValues};for(let o in e)Object.defineProperty(i,o,{get:()=>(t._proxyFormState[o]!==y.all&&(t._proxyFormState[o]=!r||y.all),n&&(n[o]=!0),e[o])});return i};let E="undefined"!=typeof window?r.useLayoutEffect:r.useEffect;function S(e){let t=w(),{control:n=t.control,disabled:i,name:o,exact:s}=e||{},[a,l]=r.useState(n._formState),u=r.useRef({isDirty:!1,isLoading:!1,dirtyFields:!1,touchedFields:!1,validatingFields:!1,isValidating:!1,isValid:!1,errors:!1});return E(()=>n._subscribe({name:o,formState:u.current,exact:s,callback:e=>{i||l({...n._formState,...e})}}),[o,i,s]),r.useEffect(()=>{u.current.isValid&&n._setValid(!0)},[n]),r.useMemo(()=>A(a,n,u.current,!1),[a,n])}var _=(e,t,n,r,i)=>"string"==typeof e?(r&&t.watch.add(e),m(n,e,i)):Array.isArray(e)?e.map(e=>(r&&t.watch.add(e),m(n,e))):(r&&(t.watchAll=!0),n);let P=e=>e.render(function(e){let t=w(),{name:n,disabled:i,control:o=t.control,shouldUnregister:s}=e,u=l(o._names.array,n),d=function(e){let t=w(),{control:n=t.control,name:i,defaultValue:o,disabled:s,exact:a}=e||{},l=r.useRef(o),[u,c]=r.useState(n._getWatch(i,l.current));return E(()=>n._subscribe({name:i,formState:{values:!0},exact:a,callback:e=>!s&&c(_(i,n._names,e.values||n._formValues,!1,l.current))}),[i,n,s,a]),r.useEffect(()=>n._removeUnmounted()),u}({control:o,name:n,defaultValue:m(o._formValues,n,m(o._defaultValues,n,e.defaultValue)),exact:!0}),f=S({control:o,name:n,exact:!0}),p=r.useRef(e),y=r.useRef(o.register(n,{...e.rules,value:d,..."boolean"==typeof e.disabled?{disabled:e.disabled}:{}})),b=r.useMemo(()=>Object.defineProperties({},{invalid:{enumerable:!0,get:()=>!!m(f.errors,n)},isDirty:{enumerable:!0,get:()=>!!m(f.dirtyFields,n)},isTouched:{enumerable:!0,get:()=>!!m(f.touchedFields,n)},isValidating:{enumerable:!0,get:()=>!!m(f.validatingFields,n)},error:{enumerable:!0,get:()=>m(f.errors,n)}}),[f,n]),x=r.useCallback(e=>y.current.onChange({target:{value:a(e),name:n},type:v.CHANGE}),[n]),k=r.useCallback(()=>y.current.onBlur({target:{value:m(o._formValues,n),name:n},type:v.BLUR}),[n,o._formValues]),A=r.useCallback(e=>{let t=m(o._fields,n);t&&e&&(t._f.ref={focus:()=>e.focus&&e.focus(),select:()=>e.select&&e.select(),setCustomValidity:t=>e.setCustomValidity(t),reportValidity:()=>e.reportValidity()})},[o._fields,n]),P=r.useMemo(()=>({name:n,value:d,..."boolean"==typeof i||f.disabled?{disabled:f.disabled||i}:{},onChange:x,onBlur:k,ref:A}),[n,i,f.disabled,x,k,A,d]);return r.useEffect(()=>{let e=o._options.shouldUnregister||s;o.register(n,{...p.current.rules,..."boolean"==typeof p.current.disabled?{disabled:p.current.disabled}:{}});let t=(e,t)=>{let n=m(o._fields,e);n&&n._f&&(n._f.mount=t)};if(t(n,!0),e){let e=c(m(o._options.defaultValues,n));g(o._defaultValues,n,e),h(m(o._formValues,n))&&g(o._formValues,n,e)}return u||o.register(n),()=>{(u?e&&!o._state.action:e)?o.unregister(n):t(n,!1)}},[n,o,u,s]),r.useEffect(()=>{o._setDisabledField({disabled:i,name:n})},[i,n,o]),r.useMemo(()=>({field:P,formState:f,fieldState:b}),[P,f,b])}(e));var T=(e,t,n,r,i)=>t?{...n[e],types:{...n[e]&&n[e].types?n[e].types:{},[r]:i||!0}}:{},M=e=>Array.isArray(e)?e:[e],C=()=>{let e=[];return{get observers(){return e},next:t=>{for(let n of e)n.next&&n.next(t)},subscribe:t=>(e.push(t),{unsubscribe:()=>{e=e.filter(e=>e!==t)}}),unsubscribe:()=>{e=[]}}},D=e=>o(e)||"object"!=typeof e;function V(e,t,n=new WeakSet){if(D(e)||D(t))return e===t;if(i(e)&&i(t))return e.getTime()===t.getTime();let r=Object.keys(e),o=Object.keys(t);if(r.length!==o.length)return!1;if(n.has(e)||n.has(t))return!0;for(let a of(n.add(e),n.add(t),r)){let r=e[a];if(!o.includes(a))return!1;if("ref"!==a){let e=t[a];if(i(r)&&i(e)||s(r)&&s(e)||Array.isArray(r)&&Array.isArray(e)?!V(r,e,n):r!==e)return!1}}return!0}var z=e=>s(e)&&!Object.keys(e).length,I=e=>"function"==typeof e,j=e=>{if(!u)return!1;let t=e?e.ownerDocument:0;return e instanceof(t&&t.defaultView?t.defaultView.HTMLElement:HTMLElement)},R=e=>j(e)&&e.isConnected;function O(e,t){let n=Array.isArray(t)?t:d(t)?[t]:p(t),r=1===n.length?e:function(e,t){let n=t.slice(0,-1).length,r=0;for(;r<n;)e=h(e)?r++:e[t[r++]];return e}(e,n),i=n.length-1,o=n[i];return r&&delete r[o],0!==i&&(s(r)&&z(r)||Array.isArray(r)&&function(e){for(let t in e)if(e.hasOwnProperty(t)&&!h(e[t]))return!1;return!0}(r))&&O(e,n.slice(0,-1)),e}var F=e=>{for(let t in e)if(I(e[t]))return!0;return!1};function L(e,t={}){let n=Array.isArray(e);if(s(e)||n)for(let n in e)Array.isArray(e[n])||s(e[n])&&!F(e[n])?(t[n]=Array.isArray(e[n])?[]:{},L(e[n],t[n])):o(e[n])||(t[n]=!0);return t}var $=(e,t)=>(function e(t,n,r){let i=Array.isArray(t);if(s(t)||i)for(let i in t)Array.isArray(t[i])||s(t[i])&&!F(t[i])?h(n)||D(r[i])?r[i]=Array.isArray(t[i])?L(t[i],[]):{...L(t[i])}:e(t[i],o(n)?{}:n[i],r[i]):r[i]=!V(t[i],n[i]);return r})(e,t,L(t));let N={value:!1,isValid:!1},U={value:!0,isValid:!0};var B=e=>{if(Array.isArray(e)){if(e.length>1){let t=e.filter(e=>e&&e.checked&&!e.disabled).map(e=>e.value);return{value:t,isValid:!!t.length}}return e[0].checked&&!e[0].disabled?e[0].attributes&&!h(e[0].attributes.value)?h(e[0].value)||""===e[0].value?U:{value:e[0].value,isValid:!0}:U:N}return N},Z=(e,{valueAsNumber:t,valueAsDate:n,setValueAs:r})=>h(e)?e:t?""===e?NaN:e?+e:e:n&&"string"==typeof e?new Date(e):r?r(e):e;let W={isValid:!1,value:null};var H=e=>Array.isArray(e)?e.reduce((e,t)=>t&&t.checked&&!t.disabled?{isValid:!0,value:t.value}:e,W):W;function q(e){let t=e.ref;return"file"===t.type?t.files:"radio"===t.type?H(e.refs).value:"select-multiple"===t.type?[...t.selectedOptions].map(({value:e})=>e):"checkbox"===t.type?B(e.refs).value:Z(h(t.value)?e.ref.value:t.value,e)}var G=e=>h(e)?e:e instanceof RegExp?e.source:s(e)?e.value instanceof RegExp?e.value.source:e.value:e,K=e=>({isOnSubmit:!e||e===y.onSubmit,isOnBlur:e===y.onBlur,isOnChange:e===y.onChange,isOnAll:e===y.all,isOnTouch:e===y.onTouched});let X="AsyncFunction";var J=e=>!!e&&!!e.validate&&!!(I(e.validate)&&e.validate.constructor.name===X||s(e.validate)&&Object.values(e.validate).find(e=>e.constructor.name===X)),Y=(e,t,n)=>!n&&(t.watchAll||t.watch.has(e)||[...t.watch].some(t=>e.startsWith(t)&&/^\.\w+/.test(e.slice(t.length))));let Q=(e,t,n,r)=>{for(let i of n||Object.keys(e)){let n=m(e,i);if(n){let{_f:e,...o}=n;if(e){if(e.refs&&e.refs[0]&&t(e.refs[0],i)&&!r)return!0;else if(e.ref&&t(e.ref,e.name)&&!r)return!0;else if(Q(o,t))break}else if(s(o)&&Q(o,t))break}}};function ee(e,t,n){let r=m(e,n);if(r||d(n))return{error:r,name:n};let i=n.split(".");for(;i.length;){let r=i.join("."),o=m(t,r),s=m(e,r);if(o&&!Array.isArray(o)&&n!==r)break;if(s&&s.type)return{name:r,error:s};if(s&&s.root&&s.root.type)return{name:`${r}.root`,error:s.root};i.pop()}return{name:n}}var et=(e,t,n)=>{let r=M(m(e,n));return g(r,"root",t[n]),g(e,n,r),e},en=e=>"string"==typeof e;function er(e,t,n="validate"){if(en(e)||Array.isArray(e)&&e.every(en)||"boolean"==typeof e&&!e)return{type:n,message:en(e)?e:"",ref:t}}var ei=e=>!s(e)||e instanceof RegExp?{value:e,message:""}:e,eo=async(e,t,n,r,i,a)=>{let{ref:l,refs:u,required:c,maxLength:d,minLength:f,min:p,max:g,pattern:v,validate:y,name:x,valueAsNumber:w,mount:k}=e._f,A=m(n,x);if(!k||t.has(x))return{};let E=u?u[0]:l,S=e=>{i&&E.reportValidity&&(E.setCustomValidity("boolean"==typeof e?"":e||""),E.reportValidity())},_={},P="radio"===l.type,M="checkbox"===l.type,C=(w||"file"===l.type)&&h(l.value)&&h(A)||j(l)&&""===l.value||""===A||Array.isArray(A)&&!A.length,D=T.bind(null,x,r,_),V=(e,t,n,r=b.maxLength,i=b.minLength)=>{let o=e?t:n;_[x]={type:e?r:i,message:o,ref:l,...D(e?r:i,o)}};if(a?!Array.isArray(A)||!A.length:c&&(!(P||M)&&(C||o(A))||"boolean"==typeof A&&!A||M&&!B(u).isValid||P&&!H(u).isValid)){let{value:e,message:t}=en(c)?{value:!!c,message:c}:ei(c);if(e&&(_[x]={type:b.required,message:t,ref:E,...D(b.required,t)},!r))return S(t),_}if(!C&&(!o(p)||!o(g))){let e,t,n=ei(g),i=ei(p);if(o(A)||isNaN(A)){let r=l.valueAsDate||new Date(A),o=e=>new Date(new Date().toDateString()+" "+e),s="time"==l.type,a="week"==l.type;"string"==typeof n.value&&A&&(e=s?o(A)>o(n.value):a?A>n.value:r>new Date(n.value)),"string"==typeof i.value&&A&&(t=s?o(A)<o(i.value):a?A<i.value:r<new Date(i.value))}else{let r=l.valueAsNumber||(A?+A:A);o(n.value)||(e=r>n.value),o(i.value)||(t=r<i.value)}if((e||t)&&(V(!!e,n.message,i.message,b.max,b.min),!r))return S(_[x].message),_}if((d||f)&&!C&&("string"==typeof A||a&&Array.isArray(A))){let e=ei(d),t=ei(f),n=!o(e.value)&&A.length>+e.value,i=!o(t.value)&&A.length<+t.value;if((n||i)&&(V(n,e.message,t.message),!r))return S(_[x].message),_}if(v&&!C&&"string"==typeof A){let{value:e,message:t}=ei(v);if(e instanceof RegExp&&!A.match(e)&&(_[x]={type:b.pattern,message:t,ref:l,...D(b.pattern,t)},!r))return S(t),_}if(y){if(I(y)){let e=er(await y(A,n),E);if(e&&(_[x]={...e,...D(b.validate,e.message)},!r))return S(e.message),_}else if(s(y)){let e={};for(let t in y){if(!z(e)&&!r)break;let i=er(await y[t](A,n),E,t);i&&(e={...i,...D(t,i.message)},S(i.message),r&&(_[x]=e))}if(!z(e)&&(_[x]={ref:E,...e},!r))return _}}return S(!0),_};let es={mode:y.onSubmit,reValidateMode:y.onChange,shouldFocusError:!0};function ea(e={}){let t=r.useRef(void 0),n=r.useRef(void 0),[d,p]=r.useState({isDirty:!1,isValidating:!1,isLoading:I(e.defaultValues),isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,submitCount:0,dirtyFields:{},touchedFields:{},validatingFields:{},errors:e.errors||{},disabled:e.disabled||!1,isReady:!1,defaultValues:I(e.defaultValues)?void 0:e.defaultValues});if(!t.current)if(e.formControl)t.current={...e.formControl,formState:d},e.defaultValues&&!I(e.defaultValues)&&e.formControl.reset(e.defaultValues,e.resetOptions);else{let{formControl:n,...r}=function(e={}){let t,n={...es,...e},r={submitCount:0,isDirty:!1,isReady:!1,isLoading:I(n.defaultValues),isValidating:!1,isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,touchedFields:{},dirtyFields:{},validatingFields:{},errors:n.errors||{},disabled:n.disabled||!1},d={},p=(s(n.defaultValues)||s(n.values))&&c(n.defaultValues||n.values)||{},b=n.shouldUnregister?{}:c(p),x={action:!1,mount:!1,watch:!1},w={mount:new Set,disabled:new Set,unMount:new Set,array:new Set,watch:new Set},k=0,A={isDirty:!1,dirtyFields:!1,validatingFields:!1,touchedFields:!1,isValidating:!1,isValid:!1,errors:!1},E={...A},S={array:C(),state:C()},P=n.criteriaMode===y.all,T=async e=>{if(!n.disabled&&(A.isValid||E.isValid||e)){let e=n.resolver?z((await N()).errors):await B(d,!0);e!==r.isValid&&S.state.next({isValid:e})}},D=(e,t)=>{!n.disabled&&(A.isValidating||A.validatingFields||E.isValidating||E.validatingFields)&&((e||Array.from(w.mount)).forEach(e=>{e&&(t?g(r.validatingFields,e,t):O(r.validatingFields,e))}),S.state.next({validatingFields:r.validatingFields,isValidating:!z(r.validatingFields)}))},F=(e,t,n,r)=>{let i=m(d,e);if(i){let o=m(b,e,h(n)?m(p,e):n);h(o)||r&&r.defaultChecked||t?g(b,e,t?o:q(i._f)):X(e,o),x.mount&&T()}},L=(e,t,i,o,s)=>{let a=!1,l=!1,u={name:e};if(!n.disabled){if(!i||o){(A.isDirty||E.isDirty)&&(l=r.isDirty,r.isDirty=u.isDirty=W(),a=l!==u.isDirty);let n=V(m(p,e),t);l=!!m(r.dirtyFields,e),n?O(r.dirtyFields,e):g(r.dirtyFields,e,!0),u.dirtyFields=r.dirtyFields,a=a||(A.dirtyFields||E.dirtyFields)&&!n!==l}if(i){let t=m(r.touchedFields,e);t||(g(r.touchedFields,e,i),u.touchedFields=r.touchedFields,a=a||(A.touchedFields||E.touchedFields)&&t!==i)}a&&s&&S.state.next(u)}return a?u:{}},N=async e=>{D(e,!0);let t=await n.resolver(b,n.context,((e,t,n,r)=>{let i={};for(let n of e){let e=m(t,n);e&&g(i,n,e._f)}return{criteriaMode:n,names:[...e],fields:i,shouldUseNativeValidation:r}})(e||w.mount,d,n.criteriaMode,n.shouldUseNativeValidation));return D(e),t},U=async e=>{let{errors:t}=await N(e);if(e)for(let n of e){let e=m(t,n);e?g(r.errors,n,e):O(r.errors,n)}else r.errors=t;return t},B=async(e,t,i={valid:!0})=>{for(let o in e){let s=e[o];if(s){let{_f:e,...a}=s;if(e){let a=w.array.has(e.name),l=s._f&&J(s._f);l&&A.validatingFields&&D([o],!0);let u=await eo(s,w.disabled,b,P,n.shouldUseNativeValidation&&!t,a);if(l&&A.validatingFields&&D([o]),u[e.name]&&(i.valid=!1,t))break;t||(m(u,e.name)?a?et(r.errors,u,e.name):g(r.errors,e.name,u[e.name]):O(r.errors,e.name))}z(a)||await B(a,t,i)}}return i.valid},W=(e,t)=>!n.disabled&&(e&&t&&g(b,e,t),!V(eu(),p)),H=(e,t,n)=>_(e,w,{...x.mount?b:h(t)?p:"string"==typeof e?{[e]:t}:t},n,t),X=(e,t,n={})=>{let r=m(d,e),i=t;if(r){let n=r._f;n&&(n.disabled||g(b,e,Z(t,n)),i=j(n.ref)&&o(t)?"":t,"select-multiple"===n.ref.type?[...n.ref.options].forEach(e=>e.selected=i.includes(e.value)):n.refs?"checkbox"===n.ref.type?n.refs.forEach(e=>{e.defaultChecked&&e.disabled||(Array.isArray(i)?e.checked=!!i.find(t=>t===e.value):e.checked=i===e.value||!!i)}):n.refs.forEach(e=>e.checked=e.value===i):"file"===n.ref.type?n.ref.value="":(n.ref.value=i,n.ref.type||S.state.next({name:e,values:c(b)})))}(n.shouldDirty||n.shouldTouch)&&L(e,i,n.shouldTouch,n.shouldDirty,!0),n.shouldValidate&&el(e)},en=(e,t,n)=>{for(let r in t){if(!t.hasOwnProperty(r))return;let o=t[r],a=e+"."+r,l=m(d,a);(w.array.has(e)||s(o)||l&&!l._f)&&!i(o)?en(a,o,n):X(a,o,n)}},er=(e,t,n={})=>{let i=m(d,e),s=w.array.has(e),a=c(t);g(b,e,a),s?(S.array.next({name:e,values:c(b)}),(A.isDirty||A.dirtyFields||E.isDirty||E.dirtyFields)&&n.shouldDirty&&S.state.next({name:e,dirtyFields:$(p,b),isDirty:W(e,a)})):!i||i._f||o(a)?X(e,a,n):en(e,a,n),Y(e,w)&&S.state.next({...r}),S.state.next({name:x.mount?e:void 0,values:c(b)})},ei=async e=>{x.mount=!0;let o=e.target,s=o.name,l=!0,u=m(d,s),h=e=>{l=Number.isNaN(e)||i(e)&&isNaN(e.getTime())||V(e,m(b,s,e))},f=K(n.mode),p=K(n.reValidateMode);if(u){let i,x,$,U=o.type?q(u._f):a(e),Z=e.type===v.BLUR||e.type===v.FOCUS_OUT,W=!(($=u._f).mount&&($.required||$.min||$.max||$.maxLength||$.minLength||$.pattern||$.validate))&&!n.resolver&&!m(r.errors,s)&&!u._f.deps||(y=Z,_=m(r.touchedFields,s),M=r.isSubmitted,C=p,!(I=f).isOnAll&&(!M&&I.isOnTouch?!(_||y):(M?C.isOnBlur:I.isOnBlur)?!y:(M?!C.isOnChange:!I.isOnChange)||y)),H=Y(s,w,Z);g(b,s,U),Z?(u._f.onBlur&&u._f.onBlur(e),t&&t(0)):u._f.onChange&&u._f.onChange(e);let G=L(s,U,Z),K=!z(G)||H;if(Z||S.state.next({name:s,type:e.type,values:c(b)}),W)return(A.isValid||E.isValid)&&("onBlur"===n.mode?Z&&T():Z||T()),K&&S.state.next({name:s,...H?{}:G});if(!Z&&H&&S.state.next({...r}),n.resolver){let{errors:e}=await N([s]);if(h(U),l){let t=ee(r.errors,d,s),n=ee(e,d,t.name||s);i=n.error,s=n.name,x=z(e)}}else D([s],!0),i=(await eo(u,w.disabled,b,P,n.shouldUseNativeValidation))[s],D([s]),h(U),l&&(i?x=!1:(A.isValid||E.isValid)&&(x=await B(d,!0)));if(l){u._f.deps&&el(u._f.deps);var y,_,M,C,I,j=s,R=x,F=i;let e=m(r.errors,j),o=(A.isValid||E.isValid)&&"boolean"==typeof R&&r.isValid!==R;if(n.delayError&&F){let e;e=()=>{g(r.errors,j,F),S.state.next({errors:r.errors})},(t=t=>{clearTimeout(k),k=setTimeout(e,t)})(n.delayError)}else clearTimeout(k),t=null,F?g(r.errors,j,F):O(r.errors,j);if((F?!V(e,F):e)||!z(G)||o){let e={...G,...o&&"boolean"==typeof R?{isValid:R}:{},errors:r.errors,name:j};r={...r,...e},S.state.next(e)}}}},ea=(e,t)=>{if(m(r.errors,t)&&e.focus)return e.focus(),1},el=async(e,t={})=>{let i,o,s=M(e);if(n.resolver){let t=await U(h(e)?e:s);i=z(t),o=e?!s.some(e=>m(t,e)):i}else e?((o=(await Promise.all(s.map(async e=>{let t=m(d,e);return await B(t&&t._f?{[e]:t}:t)}))).every(Boolean))||r.isValid)&&T():o=i=await B(d);return S.state.next({..."string"!=typeof e||(A.isValid||E.isValid)&&i!==r.isValid?{}:{name:e},...n.resolver||!e?{isValid:i}:{},errors:r.errors}),t.shouldFocus&&!o&&Q(d,ea,e?s:w.mount),o},eu=e=>{let t={...x.mount?b:p};return h(e)?t:"string"==typeof e?m(t,e):e.map(e=>m(t,e))},ec=(e,t)=>({invalid:!!m((t||r).errors,e),isDirty:!!m((t||r).dirtyFields,e),error:m((t||r).errors,e),isValidating:!!m(r.validatingFields,e),isTouched:!!m((t||r).touchedFields,e)}),ed=(e,t,n)=>{let i=(m(d,e,{_f:{}})._f||{}).ref,{ref:o,message:s,type:a,...l}=m(r.errors,e)||{};g(r.errors,e,{...l,...t,ref:i}),S.state.next({name:e,errors:r.errors,isValid:!1}),n&&n.shouldFocus&&i&&i.focus&&i.focus()},eh=e=>S.state.subscribe({next:t=>{let n,i,o;n=e.name,i=t.name,o=e.exact,(!n||!i||n===i||M(n).some(e=>e&&(o?e===i:e.startsWith(i)||i.startsWith(e))))&&((e,t,n,r)=>{n(e);let{name:i,...o}=e;return z(o)||Object.keys(o).length>=Object.keys(t).length||Object.keys(o).find(e=>t[e]===(!r||y.all))})(t,e.formState||A,ex,e.reRenderRoot)&&e.callback({values:{...b},...r,...t})}}).unsubscribe,ef=(e,t={})=>{for(let i of e?M(e):w.mount)w.mount.delete(i),w.array.delete(i),t.keepValue||(O(d,i),O(b,i)),t.keepError||O(r.errors,i),t.keepDirty||O(r.dirtyFields,i),t.keepTouched||O(r.touchedFields,i),t.keepIsValidating||O(r.validatingFields,i),n.shouldUnregister||t.keepDefaultValue||O(p,i);S.state.next({values:c(b)}),S.state.next({...r,...!t.keepDirty?{}:{isDirty:W()}}),t.keepIsValid||T()},ep=({disabled:e,name:t})=>{("boolean"==typeof e&&x.mount||e||w.disabled.has(t))&&(e?w.disabled.add(t):w.disabled.delete(t))},em=(e,t={})=>{let r=m(d,e),i="boolean"==typeof t.disabled||"boolean"==typeof n.disabled;return(g(d,e,{...r||{},_f:{...r&&r._f?r._f:{ref:{name:e}},name:e,mount:!0,...t}}),w.mount.add(e),r)?ep({disabled:"boolean"==typeof t.disabled?t.disabled:n.disabled,name:e}):F(e,!0,t.value),{...i?{disabled:t.disabled||n.disabled}:{},...n.progressive?{required:!!t.required,min:G(t.min),max:G(t.max),minLength:G(t.minLength),maxLength:G(t.maxLength),pattern:G(t.pattern)}:{},name:e,onChange:ei,onBlur:ei,ref:i=>{if(i){let n;em(e,t),r=m(d,e);let o=h(i.value)&&i.querySelectorAll&&i.querySelectorAll("input,select,textarea")[0]||i,s="radio"===(n=o).type||"checkbox"===n.type,a=r._f.refs||[];(s?a.find(e=>e===o):o===r._f.ref)||(g(d,e,{_f:{...r._f,...s?{refs:[...a.filter(R),o,...Array.isArray(m(p,e))?[{}]:[]],ref:{type:o.type,name:e}}:{ref:o}}}),F(e,!1,void 0,o))}else(r=m(d,e,{}))._f&&(r._f.mount=!1),(n.shouldUnregister||t.shouldUnregister)&&!(l(w.array,e)&&x.action)&&w.unMount.add(e)}}},eg=()=>n.shouldFocusError&&Q(d,ea,w.mount),ev=(e,t)=>async i=>{let o;i&&(i.preventDefault&&i.preventDefault(),i.persist&&i.persist());let s=c(b);if(S.state.next({isSubmitting:!0}),n.resolver){let{errors:e,values:t}=await N();r.errors=e,s=c(t)}else await B(d);if(w.disabled.size)for(let e of w.disabled)O(s,e);if(O(r.errors,"root"),z(r.errors)){S.state.next({errors:{}});try{await e(s,i)}catch(e){o=e}}else t&&await t({...r.errors},i),eg(),setTimeout(eg);if(S.state.next({isSubmitted:!0,isSubmitting:!1,isSubmitSuccessful:z(r.errors)&&!o,submitCount:r.submitCount+1,errors:r.errors}),o)throw o},ey=(e,t={})=>{let i=e?c(e):p,o=c(i),s=z(e),a=s?p:o;if(t.keepDefaultValues||(p=i),!t.keepValues){if(t.keepDirtyValues)for(let e of Array.from(new Set([...w.mount,...Object.keys($(p,b))])))m(r.dirtyFields,e)?g(a,e,m(b,e)):er(e,m(a,e));else{if(u&&h(e))for(let e of w.mount){let t=m(d,e);if(t&&t._f){let e=Array.isArray(t._f.refs)?t._f.refs[0]:t._f.ref;if(j(e)){let t=e.closest("form");if(t){t.reset();break}}}}if(t.keepFieldsRef)for(let e of w.mount)er(e,m(a,e));else d={}}b=n.shouldUnregister?t.keepDefaultValues?c(p):{}:c(a),S.array.next({values:{...a}}),S.state.next({values:{...a}})}w={mount:t.keepDirtyValues?w.mount:new Set,unMount:new Set,array:new Set,disabled:new Set,watch:new Set,watchAll:!1,focus:""},x.mount=!A.isValid||!!t.keepIsValid||!!t.keepDirtyValues,x.watch=!!n.shouldUnregister,S.state.next({submitCount:t.keepSubmitCount?r.submitCount:0,isDirty:!s&&(t.keepDirty?r.isDirty:!!(t.keepDefaultValues&&!V(e,p))),isSubmitted:!!t.keepIsSubmitted&&r.isSubmitted,dirtyFields:s?{}:t.keepDirtyValues?t.keepDefaultValues&&b?$(p,b):r.dirtyFields:t.keepDefaultValues&&e?$(p,e):t.keepDirty?r.dirtyFields:{},touchedFields:t.keepTouched?r.touchedFields:{},errors:t.keepErrors?r.errors:{},isSubmitSuccessful:!!t.keepIsSubmitSuccessful&&r.isSubmitSuccessful,isSubmitting:!1})},eb=(e,t)=>ey(I(e)?e(b):e,t),ex=e=>{r={...r,...e}},ew={control:{register:em,unregister:ef,getFieldState:ec,handleSubmit:ev,setError:ed,_subscribe:eh,_runSchema:N,_focusError:eg,_getWatch:H,_getDirty:W,_setValid:T,_setFieldArray:(e,t=[],i,o,s=!0,a=!0)=>{if(o&&i&&!n.disabled){if(x.action=!0,a&&Array.isArray(m(d,e))){let t=i(m(d,e),o.argA,o.argB);s&&g(d,e,t)}if(a&&Array.isArray(m(r.errors,e))){let t,n=i(m(r.errors,e),o.argA,o.argB);s&&g(r.errors,e,n),f(m(t=r.errors,e)).length||O(t,e)}if((A.touchedFields||E.touchedFields)&&a&&Array.isArray(m(r.touchedFields,e))){let t=i(m(r.touchedFields,e),o.argA,o.argB);s&&g(r.touchedFields,e,t)}(A.dirtyFields||E.dirtyFields)&&(r.dirtyFields=$(p,b)),S.state.next({name:e,isDirty:W(e,t),dirtyFields:r.dirtyFields,errors:r.errors,isValid:r.isValid})}else g(b,e,t)},_setDisabledField:ep,_setErrors:e=>{r.errors=e,S.state.next({errors:r.errors,isValid:!1})},_getFieldArray:e=>f(m(x.mount?b:p,e,n.shouldUnregister?m(p,e,[]):[])),_reset:ey,_resetDefaultValues:()=>I(n.defaultValues)&&n.defaultValues().then(e=>{eb(e,n.resetOptions),S.state.next({isLoading:!1})}),_removeUnmounted:()=>{for(let e of w.unMount){let t=m(d,e);t&&(t._f.refs?t._f.refs.every(e=>!R(e)):!R(t._f.ref))&&ef(e)}w.unMount=new Set},_disableForm:e=>{"boolean"==typeof e&&(S.state.next({disabled:e}),Q(d,(t,n)=>{let r=m(d,n);r&&(t.disabled=r._f.disabled||e,Array.isArray(r._f.refs)&&r._f.refs.forEach(t=>{t.disabled=r._f.disabled||e}))},0,!1))},_subjects:S,_proxyFormState:A,get _fields(){return d},get _formValues(){return b},get _state(){return x},set _state(value){x=value},get _defaultValues(){return p},get _names(){return w},set _names(value){w=value},get _formState(){return r},get _options(){return n},set _options(value){n={...n,...value}}},subscribe:e=>(x.mount=!0,E={...E,...e.formState},eh({...e,formState:E})),trigger:el,register:em,handleSubmit:ev,watch:(e,t)=>I(e)?S.state.subscribe({next:n=>e(H(void 0,t),n)}):H(e,t,!0),setValue:er,getValues:eu,reset:eb,resetField:(e,t={})=>{m(d,e)&&(h(t.defaultValue)?er(e,c(m(p,e))):(er(e,t.defaultValue),g(p,e,c(t.defaultValue))),t.keepTouched||O(r.touchedFields,e),t.keepDirty||(O(r.dirtyFields,e),r.isDirty=t.defaultValue?W(e,c(m(p,e))):W()),!t.keepError&&(O(r.errors,e),A.isValid&&T()),S.state.next({...r}))},clearErrors:e=>{e&&M(e).forEach(e=>O(r.errors,e)),S.state.next({errors:e?r.errors:{}})},unregister:ef,setError:ed,setFocus:(e,t={})=>{let n=m(d,e),r=n&&n._f;if(r){let e=r.refs?r.refs[0]:r.ref;e.focus&&(e.focus(),t.shouldSelect&&I(e.select)&&e.select())}},getFieldState:ec};return{...ew,formControl:ew}}(e);t.current={...r,formState:d}}let b=t.current.control;return b._options=e,E(()=>{let e=b._subscribe({formState:b._proxyFormState,callback:()=>p({...b._formState}),reRenderRoot:!0});return p(e=>({...e,isReady:!0})),b._formState.isReady=!0,e},[b]),r.useEffect(()=>b._disableForm(e.disabled),[b,e.disabled]),r.useEffect(()=>{e.mode&&(b._options.mode=e.mode),e.reValidateMode&&(b._options.reValidateMode=e.reValidateMode)},[b,e.mode,e.reValidateMode]),r.useEffect(()=>{e.errors&&(b._setErrors(e.errors),b._focusError())},[b,e.errors]),r.useEffect(()=>{e.shouldUnregister&&b._subjects.state.next({values:b._getWatch()})},[b,e.shouldUnregister]),r.useEffect(()=>{if(b._proxyFormState.isDirty){let e=b._getDirty();e!==d.isDirty&&b._subjects.state.next({isDirty:e})}},[b,d.isDirty]),r.useEffect(()=>{e.values&&!V(e.values,n.current)?(b._reset(e.values,{keepFieldsRef:!0,...b._options.resetOptions}),n.current=e.values,p(e=>({...e}))):b._resetDefaultValues()},[b,e.values]),r.useEffect(()=>{b._state.mount||(b._setValid(),b._state.mount=!0),b._state.watch&&(b._state.watch=!1,b._subjects.state.next({...b._formState})),b._removeUnmounted()}),t.current.formState=A(d,b),t.current}},2486:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]])},2596:(e,t,n)=>{n.d(t,{$:()=>r});function r(){for(var e,t,n=0,r="",i=arguments.length;n<i;n++)(e=arguments[n])&&(t=function e(t){var n,r,i="";if("string"==typeof t||"number"==typeof t)i+=t;else if("object"==typeof t)if(Array.isArray(t)){var o=t.length;for(n=0;n<o;n++)t[n]&&(r=e(t[n]))&&(i&&(i+=" "),i+=r)}else for(r in t)t[r]&&(i&&(i+=" "),i+=r);return i}(e))&&(r&&(r+=" "),r+=t);return r}},2605:(e,t,n)=>{let r;n.d(t,{P:()=>of});var i=n(2115);let o=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],s=new Set(o),a=e=>180*e/Math.PI,l=e=>c(a(Math.atan2(e[1],e[0]))),u={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:e=>(Math.abs(e[0])+Math.abs(e[3]))/2,rotate:l,rotateZ:l,skewX:e=>a(Math.atan(e[1])),skewY:e=>a(Math.atan(e[2])),skew:e=>(Math.abs(e[1])+Math.abs(e[2]))/2},c=e=>((e%=360)<0&&(e+=360),e),d=e=>Math.sqrt(e[0]*e[0]+e[1]*e[1]),h=e=>Math.sqrt(e[4]*e[4]+e[5]*e[5]),f={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:d,scaleY:h,scale:e=>(d(e)+h(e))/2,rotateX:e=>c(a(Math.atan2(e[6],e[5]))),rotateY:e=>c(a(Math.atan2(-e[2],e[0]))),rotateZ:l,rotate:l,skewX:e=>a(Math.atan(e[4])),skewY:e=>a(Math.atan(e[1])),skew:e=>(Math.abs(e[1])+Math.abs(e[4]))/2};function p(e){return+!!e.includes("scale")}function m(e,t){let n,r;if(!e||"none"===e)return p(t);let i=e.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);if(i)n=f,r=i;else{let t=e.match(/^matrix\(([-\d.e\s,]+)\)$/u);n=u,r=t}if(!r)return p(t);let o=n[t],s=r[1].split(",").map(g);return"function"==typeof o?o(s):s[o]}function g(e){return parseFloat(e.trim())}let v=e=>t=>"string"==typeof t&&t.startsWith(e),y=v("--"),b=v("var(--"),x=e=>!!b(e)&&w.test(e.split("/*")[0].trim()),w=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu;function k({top:e,left:t,right:n,bottom:r}){return{x:{min:t,max:n},y:{min:e,max:r}}}let A=(e,t,n)=>e+(t-e)*n;function E(e){return void 0===e||1===e}function S({scale:e,scaleX:t,scaleY:n}){return!E(e)||!E(t)||!E(n)}function _(e){return S(e)||P(e)||e.z||e.rotate||e.rotateX||e.rotateY||e.skewX||e.skewY}function P(e){var t,n;return(t=e.x)&&"0%"!==t||(n=e.y)&&"0%"!==n}function T(e,t,n,r,i){return void 0!==i&&(e=r+i*(e-r)),r+n*(e-r)+t}function M(e,t=0,n=1,r,i){e.min=T(e.min,t,n,r,i),e.max=T(e.max,t,n,r,i)}function C(e,{x:t,y:n}){M(e.x,t.translate,t.scale,t.originPoint),M(e.y,n.translate,n.scale,n.originPoint)}function D(e,t){e.min=e.min+t,e.max=e.max+t}function V(e,t,n,r,i=.5){let o=A(e.min,e.max,i);M(e,t,n,o,r)}function z(e,t){V(e.x,t.x,t.scaleX,t.scale,t.originX),V(e.y,t.y,t.scaleY,t.scale,t.originY)}function I(e,t){return k(function(e,t){if(!t)return e;let n=t({x:e.left,y:e.top}),r=t({x:e.right,y:e.bottom});return{top:n.y,left:n.x,bottom:r.y,right:r.x}}(e.getBoundingClientRect(),t))}let j=new Set(["width","height","top","left","right","bottom",...o]),R=(e,t,n)=>n>t?t:n<e?e:n,O={test:e=>"number"==typeof e,parse:parseFloat,transform:e=>e},F={...O,transform:e=>R(0,1,e)},L={...O,default:1},$=e=>({test:t=>"string"==typeof t&&t.endsWith(e)&&1===t.split(" ").length,parse:parseFloat,transform:t=>`${t}${e}`}),N=$("deg"),U=$("%"),B=$("px"),Z=$("vh"),W=$("vw"),H={...U,parse:e=>U.parse(e)/100,transform:e=>U.transform(100*e)},q=e=>t=>t.test(e),G=[O,B,U,N,W,Z,{test:e=>"auto"===e,parse:e=>e}],K=e=>G.find(q(e)),X=()=>{},J=()=>{},Y=e=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(e),Q=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u,ee=e=>e===O||e===B,et=new Set(["x","y","z"]),en=o.filter(e=>!et.has(e)),er={width:({x:e},{paddingLeft:t="0",paddingRight:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),height:({y:e},{paddingTop:t="0",paddingBottom:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),top:(e,{top:t})=>parseFloat(t),left:(e,{left:t})=>parseFloat(t),bottom:({y:e},{top:t})=>parseFloat(t)+(e.max-e.min),right:({x:e},{left:t})=>parseFloat(t)+(e.max-e.min),x:(e,{transform:t})=>m(t,"x"),y:(e,{transform:t})=>m(t,"y")};er.translateX=er.x,er.translateY=er.y;let ei=e=>e,eo={},es=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],ea={value:null,addProjectionMetrics:null};function el(e,t){let n=!1,r=!0,i={delta:0,timestamp:0,isProcessing:!1},o=()=>n=!0,s=es.reduce((e,n)=>(e[n]=function(e,t){let n=new Set,r=new Set,i=!1,o=!1,s=new WeakSet,a={delta:0,timestamp:0,isProcessing:!1},l=0;function u(t){s.has(t)&&(c.schedule(t),e()),l++,t(a)}let c={schedule:(e,t=!1,o=!1)=>{let a=o&&i?n:r;return t&&s.add(e),a.has(e)||a.add(e),e},cancel:e=>{r.delete(e),s.delete(e)},process:e=>{if(a=e,i){o=!0;return}i=!0,[n,r]=[r,n],n.forEach(u),t&&ea.value&&ea.value.frameloop[t].push(l),l=0,n.clear(),i=!1,o&&(o=!1,c.process(e))}};return c}(o,t?n:void 0),e),{}),{setup:a,read:l,resolveKeyframes:u,preUpdate:c,update:d,preRender:h,render:f,postRender:p}=s,m=()=>{let o=eo.useManualTiming?i.timestamp:performance.now();n=!1,eo.useManualTiming||(i.delta=r?1e3/60:Math.max(Math.min(o-i.timestamp,40),1)),i.timestamp=o,i.isProcessing=!0,a.process(i),l.process(i),u.process(i),c.process(i),d.process(i),h.process(i),f.process(i),p.process(i),i.isProcessing=!1,n&&t&&(r=!1,e(m))};return{schedule:es.reduce((t,o)=>{let a=s[o];return t[o]=(t,o=!1,s=!1)=>(!n&&(n=!0,r=!0,i.isProcessing||e(m)),a.schedule(t,o,s)),t},{}),cancel:e=>{for(let t=0;t<es.length;t++)s[es[t]].cancel(e)},state:i,steps:s}}let{schedule:eu,cancel:ec,state:ed,steps:eh}=el("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:ei,!0),ef=new Set,ep=!1,em=!1,eg=!1;function ev(){if(em){let e=Array.from(ef).filter(e=>e.needsMeasurement),t=new Set(e.map(e=>e.element)),n=new Map;t.forEach(e=>{let t=function(e){let t=[];return en.forEach(n=>{let r=e.getValue(n);void 0!==r&&(t.push([n,r.get()]),r.set(+!!n.startsWith("scale")))}),t}(e);t.length&&(n.set(e,t),e.render())}),e.forEach(e=>e.measureInitialState()),t.forEach(e=>{e.render();let t=n.get(e);t&&t.forEach(([t,n])=>{e.getValue(t)?.set(n)})}),e.forEach(e=>e.measureEndState()),e.forEach(e=>{void 0!==e.suspendedScrollY&&window.scrollTo(0,e.suspendedScrollY)})}em=!1,ep=!1,ef.forEach(e=>e.complete(eg)),ef.clear()}function ey(){ef.forEach(e=>{e.readKeyframes(),e.needsMeasurement&&(em=!0)})}class eb{constructor(e,t,n,r,i,o=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...e],this.onComplete=t,this.name=n,this.motionValue=r,this.element=i,this.isAsync=o}scheduleResolve(){this.state="scheduled",this.isAsync?(ef.add(this),ep||(ep=!0,eu.read(ey),eu.resolveKeyframes(ev))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:e,name:t,element:n,motionValue:r}=this;if(null===e[0]){let i=r?.get(),o=e[e.length-1];if(void 0!==i)e[0]=i;else if(n&&t){let r=n.readValue(t,o);null!=r&&(e[0]=r)}void 0===e[0]&&(e[0]=o),r&&void 0===i&&r.set(e[0])}for(let t=1;t<e.length;t++)e[t]??(e[t]=e[t-1])}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(e=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,e),ef.delete(this)}cancel(){"scheduled"===this.state&&(ef.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}}let ex=e=>/^0[^.\s]+$/u.test(e),ew=e=>Math.round(1e5*e)/1e5,ek=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu,eA=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,eE=(e,t)=>n=>!!("string"==typeof n&&eA.test(n)&&n.startsWith(e)||t&&null!=n&&Object.prototype.hasOwnProperty.call(n,t)),eS=(e,t,n)=>r=>{if("string"!=typeof r)return r;let[i,o,s,a]=r.match(ek);return{[e]:parseFloat(i),[t]:parseFloat(o),[n]:parseFloat(s),alpha:void 0!==a?parseFloat(a):1}},e_={...O,transform:e=>Math.round(R(0,255,e))},eP={test:eE("rgb","red"),parse:eS("red","green","blue"),transform:({red:e,green:t,blue:n,alpha:r=1})=>"rgba("+e_.transform(e)+", "+e_.transform(t)+", "+e_.transform(n)+", "+ew(F.transform(r))+")"},eT={test:eE("#"),parse:function(e){let t="",n="",r="",i="";return e.length>5?(t=e.substring(1,3),n=e.substring(3,5),r=e.substring(5,7),i=e.substring(7,9)):(t=e.substring(1,2),n=e.substring(2,3),r=e.substring(3,4),i=e.substring(4,5),t+=t,n+=n,r+=r,i+=i),{red:parseInt(t,16),green:parseInt(n,16),blue:parseInt(r,16),alpha:i?parseInt(i,16)/255:1}},transform:eP.transform},eM={test:eE("hsl","hue"),parse:eS("hue","saturation","lightness"),transform:({hue:e,saturation:t,lightness:n,alpha:r=1})=>"hsla("+Math.round(e)+", "+U.transform(ew(t))+", "+U.transform(ew(n))+", "+ew(F.transform(r))+")"},eC={test:e=>eP.test(e)||eT.test(e)||eM.test(e),parse:e=>eP.test(e)?eP.parse(e):eM.test(e)?eM.parse(e):eT.parse(e),transform:e=>"string"==typeof e?e:e.hasOwnProperty("red")?eP.transform(e):eM.transform(e),getAnimatableNone:e=>{let t=eC.parse(e);return t.alpha=0,eC.transform(t)}},eD=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu,eV="number",ez="color",eI=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function ej(e){let t=e.toString(),n=[],r={color:[],number:[],var:[]},i=[],o=0,s=t.replace(eI,e=>(eC.test(e)?(r.color.push(o),i.push(ez),n.push(eC.parse(e))):e.startsWith("var(")?(r.var.push(o),i.push("var"),n.push(e)):(r.number.push(o),i.push(eV),n.push(parseFloat(e))),++o,"${}")).split("${}");return{values:n,split:s,indexes:r,types:i}}function eR(e){return ej(e).values}function eO(e){let{split:t,types:n}=ej(e),r=t.length;return e=>{let i="";for(let o=0;o<r;o++)if(i+=t[o],void 0!==e[o]){let t=n[o];t===eV?i+=ew(e[o]):t===ez?i+=eC.transform(e[o]):i+=e[o]}return i}}let eF=e=>"number"==typeof e?0:eC.test(e)?eC.getAnimatableNone(e):e,eL={test:function(e){return isNaN(e)&&"string"==typeof e&&(e.match(ek)?.length||0)+(e.match(eD)?.length||0)>0},parse:eR,createTransformer:eO,getAnimatableNone:function(e){let t=eR(e);return eO(e)(t.map(eF))}},e$=new Set(["brightness","contrast","saturate","opacity"]);function eN(e){let[t,n]=e.slice(0,-1).split("(");if("drop-shadow"===t)return e;let[r]=n.match(ek)||[];if(!r)return e;let i=n.replace(r,""),o=+!!e$.has(t);return r!==n&&(o*=100),t+"("+o+i+")"}let eU=/\b([a-z-]*)\(.*?\)/gu,eB={...eL,getAnimatableNone:e=>{let t=e.match(eU);return t?t.map(eN).join(" "):e}},eZ={...O,transform:Math.round},eW={borderWidth:B,borderTopWidth:B,borderRightWidth:B,borderBottomWidth:B,borderLeftWidth:B,borderRadius:B,radius:B,borderTopLeftRadius:B,borderTopRightRadius:B,borderBottomRightRadius:B,borderBottomLeftRadius:B,width:B,maxWidth:B,height:B,maxHeight:B,top:B,right:B,bottom:B,left:B,padding:B,paddingTop:B,paddingRight:B,paddingBottom:B,paddingLeft:B,margin:B,marginTop:B,marginRight:B,marginBottom:B,marginLeft:B,backgroundPositionX:B,backgroundPositionY:B,rotate:N,rotateX:N,rotateY:N,rotateZ:N,scale:L,scaleX:L,scaleY:L,scaleZ:L,skew:N,skewX:N,skewY:N,distance:B,translateX:B,translateY:B,translateZ:B,x:B,y:B,z:B,perspective:B,transformPerspective:B,opacity:F,originX:H,originY:H,originZ:B,zIndex:eZ,fillOpacity:F,strokeOpacity:F,numOctaves:eZ},eH={...eW,color:eC,backgroundColor:eC,outlineColor:eC,fill:eC,stroke:eC,borderColor:eC,borderTopColor:eC,borderRightColor:eC,borderBottomColor:eC,borderLeftColor:eC,filter:eB,WebkitFilter:eB},eq=e=>eH[e];function eG(e,t){let n=eq(e);return n!==eB&&(n=eL),n.getAnimatableNone?n.getAnimatableNone(t):void 0}let eK=new Set(["auto","none","0"]);class eX extends eb{constructor(e,t,n,r,i){super(e,t,n,r,i,!0)}readKeyframes(){let{unresolvedKeyframes:e,element:t,name:n}=this;if(!t||!t.current)return;super.readKeyframes();for(let n=0;n<e.length;n++){let r=e[n];if("string"==typeof r&&x(r=r.trim())){let i=function e(t,n,r=1){J(r<=4,`Max CSS variable fallback depth detected in property "${t}". This may indicate a circular fallback dependency.`,"max-css-var-depth");let[i,o]=function(e){let t=Q.exec(e);if(!t)return[,];let[,n,r,i]=t;return[`--${n??r}`,i]}(t);if(!i)return;let s=window.getComputedStyle(n).getPropertyValue(i);if(s){let e=s.trim();return Y(e)?parseFloat(e):e}return x(o)?e(o,n,r+1):o}(r,t.current);void 0!==i&&(e[n]=i),n===e.length-1&&(this.finalKeyframe=r)}}if(this.resolveNoneKeyframes(),!j.has(n)||2!==e.length)return;let[r,i]=e,o=K(r),s=K(i);if(o!==s)if(ee(o)&&ee(s))for(let t=0;t<e.length;t++){let n=e[t];"string"==typeof n&&(e[t]=parseFloat(n))}else er[n]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){let{unresolvedKeyframes:e,name:t}=this,n=[];for(let t=0;t<e.length;t++){var r;(null===e[t]||("number"==typeof(r=e[t])?0===r:null===r||"none"===r||"0"===r||ex(r)))&&n.push(t)}n.length&&function(e,t,n){let r,i=0;for(;i<e.length&&!r;){let t=e[i];"string"==typeof t&&!eK.has(t)&&ej(t).values.length&&(r=e[i]),i++}if(r&&n)for(let i of t)e[i]=eG(n,r)}(e,n,t)}measureInitialState(){let{element:e,unresolvedKeyframes:t,name:n}=this;if(!e||!e.current)return;"height"===n&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=er[n](e.measureViewportBox(),window.getComputedStyle(e.current)),t[0]=this.measuredOrigin;let r=t[t.length-1];void 0!==r&&e.getValue(n,r).jump(r,!1)}measureEndState(){let{element:e,name:t,unresolvedKeyframes:n}=this;if(!e||!e.current)return;let r=e.getValue(t);r&&r.jump(this.measuredOrigin,!1);let i=n.length-1,o=n[i];n[i]=er[t](e.measureViewportBox(),window.getComputedStyle(e.current)),null!==o&&void 0===this.finalKeyframe&&(this.finalKeyframe=o),this.removedTransforms?.length&&this.removedTransforms.forEach(([t,n])=>{e.getValue(t).set(n)}),this.resolveNoneKeyframes()}}let eJ=e=>!!(e&&e.getVelocity);function eY(){r=void 0}let eQ={now:()=>(void 0===r&&eQ.set(ed.isProcessing||eo.useManualTiming?ed.timestamp:performance.now()),r),set:e=>{r=e,queueMicrotask(eY)}};function e0(e,t){-1===e.indexOf(t)&&e.push(t)}function e1(e,t){let n=e.indexOf(t);n>-1&&e.splice(n,1)}class e2{constructor(){this.subscriptions=[]}add(e){return e0(this.subscriptions,e),()=>e1(this.subscriptions,e)}notify(e,t,n){let r=this.subscriptions.length;if(r)if(1===r)this.subscriptions[0](e,t,n);else for(let i=0;i<r;i++){let r=this.subscriptions[i];r&&r(e,t,n)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}let e5={current:void 0};class e9{constructor(e,t={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=(e,t=!0)=>{let n=eQ.now();if(this.updatedAt!==n&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(e),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(let e of this.dependents)e.dirty();t&&this.events.renderRequest?.notify(this.current)},this.hasAnimated=!1,this.setCurrent(e),this.owner=t.owner}setCurrent(e){this.current=e,this.updatedAt=eQ.now(),null===this.canTrackVelocity&&void 0!==e&&(this.canTrackVelocity=!isNaN(parseFloat(this.current)))}setPrevFrameValue(e=this.current){this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt}onChange(e){return this.on("change",e)}on(e,t){this.events[e]||(this.events[e]=new e2);let n=this.events[e].add(t);return"change"===e?()=>{n(),eu.read(()=>{this.events.change.getSize()||this.stop()})}:n}clearListeners(){for(let e in this.events)this.events[e].clear()}attach(e,t){this.passiveEffect=e,this.stopPassiveEffect=t}set(e,t=!0){t&&this.passiveEffect?this.passiveEffect(e,this.updateAndNotify):this.updateAndNotify(e,t)}setWithVelocity(e,t,n){this.set(t),this.prev=void 0,this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt-n}jump(e,t=!0){this.updateAndNotify(e),this.prev=e,this.prevUpdatedAt=this.prevFrameValue=void 0,t&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(e){this.dependents||(this.dependents=new Set),this.dependents.add(e)}removeDependent(e){this.dependents&&this.dependents.delete(e)}get(){return e5.current&&e5.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){var e;let t=eQ.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||t-this.updatedAt>30)return 0;let n=Math.min(this.updatedAt-this.prevUpdatedAt,30);return e=parseFloat(this.current)-parseFloat(this.prevFrameValue),n?1e3/n*e:0}start(e){return this.stop(),new Promise(t=>{this.hasAnimated=!0,this.animation=e(t),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function e4(e,t){return new e9(e,t)}let e3=[...G,eC,eL],{schedule:e6}=el(queueMicrotask,!1),e8={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},e7={};for(let e in e8)e7[e]={isEnabled:t=>e8[e].some(e=>!!t[e])};let te=()=>({translate:0,scale:1,origin:0,originPoint:0}),tt=()=>({x:te(),y:te()}),tn=()=>({min:0,max:0}),tr=()=>({x:tn(),y:tn()});var ti=n(8972);let to={current:null},ts={current:!1},ta=new WeakMap;function tl(e){return null!==e&&"object"==typeof e&&"function"==typeof e.start}function tu(e){return"string"==typeof e||Array.isArray(e)}let tc=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],td=["initial",...tc];function th(e){return tl(e.animate)||td.some(t=>tu(e[t]))}function tf(e){return!!(th(e)||e.variants)}function tp(e){let t=[{},{}];return e?.values.forEach((e,n)=>{t[0][n]=e.get(),t[1][n]=e.getVelocity()}),t}function tm(e,t,n,r){if("function"==typeof t){let[i,o]=tp(r);t=t(void 0!==n?n:e.custom,i,o)}if("string"==typeof t&&(t=e.variants&&e.variants[t]),"function"==typeof t){let[i,o]=tp(r);t=t(void 0!==n?n:e.custom,i,o)}return t}let tg=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class tv{scrapeMotionValuesFromProps(e,t,n){return{}}constructor({parent:e,props:t,presenceContext:n,reducedMotionConfig:r,blockInitialAnimation:i,visualState:o},s={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=eb,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let e=eQ.now();this.renderScheduledAt<e&&(this.renderScheduledAt=e,eu.render(this.render,!1,!0))};let{latestValues:a,renderState:l}=o;this.latestValues=a,this.baseTarget={...a},this.initialValues=t.initial?{...a}:{},this.renderState=l,this.parent=e,this.props=t,this.presenceContext=n,this.depth=e?e.depth+1:0,this.reducedMotionConfig=r,this.options=s,this.blockInitialAnimation=!!i,this.isControllingVariants=th(t),this.isVariantNode=tf(t),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(e&&e.current);let{willChange:u,...c}=this.scrapeMotionValuesFromProps(t,{},this);for(let e in c){let t=c[e];void 0!==a[e]&&eJ(t)&&t.set(a[e],!1)}}mount(e){this.current=e,ta.set(e,this),this.projection&&!this.projection.instance&&this.projection.mount(e),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((e,t)=>this.bindToMotionValue(t,e)),ts.current||function(){if(ts.current=!0,ti.B)if(window.matchMedia){let e=window.matchMedia("(prefers-reduced-motion)"),t=()=>to.current=e.matches;e.addEventListener("change",t),t()}else to.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||to.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let e in this.projection&&this.projection.unmount(),ec(this.notifyUpdate),ec(this.render),this.valueSubscriptions.forEach(e=>e()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[e].clear();for(let e in this.features){let t=this.features[e];t&&(t.unmount(),t.isMounted=!1)}this.current=null}bindToMotionValue(e,t){let n;this.valueSubscriptions.has(e)&&this.valueSubscriptions.get(e)();let r=s.has(e);r&&this.onBindTransform&&this.onBindTransform();let i=t.on("change",t=>{this.latestValues[e]=t,this.props.onUpdate&&eu.preRender(this.notifyUpdate),r&&this.projection&&(this.projection.isTransformDirty=!0)}),o=t.on("renderRequest",this.scheduleRender);window.MotionCheckAppearSync&&(n=window.MotionCheckAppearSync(this,e,t)),this.valueSubscriptions.set(e,()=>{i(),o(),n&&n(),t.owner&&t.stop()})}sortNodePosition(e){return this.current&&this.sortInstanceNodePosition&&this.type===e.type?this.sortInstanceNodePosition(this.current,e.current):0}updateFeatures(){let e="animation";for(e in e7){let t=e7[e];if(!t)continue;let{isEnabled:n,Feature:r}=t;if(!this.features[e]&&r&&n(this.props)&&(this.features[e]=new r(this)),this.features[e]){let t=this.features[e];t.isMounted?t.update():(t.mount(),t.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):tr()}getStaticValue(e){return this.latestValues[e]}setStaticValue(e,t){this.latestValues[e]=t}update(e,t){(e.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=e,this.prevPresenceContext=this.presenceContext,this.presenceContext=t;for(let t=0;t<tg.length;t++){let n=tg[t];this.propEventSubscriptions[n]&&(this.propEventSubscriptions[n](),delete this.propEventSubscriptions[n]);let r=e["on"+n];r&&(this.propEventSubscriptions[n]=this.on(n,r))}this.prevMotionValues=function(e,t,n){for(let r in t){let i=t[r],o=n[r];if(eJ(i))e.addValue(r,i);else if(eJ(o))e.addValue(r,e4(i,{owner:e}));else if(o!==i)if(e.hasValue(r)){let t=e.getValue(r);!0===t.liveStyle?t.jump(i):t.hasAnimated||t.set(i)}else{let t=e.getStaticValue(r);e.addValue(r,e4(void 0!==t?t:i,{owner:e}))}}for(let r in n)void 0===t[r]&&e.removeValue(r);return t}(this,this.scrapeMotionValuesFromProps(e,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(e){return this.props.variants?this.props.variants[e]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(e){let t=this.getClosestVariantNode();if(t)return t.variantChildren&&t.variantChildren.add(e),()=>t.variantChildren.delete(e)}addValue(e,t){let n=this.values.get(e);t!==n&&(n&&this.removeValue(e),this.bindToMotionValue(e,t),this.values.set(e,t),this.latestValues[e]=t.get())}removeValue(e){this.values.delete(e);let t=this.valueSubscriptions.get(e);t&&(t(),this.valueSubscriptions.delete(e)),delete this.latestValues[e],this.removeValueFromRenderState(e,this.renderState)}hasValue(e){return this.values.has(e)}getValue(e,t){if(this.props.values&&this.props.values[e])return this.props.values[e];let n=this.values.get(e);return void 0===n&&void 0!==t&&(n=e4(null===t?void 0:t,{owner:this}),this.addValue(e,n)),n}readValue(e,t){let n=void 0===this.latestValues[e]&&this.current?this.getBaseTargetFromProps(this.props,e)??this.readValueFromInstance(this.current,e,this.options):this.latestValues[e];if(null!=n){if("string"==typeof n&&(Y(n)||ex(n)))n=parseFloat(n);else{let r;r=n,!e3.find(q(r))&&eL.test(t)&&(n=eG(e,t))}this.setBaseTarget(e,eJ(n)?n.get():n)}return eJ(n)?n.get():n}setBaseTarget(e,t){this.baseTarget[e]=t}getBaseTarget(e){let t,{initial:n}=this.props;if("string"==typeof n||"object"==typeof n){let r=tm(this.props,n,this.presenceContext?.custom);r&&(t=r[e])}if(n&&void 0!==t)return t;let r=this.getBaseTargetFromProps(this.props,e);return void 0===r||eJ(r)?void 0!==this.initialValues[e]&&void 0===t?void 0:this.baseTarget[e]:r}on(e,t){return this.events[e]||(this.events[e]=new e2),this.events[e].add(t)}notify(e,...t){this.events[e]&&this.events[e].notify(...t)}scheduleRenderMicrotask(){e6.render(this.render)}}class ty extends tv{constructor(){super(...arguments),this.KeyframeResolver=eX}sortInstanceNodePosition(e,t){return 2&e.compareDocumentPosition(t)?1:-1}getBaseTargetFromProps(e,t){return e.style?e.style[t]:void 0}removeValueFromRenderState(e,{vars:t,style:n}){delete t[e],delete n[e]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:e}=this.props;eJ(e)&&(this.childSubscription=e.on("change",e=>{this.current&&(this.current.textContent=`${e}`)}))}}let tb=(e,t)=>t&&"number"==typeof e?t.transform(e):e,tx={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},tw=o.length;function tk(e,t,n){let{style:r,vars:i,transformOrigin:a}=e,l=!1,u=!1;for(let e in t){let n=t[e];if(s.has(e)){l=!0;continue}if(y(e)){i[e]=n;continue}{let t=tb(n,eW[e]);e.startsWith("origin")?(u=!0,a[e]=t):r[e]=t}}if(!t.transform&&(l||n?r.transform=function(e,t,n){let r="",i=!0;for(let s=0;s<tw;s++){let a=o[s],l=e[a];if(void 0===l)continue;let u=!0;if(!(u="number"==typeof l?l===+!!a.startsWith("scale"):0===parseFloat(l))||n){let e=tb(l,eW[a]);if(!u){i=!1;let t=tx[a]||a;r+=`${t}(${e}) `}n&&(t[a]=e)}}return r=r.trim(),n?r=n(t,i?"":r):i&&(r="none"),r}(t,e.transform,n):r.transform&&(r.transform="none")),u){let{originX:e="50%",originY:t="50%",originZ:n=0}=a;r.transformOrigin=`${e} ${t} ${n}`}}function tA(e,{style:t,vars:n},r,i){let o,s=e.style;for(o in t)s[o]=t[o];for(o in i?.applyProjectionStyles(s,r),n)s.setProperty(o,n[o])}let tE={};function tS(e,{layout:t,layoutId:n}){return s.has(e)||e.startsWith("origin")||(t||void 0!==n)&&(!!tE[e]||"opacity"===e)}function t_(e,t,n){let{style:r}=e,i={};for(let o in r)(eJ(r[o])||t.style&&eJ(t.style[o])||tS(o,e)||n?.getValue(o)?.liveStyle!==void 0)&&(i[o]=r[o]);return i}class tP extends ty{constructor(){super(...arguments),this.type="html",this.renderInstance=tA}readValueFromInstance(e,t){if(s.has(t))return this.projection?.isProjecting?p(t):((e,t)=>{let{transform:n="none"}=getComputedStyle(e);return m(n,t)})(e,t);{let n=window.getComputedStyle(e),r=(y(t)?n.getPropertyValue(t):n[t])||0;return"string"==typeof r?r.trim():r}}measureInstanceViewportBox(e,{transformPagePoint:t}){return I(e,t)}build(e,t,n){tk(e,t,n.transformTemplate)}scrapeMotionValuesFromProps(e,t,n){return t_(e,t,n)}}let tT=e=>e.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),tM={offset:"stroke-dashoffset",array:"stroke-dasharray"},tC={offset:"strokeDashoffset",array:"strokeDasharray"};function tD(e,{attrX:t,attrY:n,attrScale:r,pathLength:i,pathSpacing:o=1,pathOffset:s=0,...a},l,u,c){if(tk(e,a,u),l){e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox);return}e.attrs=e.style,e.style={};let{attrs:d,style:h}=e;d.transform&&(h.transform=d.transform,delete d.transform),(h.transform||d.transformOrigin)&&(h.transformOrigin=d.transformOrigin??"50% 50%",delete d.transformOrigin),h.transform&&(h.transformBox=c?.transformBox??"fill-box",delete d.transformBox),void 0!==t&&(d.x=t),void 0!==n&&(d.y=n),void 0!==r&&(d.scale=r),void 0!==i&&function(e,t,n=1,r=0,i=!0){e.pathLength=1;let o=i?tM:tC;e[o.offset]=B.transform(-r);let s=B.transform(t),a=B.transform(n);e[o.array]=`${s} ${a}`}(d,i,o,s,!1)}let tV=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]),tz=e=>"string"==typeof e&&"svg"===e.toLowerCase();function tI(e,t,n){let r=t_(e,t,n);for(let n in e)(eJ(e[n])||eJ(t[n]))&&(r[-1!==o.indexOf(n)?"attr"+n.charAt(0).toUpperCase()+n.substring(1):n]=e[n]);return r}class tj extends ty{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=tr}getBaseTargetFromProps(e,t){return e[t]}readValueFromInstance(e,t){if(s.has(t)){let e=eq(t);return e&&e.default||0}return t=tV.has(t)?t:tT(t),e.getAttribute(t)}scrapeMotionValuesFromProps(e,t,n){return tI(e,t,n)}build(e,t,n){tD(e,t,this.isSVGTag,n.transformTemplate,n.style)}renderInstance(e,t,n,r){for(let n in tA(e,t,void 0,r),t.attrs)e.setAttribute(tV.has(n)?n:tT(n),t.attrs[n])}mount(e){this.isSVGTag=tz(e.tagName),super.mount(e)}}let tR=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function tO(e){if("string"!=typeof e||e.includes("-"));else if(tR.indexOf(e)>-1||/[A-Z]/u.test(e))return!0;return!1}var tF=n(5155),tL=n(869);let t$=(0,i.createContext)({strict:!1});var tN=n(1508);let tU=(0,i.createContext)({});function tB(e){return Array.isArray(e)?e.join(" "):e}let tZ=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function tW(e,t,n){for(let r in t)eJ(t[r])||tS(r,n)||(e[r]=t[r])}let tH=()=>({...tZ(),attrs:{}}),tq=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function tG(e){return e.startsWith("while")||e.startsWith("drag")&&"draggable"!==e||e.startsWith("layout")||e.startsWith("onTap")||e.startsWith("onPan")||e.startsWith("onLayout")||tq.has(e)}let tK=e=>!tG(e);try{!function(e){"function"==typeof e&&(tK=t=>t.startsWith("on")?!tG(t):e(t))}(require("@emotion/is-prop-valid").default)}catch{}var tX=n(845),tJ=n(2885);function tY(e){return eJ(e)?e.get():e}let tQ=e=>(t,n)=>{let r=(0,i.useContext)(tU),o=(0,i.useContext)(tX.t),s=()=>(function({scrapeMotionValuesFromProps:e,createRenderState:t},n,r,i){return{latestValues:function(e,t,n,r){let i={},o=r(e,{});for(let e in o)i[e]=tY(o[e]);let{initial:s,animate:a}=e,l=th(e),u=tf(e);t&&u&&!l&&!1!==e.inherit&&(void 0===s&&(s=t.initial),void 0===a&&(a=t.animate));let c=!!n&&!1===n.initial,d=(c=c||!1===s)?a:s;if(d&&"boolean"!=typeof d&&!tl(d)){let t=Array.isArray(d)?d:[d];for(let n=0;n<t.length;n++){let r=tm(e,t[n]);if(r){let{transitionEnd:e,transition:t,...n}=r;for(let e in n){let t=n[e];if(Array.isArray(t)){let e=c?t.length-1:0;t=t[e]}null!==t&&(i[e]=t)}for(let t in e)i[t]=e[t]}}}return i}(n,r,i,e),renderState:t()}})(e,t,r,o);return n?s():(0,tJ.M)(s)},t0=tQ({scrapeMotionValuesFromProps:t_,createRenderState:tZ}),t1=tQ({scrapeMotionValuesFromProps:tI,createRenderState:tH}),t2=Symbol.for("motionComponentSymbol");function t5(e){return e&&"object"==typeof e&&Object.prototype.hasOwnProperty.call(e,"current")}let t9="data-"+tT("framerAppearId"),t4=(0,i.createContext)({});var t3=n(7494);function t6(e){var t,n;let{forwardMotionProps:r=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},o=arguments.length>2?arguments[2]:void 0,s=arguments.length>3?arguments[3]:void 0;o&&function(e){for(let t in e)e7[t]={...e7[t],...e[t]}}(o);let a=tO(e)?t1:t0;function l(t,n){var o;let l,u={...(0,i.useContext)(tN.Q),...t,layoutId:function(e){let{layoutId:t}=e,n=(0,i.useContext)(tL.L).id;return n&&void 0!==t?n+"-"+t:t}(t)},{isStatic:c}=u,d=function(e){let{initial:t,animate:n}=function(e,t){if(th(e)){let{initial:t,animate:n}=e;return{initial:!1===t||tu(t)?t:void 0,animate:tu(n)?n:void 0}}return!1!==e.inherit?t:{}}(e,(0,i.useContext)(tU));return(0,i.useMemo)(()=>({initial:t,animate:n}),[tB(t),tB(n)])}(t),h=a(t,c);if(!c&&ti.B){(0,i.useContext)(t$).strict;let t=function(e){let{drag:t,layout:n}=e7;if(!t&&!n)return{};let r={...t,...n};return{MeasureLayout:(null==t?void 0:t.isEnabled(e))||(null==n?void 0:n.isEnabled(e))?r.MeasureLayout:void 0,ProjectionNode:r.ProjectionNode}}(u);l=t.MeasureLayout,d.visualElement=function(e,t,n,r,o){let{visualElement:s}=(0,i.useContext)(tU),a=(0,i.useContext)(t$),l=(0,i.useContext)(tX.t),u=(0,i.useContext)(tN.Q).reducedMotion,c=(0,i.useRef)(null);r=r||a.renderer,!c.current&&r&&(c.current=r(e,{visualState:t,parent:s,props:n,presenceContext:l,blockInitialAnimation:!!l&&!1===l.initial,reducedMotionConfig:u}));let d=c.current,h=(0,i.useContext)(t4);d&&!d.projection&&o&&("html"===d.type||"svg"===d.type)&&function(e,t,n,r){let{layoutId:i,layout:o,drag:s,dragConstraints:a,layoutScroll:l,layoutRoot:u,layoutCrossfade:c}=t;e.projection=new n(e.latestValues,t["data-framer-portal-id"]?void 0:function e(t){if(t)return!1!==t.options.allowProjection?t.projection:e(t.parent)}(e.parent)),e.projection.setOptions({layoutId:i,layout:o,alwaysMeasureLayout:!!s||a&&t5(a),visualElement:e,animationType:"string"==typeof o?o:"both",initialPromotionConfig:r,crossfade:c,layoutScroll:l,layoutRoot:u})}(c.current,n,o,h);let f=(0,i.useRef)(!1);(0,i.useInsertionEffect)(()=>{d&&f.current&&d.update(n,l)});let p=n[t9],m=(0,i.useRef)(!!p&&!window.MotionHandoffIsComplete?.(p)&&window.MotionHasOptimisedAnimation?.(p));return(0,t3.E)(()=>{d&&(f.current=!0,window.MotionIsMounted=!0,d.updateFeatures(),d.scheduleRenderMicrotask(),m.current&&d.animationState&&d.animationState.animateChanges())}),(0,i.useEffect)(()=>{d&&(!m.current&&d.animationState&&d.animationState.animateChanges(),m.current&&(queueMicrotask(()=>{window.MotionHandoffMarkAsComplete?.(p)}),m.current=!1))}),d}(e,h,u,s,t.ProjectionNode)}return(0,tF.jsxs)(tU.Provider,{value:d,children:[l&&d.visualElement?(0,tF.jsx)(l,{visualElement:d.visualElement,...u}):null,function(e,t,n,{latestValues:r},o,s=!1){let a=(tO(e)?function(e,t,n,r){let o=(0,i.useMemo)(()=>{let n=tH();return tD(n,t,tz(r),e.transformTemplate,e.style),{...n.attrs,style:{...n.style}}},[t]);if(e.style){let t={};tW(t,e.style,e),o.style={...t,...o.style}}return o}:function(e,t){let n={},r=function(e,t){let n=e.style||{},r={};return tW(r,n,e),Object.assign(r,function({transformTemplate:e},t){return(0,i.useMemo)(()=>{let n=tZ();return tk(n,t,e),Object.assign({},n.vars,n.style)},[t])}(e,t)),r}(e,t);return e.drag&&!1!==e.dragListener&&(n.draggable=!1,r.userSelect=r.WebkitUserSelect=r.WebkitTouchCallout="none",r.touchAction=!0===e.drag?"none":`pan-${"x"===e.drag?"y":"x"}`),void 0===e.tabIndex&&(e.onTap||e.onTapStart||e.whileTap)&&(n.tabIndex=0),n.style=r,n})(t,r,o,e),l=function(e,t,n){let r={};for(let i in e)("values"!==i||"object"!=typeof e.values)&&(tK(i)||!0===n&&tG(i)||!t&&!tG(i)||e.draggable&&i.startsWith("onDrag"))&&(r[i]=e[i]);return r}(t,"string"==typeof e,s),u=e!==i.Fragment?{...l,...a,ref:n}:{},{children:c}=t,d=(0,i.useMemo)(()=>eJ(c)?c.get():c,[c]);return(0,i.createElement)(e,{...u,children:d})}(e,t,(o=d.visualElement,(0,i.useCallback)(e=>{e&&h.onMount&&h.onMount(e),o&&(e?o.mount(e):o.unmount()),n&&("function"==typeof n?n(e):t5(n)&&(n.current=e))},[o])),h,c,r)]})}l.displayName="motion.".concat("string"==typeof e?e:"create(".concat(null!=(n=null!=(t=e.displayName)?t:e.name)?n:"",")"));let u=(0,i.forwardRef)(l);return u[t2]=e,u}function t8(e,t,n){let r=e.getProps();return tm(r,t,void 0!==n?n:r.custom,e)}function t7(e,t){return e?.[t]??e?.default??e}let ne=e=>Array.isArray(e);function nt(e,t){let n=e.getValue("willChange");if(eJ(n)&&n.add)return n.add(t);if(!n&&eo.WillChange){let n=new eo.WillChange("auto");e.addValue("willChange",n),n.add(t)}}let nn=(e,t)=>n=>t(e(n)),nr=(...e)=>e.reduce(nn),ni=e=>1e3*e,no={layout:0,mainThread:0,waapi:0};function ns(e,t,n){return(n<0&&(n+=1),n>1&&(n-=1),n<1/6)?e+(t-e)*6*n:n<.5?t:n<2/3?e+(t-e)*(2/3-n)*6:e}function na(e,t){return n=>n>0?t:e}let nl=(e,t,n)=>{let r=e*e,i=n*(t*t-r)+r;return i<0?0:Math.sqrt(i)},nu=[eT,eP,eM];function nc(e){let t=nu.find(t=>t.test(e));if(X(!!t,`'${e}' is not an animatable color. Use the equivalent color code instead.`,"color-not-animatable"),!t)return!1;let n=t.parse(e);return t===eM&&(n=function({hue:e,saturation:t,lightness:n,alpha:r}){e/=360,n/=100;let i=0,o=0,s=0;if(t/=100){let r=n<.5?n*(1+t):n+t-n*t,a=2*n-r;i=ns(a,r,e+1/3),o=ns(a,r,e),s=ns(a,r,e-1/3)}else i=o=s=n;return{red:Math.round(255*i),green:Math.round(255*o),blue:Math.round(255*s),alpha:r}}(n)),n}let nd=(e,t)=>{let n=nc(e),r=nc(t);if(!n||!r)return na(e,t);let i={...n};return e=>(i.red=nl(n.red,r.red,e),i.green=nl(n.green,r.green,e),i.blue=nl(n.blue,r.blue,e),i.alpha=A(n.alpha,r.alpha,e),eP.transform(i))},nh=new Set(["none","hidden"]);function nf(e,t){return n=>A(e,t,n)}function np(e){return"number"==typeof e?nf:"string"==typeof e?x(e)?na:eC.test(e)?nd:nv:Array.isArray(e)?nm:"object"==typeof e?eC.test(e)?nd:ng:na}function nm(e,t){let n=[...e],r=n.length,i=e.map((e,n)=>np(e)(e,t[n]));return e=>{for(let t=0;t<r;t++)n[t]=i[t](e);return n}}function ng(e,t){let n={...e,...t},r={};for(let i in n)void 0!==e[i]&&void 0!==t[i]&&(r[i]=np(e[i])(e[i],t[i]));return e=>{for(let t in r)n[t]=r[t](e);return n}}let nv=(e,t)=>{let n=eL.createTransformer(t),r=ej(e),i=ej(t);return r.indexes.var.length===i.indexes.var.length&&r.indexes.color.length===i.indexes.color.length&&r.indexes.number.length>=i.indexes.number.length?nh.has(e)&&!i.values.length||nh.has(t)&&!r.values.length?function(e,t){return nh.has(e)?n=>n<=0?e:t:n=>n>=1?t:e}(e,t):nr(nm(function(e,t){let n=[],r={color:0,var:0,number:0};for(let i=0;i<t.values.length;i++){let o=t.types[i],s=e.indexes[o][r[o]],a=e.values[s]??0;n[i]=a,r[o]++}return n}(r,i),i.values),n):(X(!0,`Complex values '${e}' and '${t}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`,"complex-values-different"),na(e,t))};function ny(e,t,n){return"number"==typeof e&&"number"==typeof t&&"number"==typeof n?A(e,t,n):np(e)(e,t)}let nb=e=>{let t=({timestamp:t})=>e(t);return{start:(e=!0)=>eu.update(t,e),stop:()=>ec(t),now:()=>ed.isProcessing?ed.timestamp:eQ.now()}},nx=(e,t,n=10)=>{let r="",i=Math.max(Math.round(t/n),2);for(let t=0;t<i;t++)r+=Math.round(1e4*e(t/(i-1)))/1e4+", ";return`linear(${r.substring(0,r.length-2)})`};function nw(e){let t=0,n=e.next(t);for(;!n.done&&t<2e4;)t+=50,n=e.next(t);return t>=2e4?1/0:t}function nk(e,t,n){var r,i;let o=Math.max(t-5,0);return r=n-e(o),(i=t-o)?1e3/i*r:0}let nA={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};function nE(e,t){return e*Math.sqrt(1-t*t)}let nS=["duration","bounce"],n_=["stiffness","damping","mass"];function nP(e,t){return t.some(t=>void 0!==e[t])}function nT(e=nA.visualDuration,t=nA.bounce){let n,r="object"!=typeof e?{visualDuration:e,keyframes:[0,1],bounce:t}:e,{restSpeed:i,restDelta:o}=r,s=r.keyframes[0],a=r.keyframes[r.keyframes.length-1],l={done:!1,value:s},{stiffness:u,damping:c,mass:d,duration:h,velocity:f,isResolvedFromDuration:p}=function(e){let t={velocity:nA.velocity,stiffness:nA.stiffness,damping:nA.damping,mass:nA.mass,isResolvedFromDuration:!1,...e};if(!nP(e,n_)&&nP(e,nS))if(e.visualDuration){let n=2*Math.PI/(1.2*e.visualDuration),r=n*n,i=2*R(.05,1,1-(e.bounce||0))*Math.sqrt(r);t={...t,mass:nA.mass,stiffness:r,damping:i}}else{let n=function({duration:e=nA.duration,bounce:t=nA.bounce,velocity:n=nA.velocity,mass:r=nA.mass}){let i,o;X(e<=ni(nA.maxDuration),"Spring duration must be 10 seconds or less","spring-duration-limit");let s=1-t;s=R(nA.minDamping,nA.maxDamping,s),e=R(nA.minDuration,nA.maxDuration,e/1e3),s<1?(i=t=>{let r=t*s,i=r*e;return .001-(r-n)/nE(t,s)*Math.exp(-i)},o=t=>{let r=t*s*e,o=Math.pow(s,2)*Math.pow(t,2)*e,a=Math.exp(-r),l=nE(Math.pow(t,2),s);return(r*n+n-o)*a*(-i(t)+.001>0?-1:1)/l}):(i=t=>-.001+Math.exp(-t*e)*((t-n)*e+1),o=t=>e*e*(n-t)*Math.exp(-t*e));let a=function(e,t,n){let r=n;for(let n=1;n<12;n++)r-=e(r)/t(r);return r}(i,o,5/e);if(e=ni(e),isNaN(a))return{stiffness:nA.stiffness,damping:nA.damping,duration:e};{let t=Math.pow(a,2)*r;return{stiffness:t,damping:2*s*Math.sqrt(r*t),duration:e}}}(e);(t={...t,...n,mass:nA.mass}).isResolvedFromDuration=!0}return t}({...r,velocity:-((r.velocity||0)/1e3)}),m=f||0,g=c/(2*Math.sqrt(u*d)),v=a-s,y=Math.sqrt(u/d)/1e3,b=5>Math.abs(v);if(i||(i=b?nA.restSpeed.granular:nA.restSpeed.default),o||(o=b?nA.restDelta.granular:nA.restDelta.default),g<1){let e=nE(y,g);n=t=>a-Math.exp(-g*y*t)*((m+g*y*v)/e*Math.sin(e*t)+v*Math.cos(e*t))}else if(1===g)n=e=>a-Math.exp(-y*e)*(v+(m+y*v)*e);else{let e=y*Math.sqrt(g*g-1);n=t=>{let n=Math.exp(-g*y*t),r=Math.min(e*t,300);return a-n*((m+g*y*v)*Math.sinh(r)+e*v*Math.cosh(r))/e}}let x={calculatedDuration:p&&h||null,next:e=>{let t=n(e);if(p)l.done=e>=h;else{let r=0===e?m:0;g<1&&(r=0===e?ni(m):nk(n,e,t));let s=Math.abs(a-t)<=o;l.done=Math.abs(r)<=i&&s}return l.value=l.done?a:t,l},toString:()=>{let e=Math.min(nw(x),2e4),t=nx(t=>x.next(e*t).value,e,30);return e+"ms "+t},toTransition:()=>{}};return x}function nM({keyframes:e,velocity:t=0,power:n=.8,timeConstant:r=325,bounceDamping:i=10,bounceStiffness:o=500,modifyTarget:s,min:a,max:l,restDelta:u=.5,restSpeed:c}){let d,h,f=e[0],p={done:!1,value:f},m=n*t,g=f+m,v=void 0===s?g:s(g);v!==g&&(m=v-f);let y=e=>-m*Math.exp(-e/r),b=e=>v+y(e),x=e=>{let t=y(e),n=b(e);p.done=Math.abs(t)<=u,p.value=p.done?v:n},w=e=>{let t;if(t=p.value,void 0!==a&&t<a||void 0!==l&&t>l){var n;d=e,h=nT({keyframes:[p.value,(n=p.value,void 0===a?l:void 0===l||Math.abs(a-n)<Math.abs(l-n)?a:l)],velocity:nk(b,e,p.value),damping:i,stiffness:o,restDelta:u,restSpeed:c})}};return w(0),{calculatedDuration:null,next:e=>{let t=!1;return(h||void 0!==d||(t=!0,x(e),w(e)),void 0!==d&&e>=d)?h.next(e-d):(t||x(e),p)}}}nT.applyToOptions=e=>{let t=function(e,t=100,n){let r=n({...e,keyframes:[0,t]}),i=Math.min(nw(r),2e4);return{type:"keyframes",ease:e=>r.next(i*e).value/t,duration:i/1e3}}(e,100,nT);return e.ease=t.ease,e.duration=ni(t.duration),e.type="keyframes",e};let nC=(e,t,n)=>(((1-3*n+3*t)*e+(3*n-6*t))*e+3*t)*e;function nD(e,t,n,r){return e===t&&n===r?ei:i=>0===i||1===i?i:nC(function(e,t,n,r,i){let o,s,a=0;do(o=nC(s=t+(n-t)/2,r,i)-e)>0?n=s:t=s;while(Math.abs(o)>1e-7&&++a<12);return s}(i,0,1,e,n),t,r)}let nV=nD(.42,0,1,1),nz=nD(0,0,.58,1),nI=nD(.42,0,.58,1),nj=e=>t=>t<=.5?e(2*t)/2:(2-e(2*(1-t)))/2,nR=e=>t=>1-e(1-t),nO=nD(.33,1.53,.69,.99),nF=nR(nO),nL=nj(nF),n$=e=>(e*=2)<1?.5*nF(e):.5*(2-Math.pow(2,-10*(e-1))),nN=e=>1-Math.sin(Math.acos(e)),nU=nR(nN),nB=nj(nN),nZ=e=>Array.isArray(e)&&"number"==typeof e[0],nW={linear:ei,easeIn:nV,easeInOut:nI,easeOut:nz,circIn:nN,circInOut:nB,circOut:nU,backIn:nF,backInOut:nL,backOut:nO,anticipate:n$},nH=e=>{if(nZ(e)){J(4===e.length,"Cubic bezier arrays must contain four numerical values.","cubic-bezier-length");let[t,n,r,i]=e;return nD(t,n,r,i)}return"string"==typeof e?(J(void 0!==nW[e],`Invalid easing type '${e}'`,"invalid-easing-type"),nW[e]):e},nq=(e,t,n)=>{let r=t-e;return 0===r?1:(n-e)/r};function nG({duration:e=300,keyframes:t,times:n,ease:r="easeInOut"}){var i;let o=Array.isArray(r)&&"number"!=typeof r[0]?r.map(nH):nH(r),s={done:!1,value:t[0]},a=function(e,t,{clamp:n=!0,ease:r,mixer:i}={}){let o=e.length;if(J(o===t.length,"Both input and output ranges must be the same length","range-length"),1===o)return()=>t[0];if(2===o&&t[0]===t[1])return()=>t[1];let s=e[0]===e[1];e[0]>e[o-1]&&(e=[...e].reverse(),t=[...t].reverse());let a=function(e,t,n){let r=[],i=n||eo.mix||ny,o=e.length-1;for(let n=0;n<o;n++){let o=i(e[n],e[n+1]);t&&(o=nr(Array.isArray(t)?t[n]||ei:t,o)),r.push(o)}return r}(t,r,i),l=a.length,u=n=>{if(s&&n<e[0])return t[0];let r=0;if(l>1)for(;r<e.length-2&&!(n<e[r+1]);r++);let i=nq(e[r],e[r+1],n);return a[r](i)};return n?t=>u(R(e[0],e[o-1],t)):u}((i=n&&n.length===t.length?n:function(e){let t=[0];return!function(e,t){let n=e[e.length-1];for(let r=1;r<=t;r++){let i=nq(0,t,r);e.push(A(n,1,i))}}(t,e.length-1),t}(t),i.map(t=>t*e)),t,{ease:Array.isArray(o)?o:t.map(()=>o||nI).splice(0,t.length-1)});return{calculatedDuration:e,next:t=>(s.value=a(t),s.done=t>=e,s)}}let nK=e=>null!==e;function nX(e,{repeat:t,repeatType:n="loop"},r,i=1){let o=e.filter(nK),s=i<0||t&&"loop"!==n&&t%2==1?0:o.length-1;return s&&void 0!==r?r:o[s]}let nJ={decay:nM,inertia:nM,tween:nG,keyframes:nG,spring:nT};function nY(e){"string"==typeof e.type&&(e.type=nJ[e.type])}class nQ{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(e=>{this.resolve=e})}notifyFinished(){this.resolve()}then(e,t){return this.finished.then(e,t)}}let n0=e=>e/100;class n1 extends nQ{constructor(e){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{let{motionValue:e}=this.options;e&&e.updatedAt!==eQ.now()&&this.tick(eQ.now()),this.isStopped=!0,"idle"!==this.state&&(this.teardown(),this.options.onStop?.())},no.mainThread++,this.options=e,this.initAnimation(),this.play(),!1===e.autoplay&&this.pause()}initAnimation(){let{options:e}=this;nY(e);let{type:t=nG,repeat:n=0,repeatDelay:r=0,repeatType:i,velocity:o=0}=e,{keyframes:s}=e,a=t||nG;a!==nG&&"number"!=typeof s[0]&&(this.mixKeyframes=nr(n0,ny(s[0],s[1])),s=[0,100]);let l=a({...e,keyframes:s});"mirror"===i&&(this.mirroredGenerator=a({...e,keyframes:[...s].reverse(),velocity:-o})),null===l.calculatedDuration&&(l.calculatedDuration=nw(l));let{calculatedDuration:u}=l;this.calculatedDuration=u,this.resolvedDuration=u+r,this.totalDuration=this.resolvedDuration*(n+1)-r,this.generator=l}updateTime(e){let t=Math.round(e-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=t}tick(e,t=!1){let{generator:n,totalDuration:r,mixKeyframes:i,mirroredGenerator:o,resolvedDuration:s,calculatedDuration:a}=this;if(null===this.startTime)return n.next(0);let{delay:l=0,keyframes:u,repeat:c,repeatType:d,repeatDelay:h,type:f,onUpdate:p,finalKeyframe:m}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,e):this.speed<0&&(this.startTime=Math.min(e-r/this.speed,this.startTime)),t?this.currentTime=e:this.updateTime(e);let g=this.currentTime-l*(this.playbackSpeed>=0?1:-1),v=this.playbackSpeed>=0?g<0:g>r;this.currentTime=Math.max(g,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=r);let y=this.currentTime,b=n;if(c){let e=Math.min(this.currentTime,r)/s,t=Math.floor(e),n=e%1;!n&&e>=1&&(n=1),1===n&&t--,(t=Math.min(t,c+1))%2&&("reverse"===d?(n=1-n,h&&(n-=h/s)):"mirror"===d&&(b=o)),y=R(0,1,n)*s}let x=v?{done:!1,value:u[0]}:b.next(y);i&&(x.value=i(x.value));let{done:w}=x;v||null===a||(w=this.playbackSpeed>=0?this.currentTime>=r:this.currentTime<=0);let k=null===this.holdTime&&("finished"===this.state||"running"===this.state&&w);return k&&f!==nM&&(x.value=nX(u,this.options,m,this.speed)),p&&p(x.value),k&&this.finish(),x}then(e,t){return this.finished.then(e,t)}get duration(){return this.calculatedDuration/1e3}get time(){return this.currentTime/1e3}set time(e){e=ni(e),this.currentTime=e,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=e:this.driver&&(this.startTime=this.driver.now()-e/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(e){this.updateTime(eQ.now());let t=this.playbackSpeed!==e;this.playbackSpeed=e,t&&(this.time=this.currentTime/1e3)}play(){if(this.isStopped)return;let{driver:e=nb,startTime:t}=this.options;this.driver||(this.driver=e(e=>this.tick(e))),this.options.onPlay?.();let n=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=n):null!==this.holdTime?this.startTime=n-this.holdTime:this.startTime||(this.startTime=t??n),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(eQ.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state="finished",this.options.onComplete?.()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),this.options.onCancel?.()}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null,no.mainThread--}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(e){return this.startTime=0,this.tick(e,!0)}attachTimeline(e){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),e.observe(this)}}function n2(e){let t;return()=>(void 0===t&&(t=e()),t)}let n5=n2(()=>void 0!==window.ScrollTimeline),n9={},n4=function(e,t){let n=n2(e);return()=>n9[t]??n()}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(e){return!1}return!0},"linearEasing"),n3=([e,t,n,r])=>`cubic-bezier(${e}, ${t}, ${n}, ${r})`,n6={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:n3([0,.65,.55,1]),circOut:n3([.55,0,1,.45]),backIn:n3([.31,.01,.66,-.59]),backOut:n3([.33,1.53,.69,.99])};function n8(e){return"function"==typeof e&&"applyToOptions"in e}class n7 extends nQ{constructor(e){if(super(),this.finishedTime=null,this.isStopped=!1,!e)return;let{element:t,name:n,keyframes:r,pseudoElement:i,allowFlatten:o=!1,finalKeyframe:s,onComplete:a}=e;this.isPseudoElement=!!i,this.allowFlatten=o,this.options=e,J("string"!=typeof e.type,'Mini animate() doesn\'t support "type" as a string.',"mini-spring");let l=function({type:e,...t}){return n8(e)&&n4()?e.applyToOptions(t):(t.duration??(t.duration=300),t.ease??(t.ease="easeOut"),t)}(e);this.animation=function(e,t,n,{delay:r=0,duration:i=300,repeat:o=0,repeatType:s="loop",ease:a="easeOut",times:l}={},u){let c={[t]:n};l&&(c.offset=l);let d=function e(t,n){if(t)return"function"==typeof t?n4()?nx(t,n):"ease-out":nZ(t)?n3(t):Array.isArray(t)?t.map(t=>e(t,n)||n6.easeOut):n6[t]}(a,i);Array.isArray(d)&&(c.easing=d),ea.value&&no.waapi++;let h={delay:r,duration:i,easing:Array.isArray(d)?"linear":d,fill:"both",iterations:o+1,direction:"reverse"===s?"alternate":"normal"};u&&(h.pseudoElement=u);let f=e.animate(c,h);return ea.value&&f.finished.finally(()=>{no.waapi--}),f}(t,n,r,l,i),!1===l.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!i){let e=nX(r,this.options,s,this.speed);this.updateMotionValue?this.updateMotionValue(e):function(e,t,n){t.startsWith("--")?e.style.setProperty(t,n):e.style[t]=n}(t,n,e),this.animation.cancel()}a?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(e){}}stop(){if(this.isStopped)return;this.isStopped=!0;let{state:e}=this;"idle"!==e&&"finished"!==e&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){return Number(this.animation.effect?.getComputedTiming?.().duration||0)/1e3}get time(){return(Number(this.animation.currentTime)||0)/1e3}set time(e){this.finishedTime=null,this.animation.currentTime=ni(e)}get speed(){return this.animation.playbackRate}set speed(e){e<0&&(this.finishedTime=null),this.animation.playbackRate=e}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(e){this.animation.startTime=e}attachTimeline({timeline:e,observe:t}){return(this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,e&&n5())?(this.animation.timeline=e,ei):t(this)}}let re={anticipate:n$,backInOut:nL,circInOut:nB};class rt extends n7{constructor(e){!function(e){"string"==typeof e.ease&&e.ease in re&&(e.ease=re[e.ease])}(e),nY(e),super(e),e.startTime&&(this.startTime=e.startTime),this.options=e}updateMotionValue(e){let{motionValue:t,onUpdate:n,onComplete:r,element:i,...o}=this.options;if(!t)return;if(void 0!==e)return void t.set(e);let s=new n1({...o,autoplay:!1}),a=ni(this.finishedTime??this.time);t.setWithVelocity(s.sample(a-10).value,s.sample(a).value,10),s.stop()}}let rn=(e,t)=>"zIndex"!==t&&!!("number"==typeof e||Array.isArray(e)||"string"==typeof e&&(eL.test(e)||"0"===e)&&!e.startsWith("url(")),rr=new Set(["opacity","clipPath","filter","transform"]),ri=n2(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));class ro extends nQ{constructor({autoplay:e=!0,delay:t=0,type:n="keyframes",repeat:r=0,repeatDelay:i=0,repeatType:o="loop",keyframes:s,name:a,motionValue:l,element:u,...c}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=eQ.now();let d={autoplay:e,delay:t,type:n,repeat:r,repeatDelay:i,repeatType:o,name:a,motionValue:l,element:u,...c},h=u?.KeyframeResolver||eb;this.keyframeResolver=new h(s,(e,t,n)=>this.onKeyframesResolved(e,t,d,!n),a,l,u),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(e,t,n,r){this.keyframeResolver=void 0;let{name:i,type:o,velocity:s,delay:a,isHandoff:l,onUpdate:u}=n;this.resolvedAt=eQ.now(),!function(e,t,n,r){let i=e[0];if(null===i)return!1;if("display"===t||"visibility"===t)return!0;let o=e[e.length-1],s=rn(i,t),a=rn(o,t);return X(s===a,`You are trying to animate ${t} from "${i}" to "${o}". "${s?o:i}" is not an animatable value.`,"value-not-animatable"),!!s&&!!a&&(function(e){let t=e[0];if(1===e.length)return!0;for(let n=0;n<e.length;n++)if(e[n]!==t)return!0}(e)||("spring"===n||n8(n))&&r)}(e,i,o,s)&&((eo.instantAnimations||!a)&&u?.(nX(e,n,t)),e[0]=e[e.length-1],n.duration=0,n.repeat=0);let c={startTime:r?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:t,...n,keyframes:e},d=!l&&function(e){let{motionValue:t,name:n,repeatDelay:r,repeatType:i,damping:o,type:s}=e;if(!(t?.owner?.current instanceof HTMLElement))return!1;let{onUpdate:a,transformTemplate:l}=t.owner.getProps();return ri()&&n&&rr.has(n)&&("transform"!==n||!l)&&!a&&!r&&"mirror"!==i&&0!==o&&"inertia"!==s}(c)?new rt({...c,element:c.motionValue.owner.current}):new n1(c);d.finished.then(()=>this.notifyFinished()).catch(ei),this.pendingTimeline&&(this.stopTimeline=d.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=d}get finished(){return this._animation?this.animation.finished:this._finished}then(e,t){return this.finished.finally(e).then(()=>{})}get animation(){return this._animation||(this.keyframeResolver?.resume(),eg=!0,ey(),ev(),eg=!1),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(e){this.animation.time=e}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(e){this.animation.speed=e}get startTime(){return this.animation.startTime}attachTimeline(e){return this._animation?this.stopTimeline=this.animation.attachTimeline(e):this.pendingTimeline=e,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}let rs=e=>null!==e,ra={type:"spring",stiffness:500,damping:25,restSpeed:10},rl={type:"keyframes",duration:.8},ru={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},rc=(e,t,n,r={},i,o)=>a=>{let l=t7(r,e)||{},u=l.delay||r.delay||0,{elapsed:c=0}=r;c-=ni(u);let d={keyframes:Array.isArray(n)?n:[null,n],ease:"easeOut",velocity:t.getVelocity(),...l,delay:-c,onUpdate:e=>{t.set(e),l.onUpdate&&l.onUpdate(e)},onComplete:()=>{a(),l.onComplete&&l.onComplete()},name:e,motionValue:t,element:o?void 0:i};!function({when:e,delay:t,delayChildren:n,staggerChildren:r,staggerDirection:i,repeat:o,repeatType:s,repeatDelay:a,from:l,elapsed:u,...c}){return!!Object.keys(c).length}(l)&&Object.assign(d,((e,{keyframes:t})=>t.length>2?rl:s.has(e)?e.startsWith("scale")?{type:"spring",stiffness:550,damping:0===t[1]?2*Math.sqrt(550):30,restSpeed:10}:ra:ru)(e,d)),d.duration&&(d.duration=ni(d.duration)),d.repeatDelay&&(d.repeatDelay=ni(d.repeatDelay)),void 0!==d.from&&(d.keyframes[0]=d.from);let h=!1;if(!1!==d.type&&(0!==d.duration||d.repeatDelay)||(d.duration=0,0===d.delay&&(h=!0)),(eo.instantAnimations||eo.skipAnimations)&&(h=!0,d.duration=0,d.delay=0),d.allowFlatten=!l.type&&!l.ease,h&&!o&&void 0!==t.get()){let e=function(e,{repeat:t,repeatType:n="loop"},r){let i=e.filter(rs),o=t&&"loop"!==n&&t%2==1?0:i.length-1;return i[o]}(d.keyframes,l);if(void 0!==e)return void eu.update(()=>{d.onUpdate(e),d.onComplete()})}return l.isSync?new n1(d):new ro(d)};function rd(e,t,{delay:n=0,transitionOverride:r,type:i}={}){let{transition:o=e.getDefaultTransition(),transitionEnd:s,...a}=t;r&&(o=r);let l=[],u=i&&e.animationState&&e.animationState.getState()[i];for(let t in a){let r=e.getValue(t,e.latestValues[t]??null),i=a[t];if(void 0===i||u&&function({protectedKeys:e,needsAnimating:t},n){let r=e.hasOwnProperty(n)&&!0!==t[n];return t[n]=!1,r}(u,t))continue;let s={delay:n,...t7(o||{},t)},c=r.get();if(void 0!==c&&!r.isAnimating&&!Array.isArray(i)&&i===c&&!s.velocity)continue;let d=!1;if(window.MotionHandoffAnimation){let n=e.props[t9];if(n){let e=window.MotionHandoffAnimation(n,t,eu);null!==e&&(s.startTime=e,d=!0)}}nt(e,t),r.start(rc(t,r,i,e.shouldReduceMotion&&j.has(t)?{type:!1}:s,e,d));let h=r.animation;h&&l.push(h)}return s&&Promise.all(l).then(()=>{eu.update(()=>{s&&function(e,t){let{transitionEnd:n={},transition:r={},...i}=t8(e,t)||{};for(let t in i={...i,...n}){var o;let n=ne(o=i[t])?o[o.length-1]||0:o;e.hasValue(t)?e.getValue(t).set(n):e.addValue(t,e4(n))}}(e,s)})}),l}function rh(e,t,n={}){let r=t8(e,t,"exit"===n.type?e.presenceContext?.custom:void 0),{transition:i=e.getDefaultTransition()||{}}=r||{};n.transitionOverride&&(i=n.transitionOverride);let o=r?()=>Promise.all(rd(e,r,n)):()=>Promise.resolve(),s=e.variantChildren&&e.variantChildren.size?(r=0)=>{let{delayChildren:o=0,staggerChildren:s,staggerDirection:a}=i;return function(e,t,n=0,r=0,i=0,o=1,s){let a=[],l=e.variantChildren.size,u=(l-1)*i,c="function"==typeof r,d=c?e=>r(e,l):1===o?(e=0)=>e*i:(e=0)=>u-e*i;return Array.from(e.variantChildren).sort(rf).forEach((e,i)=>{e.notify("AnimationStart",t),a.push(rh(e,t,{...s,delay:n+(c?0:r)+d(i)}).then(()=>e.notify("AnimationComplete",t)))}),Promise.all(a)}(e,t,r,o,s,a,n)}:()=>Promise.resolve(),{when:a}=i;if(!a)return Promise.all([o(),s(n.delay)]);{let[e,t]="beforeChildren"===a?[o,s]:[s,o];return e().then(()=>t())}}function rf(e,t){return e.sortNodePosition(t)}function rp(e,t){if(!Array.isArray(t))return!1;let n=t.length;if(n!==e.length)return!1;for(let r=0;r<n;r++)if(t[r]!==e[r])return!1;return!0}let rm=td.length,rg=[...tc].reverse(),rv=tc.length;function ry(e=!1){return{isActive:e,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function rb(){return{animate:ry(!0),whileInView:ry(),whileHover:ry(),whileTap:ry(),whileDrag:ry(),whileFocus:ry(),exit:ry()}}class rx{constructor(e){this.isMounted=!1,this.node=e}update(){}}class rw extends rx{constructor(e){super(e),e.animationState||(e.animationState=function(e){let t=t=>Promise.all(t.map(({animation:t,options:n})=>(function(e,t,n={}){let r;if(e.notify("AnimationStart",t),Array.isArray(t))r=Promise.all(t.map(t=>rh(e,t,n)));else if("string"==typeof t)r=rh(e,t,n);else{let i="function"==typeof t?t8(e,t,n.custom):t;r=Promise.all(rd(e,i,n))}return r.then(()=>{e.notify("AnimationComplete",t)})})(e,t,n))),n=rb(),r=!0,i=t=>(n,r)=>{let i=t8(e,r,"exit"===t?e.presenceContext?.custom:void 0);if(i){let{transition:e,transitionEnd:t,...r}=i;n={...n,...r,...t}}return n};function o(o){let{props:s}=e,a=function e(t){if(!t)return;if(!t.isControllingVariants){let n=t.parent&&e(t.parent)||{};return void 0!==t.props.initial&&(n.initial=t.props.initial),n}let n={};for(let e=0;e<rm;e++){let r=td[e],i=t.props[r];(tu(i)||!1===i)&&(n[r]=i)}return n}(e.parent)||{},l=[],u=new Set,c={},d=1/0;for(let t=0;t<rv;t++){var h,f;let p=rg[t],m=n[p],g=void 0!==s[p]?s[p]:a[p],v=tu(g),y=p===o?m.isActive:null;!1===y&&(d=t);let b=g===a[p]&&g!==s[p]&&v;if(b&&r&&e.manuallyAnimateOnMount&&(b=!1),m.protectedKeys={...c},!m.isActive&&null===y||!g&&!m.prevProp||tl(g)||"boolean"==typeof g)continue;let x=(h=m.prevProp,"string"==typeof(f=g)?f!==h:!!Array.isArray(f)&&!rp(f,h)),w=x||p===o&&m.isActive&&!b&&v||t>d&&v,k=!1,A=Array.isArray(g)?g:[g],E=A.reduce(i(p),{});!1===y&&(E={});let{prevResolvedValues:S={}}=m,_={...S,...E},P=t=>{w=!0,u.has(t)&&(k=!0,u.delete(t)),m.needsAnimating[t]=!0;let n=e.getValue(t);n&&(n.liveStyle=!1)};for(let e in _){let t=E[e],n=S[e];if(!c.hasOwnProperty(e))(ne(t)&&ne(n)?rp(t,n):t===n)?void 0!==t&&u.has(e)?P(e):m.protectedKeys[e]=!0:null!=t?P(e):u.add(e)}m.prevProp=g,m.prevResolvedValues=E,m.isActive&&(c={...c,...E}),r&&e.blockInitialAnimation&&(w=!1);let T=!(b&&x)||k;w&&T&&l.push(...A.map(e=>({animation:e,options:{type:p}})))}if(u.size){let t={};if("boolean"!=typeof s.initial){let n=t8(e,Array.isArray(s.initial)?s.initial[0]:s.initial);n&&n.transition&&(t.transition=n.transition)}u.forEach(n=>{let r=e.getBaseTarget(n),i=e.getValue(n);i&&(i.liveStyle=!0),t[n]=r??null}),l.push({animation:t})}let p=!!l.length;return r&&(!1===s.initial||s.initial===s.animate)&&!e.manuallyAnimateOnMount&&(p=!1),r=!1,p?t(l):Promise.resolve()}return{animateChanges:o,setActive:function(t,r){if(n[t].isActive===r)return Promise.resolve();e.variantChildren?.forEach(e=>e.animationState?.setActive(t,r)),n[t].isActive=r;let i=o(t);for(let e in n)n[e].protectedKeys={};return i},setAnimateFunction:function(n){t=n(e)},getState:()=>n,reset:()=>{n=rb(),r=!0}}}(e))}updateAnimationControlsSubscription(){let{animate:e}=this.node.getProps();tl(e)&&(this.unmountControls=e.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:e}=this.node.getProps(),{animate:t}=this.node.prevProps||{};e!==t&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}let rk=0;class rA extends rx{constructor(){super(...arguments),this.id=rk++}update(){if(!this.node.presenceContext)return;let{isPresent:e,onExitComplete:t}=this.node.presenceContext,{isPresent:n}=this.node.prevPresenceContext||{};if(!this.node.animationState||e===n)return;let r=this.node.animationState.setActive("exit",!e);t&&!e&&r.then(()=>{t(this.id)})}mount(){let{register:e,onExitComplete:t}=this.node.presenceContext||{};t&&t(this.id),e&&(this.unmount=e(this.id))}unmount(){}}let rE={x:!1,y:!1};function rS(e,t,n,r={passive:!0}){return e.addEventListener(t,n,r),()=>e.removeEventListener(t,n)}let r_=e=>"mouse"===e.pointerType?"number"!=typeof e.button||e.button<=0:!1!==e.isPrimary;function rP(e){return{point:{x:e.pageX,y:e.pageY}}}function rT(e,t,n,r){return rS(e,t,e=>r_(e)&&n(e,rP(e)),r)}function rM(e){return e.max-e.min}function rC(e,t,n,r=.5){e.origin=r,e.originPoint=A(t.min,t.max,e.origin),e.scale=rM(n)/rM(t),e.translate=A(n.min,n.max,e.origin)-e.originPoint,(e.scale>=.9999&&e.scale<=1.0001||isNaN(e.scale))&&(e.scale=1),(e.translate>=-.01&&e.translate<=.01||isNaN(e.translate))&&(e.translate=0)}function rD(e,t,n,r){rC(e.x,t.x,n.x,r?r.originX:void 0),rC(e.y,t.y,n.y,r?r.originY:void 0)}function rV(e,t,n){e.min=n.min+t.min,e.max=e.min+rM(t)}function rz(e,t,n){e.min=t.min-n.min,e.max=e.min+rM(t)}function rI(e,t,n){rz(e.x,t.x,n.x),rz(e.y,t.y,n.y)}function rj(e){return[e("x"),e("y")]}let rR=({current:e})=>e?e.ownerDocument.defaultView:null,rO=(e,t)=>Math.abs(e-t);class rF{constructor(e,t,{transformPagePoint:n,contextWindow:r=window,dragSnapToOrigin:i=!1,distanceThreshold:o=3}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let e=rN(this.lastMoveEventInfo,this.history),t=null!==this.startEvent,n=function(e,t){return Math.sqrt(rO(e.x,t.x)**2+rO(e.y,t.y)**2)}(e.offset,{x:0,y:0})>=this.distanceThreshold;if(!t&&!n)return;let{point:r}=e,{timestamp:i}=ed;this.history.push({...r,timestamp:i});let{onStart:o,onMove:s}=this.handlers;t||(o&&o(this.lastMoveEvent,e),this.startEvent=this.lastMoveEvent),s&&s(this.lastMoveEvent,e)},this.handlePointerMove=(e,t)=>{this.lastMoveEvent=e,this.lastMoveEventInfo=rL(t,this.transformPagePoint),eu.update(this.updatePoint,!0)},this.handlePointerUp=(e,t)=>{this.end();let{onEnd:n,onSessionEnd:r,resumeAnimation:i}=this.handlers;if(this.dragSnapToOrigin&&i&&i(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let o=rN("pointercancel"===e.type?this.lastMoveEventInfo:rL(t,this.transformPagePoint),this.history);this.startEvent&&n&&n(e,o),r&&r(e,o)},!r_(e))return;this.dragSnapToOrigin=i,this.handlers=t,this.transformPagePoint=n,this.distanceThreshold=o,this.contextWindow=r||window;let s=rL(rP(e),this.transformPagePoint),{point:a}=s,{timestamp:l}=ed;this.history=[{...a,timestamp:l}];let{onSessionStart:u}=t;u&&u(e,rN(s,this.history)),this.removeListeners=nr(rT(this.contextWindow,"pointermove",this.handlePointerMove),rT(this.contextWindow,"pointerup",this.handlePointerUp),rT(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(e){this.handlers=e}end(){this.removeListeners&&this.removeListeners(),ec(this.updatePoint)}}function rL(e,t){return t?{point:t(e.point)}:e}function r$(e,t){return{x:e.x-t.x,y:e.y-t.y}}function rN({point:e},t){return{point:e,delta:r$(e,rU(t)),offset:r$(e,t[0]),velocity:function(e,t){if(e.length<2)return{x:0,y:0};let n=e.length-1,r=null,i=rU(e);for(;n>=0&&(r=e[n],!(i.timestamp-r.timestamp>ni(.1)));)n--;if(!r)return{x:0,y:0};let o=(i.timestamp-r.timestamp)/1e3;if(0===o)return{x:0,y:0};let s={x:(i.x-r.x)/o,y:(i.y-r.y)/o};return s.x===1/0&&(s.x=0),s.y===1/0&&(s.y=0),s}(t,.1)}}function rU(e){return e[e.length-1]}function rB(e,t,n){return{min:void 0!==t?e.min+t:void 0,max:void 0!==n?e.max+n-(e.max-e.min):void 0}}function rZ(e,t){let n=t.min-e.min,r=t.max-e.max;return t.max-t.min<e.max-e.min&&([n,r]=[r,n]),{min:n,max:r}}function rW(e,t,n){return{min:rH(e,t),max:rH(e,n)}}function rH(e,t){return"number"==typeof e?e:e[t]||0}let rq=new WeakMap;class rG{constructor(e){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=tr(),this.latestPointerEvent=null,this.latestPanInfo=null,this.visualElement=e}start(e,{snapToCursor:t=!1,distanceThreshold:n}={}){let{presenceContext:r}=this.visualElement;if(r&&!1===r.isPresent)return;let{dragSnapToOrigin:i}=this.getProps();this.panSession=new rF(e,{onSessionStart:e=>{let{dragSnapToOrigin:n}=this.getProps();n?this.pauseAnimation():this.stopAnimation(),t&&this.snapToCursor(rP(e).point)},onStart:(e,t)=>{let{drag:n,dragPropagation:r,onDragStart:i}=this.getProps();if(n&&!r&&(this.openDragLock&&this.openDragLock(),this.openDragLock=function(e){if("x"===e||"y"===e)if(rE[e])return null;else return rE[e]=!0,()=>{rE[e]=!1};return rE.x||rE.y?null:(rE.x=rE.y=!0,()=>{rE.x=rE.y=!1})}(n),!this.openDragLock))return;this.latestPointerEvent=e,this.latestPanInfo=t,this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),rj(e=>{let t=this.getAxisMotionValue(e).get()||0;if(U.test(t)){let{projection:n}=this.visualElement;if(n&&n.layout){let r=n.layout.layoutBox[e];r&&(t=rM(r)*(parseFloat(t)/100))}}this.originPoint[e]=t}),i&&eu.postRender(()=>i(e,t)),nt(this.visualElement,"transform");let{animationState:o}=this.visualElement;o&&o.setActive("whileDrag",!0)},onMove:(e,t)=>{this.latestPointerEvent=e,this.latestPanInfo=t;let{dragPropagation:n,dragDirectionLock:r,onDirectionLock:i,onDrag:o}=this.getProps();if(!n&&!this.openDragLock)return;let{offset:s}=t;if(r&&null===this.currentDirection){this.currentDirection=function(e,t=10){let n=null;return Math.abs(e.y)>t?n="y":Math.abs(e.x)>t&&(n="x"),n}(s),null!==this.currentDirection&&i&&i(this.currentDirection);return}this.updateAxis("x",t.point,s),this.updateAxis("y",t.point,s),this.visualElement.render(),o&&o(e,t)},onSessionEnd:(e,t)=>{this.latestPointerEvent=e,this.latestPanInfo=t,this.stop(e,t),this.latestPointerEvent=null,this.latestPanInfo=null},resumeAnimation:()=>rj(e=>"paused"===this.getAnimationState(e)&&this.getAxisMotionValue(e).animation?.play())},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:i,distanceThreshold:n,contextWindow:rR(this.visualElement)})}stop(e,t){let n=e||this.latestPointerEvent,r=t||this.latestPanInfo,i=this.isDragging;if(this.cancel(),!i||!r||!n)return;let{velocity:o}=r;this.startAnimation(o);let{onDragEnd:s}=this.getProps();s&&eu.postRender(()=>s(n,r))}cancel(){this.isDragging=!1;let{projection:e,animationState:t}=this.visualElement;e&&(e.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:n}=this.getProps();!n&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),t&&t.setActive("whileDrag",!1)}updateAxis(e,t,n){let{drag:r}=this.getProps();if(!n||!rK(e,r,this.currentDirection))return;let i=this.getAxisMotionValue(e),o=this.originPoint[e]+n[e];this.constraints&&this.constraints[e]&&(o=function(e,{min:t,max:n},r){return void 0!==t&&e<t?e=r?A(t,e,r.min):Math.max(e,t):void 0!==n&&e>n&&(e=r?A(n,e,r.max):Math.min(e,n)),e}(o,this.constraints[e],this.elastic[e])),i.set(o)}resolveConstraints(){let{dragConstraints:e,dragElastic:t}=this.getProps(),n=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,r=this.constraints;e&&t5(e)?this.constraints||(this.constraints=this.resolveRefConstraints()):e&&n?this.constraints=function(e,{top:t,left:n,bottom:r,right:i}){return{x:rB(e.x,n,i),y:rB(e.y,t,r)}}(n.layoutBox,e):this.constraints=!1,this.elastic=function(e=.35){return!1===e?e=0:!0===e&&(e=.35),{x:rW(e,"left","right"),y:rW(e,"top","bottom")}}(t),r!==this.constraints&&n&&this.constraints&&!this.hasMutatedConstraints&&rj(e=>{!1!==this.constraints&&this.getAxisMotionValue(e)&&(this.constraints[e]=function(e,t){let n={};return void 0!==t.min&&(n.min=t.min-e.min),void 0!==t.max&&(n.max=t.max-e.min),n}(n.layoutBox[e],this.constraints[e]))})}resolveRefConstraints(){var e;let{dragConstraints:t,onMeasureDragConstraints:n}=this.getProps();if(!t||!t5(t))return!1;let r=t.current;J(null!==r,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.","drag-constraints-ref");let{projection:i}=this.visualElement;if(!i||!i.layout)return!1;let o=function(e,t,n){let r=I(e,n),{scroll:i}=t;return i&&(D(r.x,i.offset.x),D(r.y,i.offset.y)),r}(r,i.root,this.visualElement.getTransformPagePoint()),s=(e=i.layout.layoutBox,{x:rZ(e.x,o.x),y:rZ(e.y,o.y)});if(n){let e=n(function({x:e,y:t}){return{top:t.min,right:e.max,bottom:t.max,left:e.min}}(s));this.hasMutatedConstraints=!!e,e&&(s=k(e))}return s}startAnimation(e){let{drag:t,dragMomentum:n,dragElastic:r,dragTransition:i,dragSnapToOrigin:o,onDragTransitionEnd:s}=this.getProps(),a=this.constraints||{};return Promise.all(rj(s=>{if(!rK(s,t,this.currentDirection))return;let l=a&&a[s]||{};o&&(l={min:0,max:0});let u={type:"inertia",velocity:n?e[s]:0,bounceStiffness:r?200:1e6,bounceDamping:r?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...i,...l};return this.startAxisValueAnimation(s,u)})).then(s)}startAxisValueAnimation(e,t){let n=this.getAxisMotionValue(e);return nt(this.visualElement,e),n.start(rc(e,n,0,t,this.visualElement,!1))}stopAnimation(){rj(e=>this.getAxisMotionValue(e).stop())}pauseAnimation(){rj(e=>this.getAxisMotionValue(e).animation?.pause())}getAnimationState(e){return this.getAxisMotionValue(e).animation?.state}getAxisMotionValue(e){let t=`_drag${e.toUpperCase()}`,n=this.visualElement.getProps();return n[t]||this.visualElement.getValue(e,(n.initial?n.initial[e]:void 0)||0)}snapToCursor(e){rj(t=>{let{drag:n}=this.getProps();if(!rK(t,n,this.currentDirection))return;let{projection:r}=this.visualElement,i=this.getAxisMotionValue(t);if(r&&r.layout){let{min:n,max:o}=r.layout.layoutBox[t];i.set(e[t]-A(n,o,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:e,dragConstraints:t}=this.getProps(),{projection:n}=this.visualElement;if(!t5(t)||!n||!this.constraints)return;this.stopAnimation();let r={x:0,y:0};rj(e=>{let t=this.getAxisMotionValue(e);if(t&&!1!==this.constraints){let n=t.get();r[e]=function(e,t){let n=.5,r=rM(e),i=rM(t);return i>r?n=nq(t.min,t.max-r,e.min):r>i&&(n=nq(e.min,e.max-i,t.min)),R(0,1,n)}({min:n,max:n},this.constraints[e])}});let{transformTemplate:i}=this.visualElement.getProps();this.visualElement.current.style.transform=i?i({},""):"none",n.root&&n.root.updateScroll(),n.updateLayout(),this.resolveConstraints(),rj(t=>{if(!rK(t,e,null))return;let n=this.getAxisMotionValue(t),{min:i,max:o}=this.constraints[t];n.set(A(i,o,r[t]))})}addListeners(){if(!this.visualElement.current)return;rq.set(this.visualElement,this);let e=rT(this.visualElement.current,"pointerdown",e=>{let{drag:t,dragListener:n=!0}=this.getProps();t&&n&&this.start(e)}),t=()=>{let{dragConstraints:e}=this.getProps();t5(e)&&e.current&&(this.constraints=this.resolveRefConstraints())},{projection:n}=this.visualElement,r=n.addEventListener("measure",t);n&&!n.layout&&(n.root&&n.root.updateScroll(),n.updateLayout()),eu.read(t);let i=rS(window,"resize",()=>this.scalePositionWithinConstraints()),o=n.addEventListener("didUpdate",({delta:e,hasLayoutChanged:t})=>{this.isDragging&&t&&(rj(t=>{let n=this.getAxisMotionValue(t);n&&(this.originPoint[t]+=e[t].translate,n.set(n.get()+e[t].translate))}),this.visualElement.render())});return()=>{i(),e(),r(),o&&o()}}getProps(){let e=this.visualElement.getProps(),{drag:t=!1,dragDirectionLock:n=!1,dragPropagation:r=!1,dragConstraints:i=!1,dragElastic:o=.35,dragMomentum:s=!0}=e;return{...e,drag:t,dragDirectionLock:n,dragPropagation:r,dragConstraints:i,dragElastic:o,dragMomentum:s}}}function rK(e,t,n){return(!0===t||t===e)&&(null===n||n===e)}class rX extends rx{constructor(e){super(e),this.removeGroupControls=ei,this.removeListeners=ei,this.controls=new rG(e)}mount(){let{dragControls:e}=this.node.getProps();e&&(this.removeGroupControls=e.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||ei}unmount(){this.removeGroupControls(),this.removeListeners()}}let rJ=e=>(t,n)=>{e&&eu.postRender(()=>e(t,n))};class rY extends rx{constructor(){super(...arguments),this.removePointerDownListener=ei}onPointerDown(e){this.session=new rF(e,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:rR(this.node)})}createPanHandlers(){let{onPanSessionStart:e,onPanStart:t,onPan:n,onPanEnd:r}=this.node.getProps();return{onSessionStart:rJ(e),onStart:rJ(t),onMove:n,onEnd:(e,t)=>{delete this.session,r&&eu.postRender(()=>r(e,t))}}}mount(){this.removePointerDownListener=rT(this.node.current,"pointerdown",e=>this.onPointerDown(e))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}var rQ=n(2082);let r0={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function r1(e,t){return t.max===t.min?0:e/(t.max-t.min)*100}let r2={correct:(e,t)=>{if(!t.target)return e;if("string"==typeof e)if(!B.test(e))return e;else e=parseFloat(e);let n=r1(e,t.target.x),r=r1(e,t.target.y);return`${n}% ${r}%`}},r5=!1;class r9 extends i.Component{componentDidMount(){let{visualElement:e,layoutGroup:t,switchLayoutGroup:n,layoutId:r}=this.props,{projection:i}=e;for(let e in r3)tE[e]=r3[e],y(e)&&(tE[e].isCSSVariable=!0);i&&(t.group&&t.group.add(i),n&&n.register&&r&&n.register(i),r5&&i.root.didUpdate(),i.addEventListener("animationComplete",()=>{this.safeToRemove()}),i.setOptions({...i.options,onExitComplete:()=>this.safeToRemove()})),r0.hasEverUpdated=!0}getSnapshotBeforeUpdate(e){let{layoutDependency:t,visualElement:n,drag:r,isPresent:i}=this.props,{projection:o}=n;return o&&(o.isPresent=i,r5=!0,r||e.layoutDependency!==t||void 0===t||e.isPresent!==i?o.willUpdate():this.safeToRemove(),e.isPresent!==i&&(i?o.promote():o.relegate()||eu.postRender(()=>{let e=o.getStack();e&&e.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:e}=this.props.visualElement;e&&(e.root.didUpdate(),e6.postRender(()=>{!e.currentAnimation&&e.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:e,layoutGroup:t,switchLayoutGroup:n}=this.props,{projection:r}=e;r&&(r.scheduleCheckAfterUnmount(),t&&t.group&&t.group.remove(r),n&&n.deregister&&n.deregister(r))}safeToRemove(){let{safeToRemove:e}=this.props;e&&e()}render(){return null}}function r4(e){let[t,n]=(0,rQ.xQ)(),r=(0,i.useContext)(tL.L);return(0,tF.jsx)(r9,{...e,layoutGroup:r,switchLayoutGroup:(0,i.useContext)(t4),isPresent:t,safeToRemove:n})}let r3={borderRadius:{...r2,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:r2,borderTopRightRadius:r2,borderBottomLeftRadius:r2,borderBottomRightRadius:r2,boxShadow:{correct:(e,{treeScale:t,projectionDelta:n})=>{let r=eL.parse(e);if(r.length>5)return e;let i=eL.createTransformer(e),o=+("number"!=typeof r[0]),s=n.x.scale*t.x,a=n.y.scale*t.y;r[0+o]/=s,r[1+o]/=a;let l=A(s,a,.5);return"number"==typeof r[2+o]&&(r[2+o]/=l),"number"==typeof r[3+o]&&(r[3+o]/=l),i(r)}}};var r6=n(6983);function r8(e){return(0,r6.G)(e)&&"ownerSVGElement"in e}let r7=(e,t)=>e.depth-t.depth;class ie{constructor(){this.children=[],this.isDirty=!1}add(e){e0(this.children,e),this.isDirty=!0}remove(e){e1(this.children,e),this.isDirty=!0}forEach(e){this.isDirty&&this.children.sort(r7),this.isDirty=!1,this.children.forEach(e)}}let it=["TopLeft","TopRight","BottomLeft","BottomRight"],ir=it.length,ii=e=>"string"==typeof e?parseFloat(e):e,io=e=>"number"==typeof e||B.test(e);function is(e,t){return void 0!==e[t]?e[t]:e.borderRadius}let ia=iu(0,.5,nU),il=iu(.5,.95,ei);function iu(e,t,n){return r=>r<e?0:r>t?1:n(nq(e,t,r))}function ic(e,t){e.min=t.min,e.max=t.max}function id(e,t){ic(e.x,t.x),ic(e.y,t.y)}function ih(e,t){e.translate=t.translate,e.scale=t.scale,e.originPoint=t.originPoint,e.origin=t.origin}function ip(e,t,n,r,i){return e-=t,e=r+1/n*(e-r),void 0!==i&&(e=r+1/i*(e-r)),e}function im(e,t,[n,r,i],o,s){!function(e,t=0,n=1,r=.5,i,o=e,s=e){if(U.test(t)&&(t=parseFloat(t),t=A(s.min,s.max,t/100)-s.min),"number"!=typeof t)return;let a=A(o.min,o.max,r);e===o&&(a-=t),e.min=ip(e.min,t,n,a,i),e.max=ip(e.max,t,n,a,i)}(e,t[n],t[r],t[i],t.scale,o,s)}let ig=["x","scaleX","originX"],iv=["y","scaleY","originY"];function iy(e,t,n,r){im(e.x,t,ig,n?n.x:void 0,r?r.x:void 0),im(e.y,t,iv,n?n.y:void 0,r?r.y:void 0)}function ib(e){return 0===e.translate&&1===e.scale}function ix(e){return ib(e.x)&&ib(e.y)}function iw(e,t){return e.min===t.min&&e.max===t.max}function ik(e,t){return Math.round(e.min)===Math.round(t.min)&&Math.round(e.max)===Math.round(t.max)}function iA(e,t){return ik(e.x,t.x)&&ik(e.y,t.y)}function iE(e){return rM(e.x)/rM(e.y)}function iS(e,t){return e.translate===t.translate&&e.scale===t.scale&&e.originPoint===t.originPoint}class i_{constructor(){this.members=[]}add(e){e0(this.members,e),e.scheduleRender()}remove(e){if(e1(this.members,e),e===this.prevLead&&(this.prevLead=void 0),e===this.lead){let e=this.members[this.members.length-1];e&&this.promote(e)}}relegate(e){let t,n=this.members.findIndex(t=>e===t);if(0===n)return!1;for(let e=n;e>=0;e--){let n=this.members[e];if(!1!==n.isPresent){t=n;break}}return!!t&&(this.promote(t),!0)}promote(e,t){let n=this.lead;if(e!==n&&(this.prevLead=n,this.lead=e,e.show(),n)){n.instance&&n.scheduleRender(),e.scheduleRender(),e.resumeFrom=n,t&&(e.resumeFrom.preserveOpacity=!0),n.snapshot&&(e.snapshot=n.snapshot,e.snapshot.latestValues=n.animationValues||n.latestValues),e.root&&e.root.isUpdating&&(e.isLayoutDirty=!0);let{crossfade:r}=e.options;!1===r&&n.hide()}}exitAnimationComplete(){this.members.forEach(e=>{let{options:t,resumingFrom:n}=e;t.onExitComplete&&t.onExitComplete(),n&&n.options.onExitComplete&&n.options.onExitComplete()})}scheduleRender(){this.members.forEach(e=>{e.instance&&e.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}let iP={nodes:0,calculatedTargetDeltas:0,calculatedProjections:0},iT=["","X","Y","Z"],iM=0;function iC(e,t,n,r){let{latestValues:i}=t;i[e]&&(n[e]=i[e],t.setStaticValue(e,0),r&&(r[e]=0))}function iD({attachResizeListener:e,defaultParent:t,measureScroll:n,checkIsScrollRoot:r,resetTransform:i}){return class{constructor(e={},n=t?.()){this.id=iM++,this.animationId=0,this.animationCommitId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,ea.value&&(iP.nodes=iP.calculatedTargetDeltas=iP.calculatedProjections=0),this.nodes.forEach(iI),this.nodes.forEach(iN),this.nodes.forEach(iU),this.nodes.forEach(ij),ea.addProjectionMetrics&&ea.addProjectionMetrics(iP)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=e,this.root=n?n.root||n:this,this.path=n?[...n.path,n]:[],this.parent=n,this.depth=n?n.depth+1:0;for(let e=0;e<this.path.length;e++)this.path[e].shouldResetTransform=!0;this.root===this&&(this.nodes=new ie)}addEventListener(e,t){return this.eventHandlers.has(e)||this.eventHandlers.set(e,new e2),this.eventHandlers.get(e).add(t)}notifyListeners(e,...t){let n=this.eventHandlers.get(e);n&&n.notify(...t)}hasListeners(e){return this.eventHandlers.has(e)}mount(t){if(this.instance)return;this.isSVG=r8(t)&&!(r8(t)&&"svg"===t.tagName),this.instance=t;let{layoutId:n,layout:r,visualElement:i}=this.options;if(i&&!i.current&&i.mount(t),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(r||n)&&(this.isLayoutDirty=!0),e){let n,r=0,i=()=>this.root.updateBlockedByResize=!1;eu.read(()=>{r=window.innerWidth}),e(t,()=>{let e=window.innerWidth;e!==r&&(r=e,this.root.updateBlockedByResize=!0,n&&n(),n=function(e,t){let n=eQ.now(),r=({timestamp:t})=>{let i=t-n;i>=250&&(ec(r),e(i-250))};return eu.setup(r,!0),()=>ec(r)}(i,250),r0.hasAnimatedSinceResize&&(r0.hasAnimatedSinceResize=!1,this.nodes.forEach(i$)))})}n&&this.root.registerSharedNode(n,this),!1!==this.options.animate&&i&&(n||r)&&this.addEventListener("didUpdate",({delta:e,hasLayoutChanged:t,hasRelativeLayoutChanged:n,layout:r})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let o=this.options.transition||i.getDefaultTransition()||iG,{onLayoutAnimationStart:s,onLayoutAnimationComplete:a}=i.getProps(),l=!this.targetLayout||!iA(this.targetLayout,r),u=!t&&n;if(this.options.layoutRoot||this.resumeFrom||u||t&&(l||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);let t={...t7(o,"layout"),onPlay:s,onComplete:a};(i.shouldReduceMotion||this.options.layoutRoot)&&(t.delay=0,t.type=!1),this.startAnimation(t),this.setAnimationOrigin(e,u)}else t||i$(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=r})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let e=this.getStack();e&&e.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),ec(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(iB),this.animationId++)}getTransformTemplate(){let{visualElement:e}=this.options;return e&&e.getProps().transformTemplate}willUpdate(e=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function e(t){if(t.hasCheckedOptimisedAppear=!0,t.root===t)return;let{visualElement:n}=t.options;if(!n)return;let r=n.props[t9];if(window.MotionHasOptimisedAnimation(r,"transform")){let{layout:e,layoutId:n}=t.options;window.MotionCancelOptimisedAnimation(r,"transform",eu,!(e||n))}let{parent:i}=t;i&&!i.hasCheckedOptimisedAppear&&e(i)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let e=0;e<this.path.length;e++){let t=this.path[e];t.shouldResetTransform=!0,t.updateScroll("snapshot"),t.options.layoutRoot&&t.willUpdate(!1)}let{layoutId:t,layout:n}=this.options;if(void 0===t&&!n)return;let r=this.getTransformTemplate();this.prevTransformTemplateValue=r?r(this.latestValues,""):void 0,this.updateSnapshot(),e&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(iO);return}if(this.animationId<=this.animationCommitId)return void this.nodes.forEach(iF);this.animationCommitId=this.animationId,this.isUpdating?(this.isUpdating=!1,this.nodes.forEach(iL),this.nodes.forEach(iV),this.nodes.forEach(iz)):this.nodes.forEach(iF),this.clearAllSnapshots();let e=eQ.now();ed.delta=R(0,1e3/60,e-ed.timestamp),ed.timestamp=e,ed.isProcessing=!0,eh.update.process(ed),eh.preRender.process(ed),eh.render.process(ed),ed.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,e6.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(iR),this.sharedNodes.forEach(iZ)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,eu.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){eu.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure(),!this.snapshot||rM(this.snapshot.measuredBox.x)||rM(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let e=0;e<this.path.length;e++)this.path[e].updateScroll();let e=this.layout;this.layout=this.measure(!1),this.layoutCorrected=tr(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:t}=this.options;t&&t.notify("LayoutMeasure",this.layout.layoutBox,e?e.layoutBox:void 0)}updateScroll(e="measure"){let t=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===e&&(t=!1),t&&this.instance){let t=r(this.instance);this.scroll={animationId:this.root.animationId,phase:e,isRoot:t,offset:n(this.instance),wasRoot:this.scroll?this.scroll.isRoot:t}}}resetTransform(){if(!i)return;let e=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,t=this.projectionDelta&&!ix(this.projectionDelta),n=this.getTransformTemplate(),r=n?n(this.latestValues,""):void 0,o=r!==this.prevTransformTemplateValue;e&&this.instance&&(t||_(this.latestValues)||o)&&(i(this.instance,r),this.shouldResetTransform=!1,this.scheduleRender())}measure(e=!0){var t;let n=this.measurePageBox(),r=this.removeElementScroll(n);return e&&(r=this.removeTransform(r)),iJ((t=r).x),iJ(t.y),{animationId:this.root.animationId,measuredBox:n,layoutBox:r,latestValues:{},source:this.id}}measurePageBox(){let{visualElement:e}=this.options;if(!e)return tr();let t=e.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(iQ))){let{scroll:e}=this.root;e&&(D(t.x,e.offset.x),D(t.y,e.offset.y))}return t}removeElementScroll(e){let t=tr();if(id(t,e),this.scroll?.wasRoot)return t;for(let n=0;n<this.path.length;n++){let r=this.path[n],{scroll:i,options:o}=r;r!==this.root&&i&&o.layoutScroll&&(i.wasRoot&&id(t,e),D(t.x,i.offset.x),D(t.y,i.offset.y))}return t}applyTransform(e,t=!1){let n=tr();id(n,e);for(let e=0;e<this.path.length;e++){let r=this.path[e];!t&&r.options.layoutScroll&&r.scroll&&r!==r.root&&z(n,{x:-r.scroll.offset.x,y:-r.scroll.offset.y}),_(r.latestValues)&&z(n,r.latestValues)}return _(this.latestValues)&&z(n,this.latestValues),n}removeTransform(e){let t=tr();id(t,e);for(let e=0;e<this.path.length;e++){let n=this.path[e];if(!n.instance||!_(n.latestValues))continue;S(n.latestValues)&&n.updateSnapshot();let r=tr();id(r,n.measurePageBox()),iy(t,n.latestValues,n.snapshot?n.snapshot.layoutBox:void 0,r)}return _(this.latestValues)&&iy(t,this.latestValues),t}setTargetDelta(e){this.targetDelta=e,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(e){this.options={...this.options,...e,crossfade:void 0===e.crossfade||e.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==ed.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(e=!1){let t=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=t.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=t.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=t.isSharedProjectionDirty);let n=!!this.resumingFrom||this!==t;if(!(e||n&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:r,layoutId:i}=this.options;if(this.layout&&(r||i)){if(this.resolvedRelativeTargetAt=ed.timestamp,!this.targetDelta&&!this.relativeTarget){let e=this.getClosestProjectingParent();e&&e.layout&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget=tr(),this.relativeTargetOrigin=tr(),rI(this.relativeTargetOrigin,this.layout.layoutBox,e.layout.layoutBox),id(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if(this.target||(this.target=tr(),this.targetWithTransforms=tr()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target){var o,s,a;this.forceRelativeParentToResolveTarget(),o=this.target,s=this.relativeTarget,a=this.relativeParent.target,rV(o.x,s.x,a.x),rV(o.y,s.y,a.y)}else this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):id(this.target,this.layout.layoutBox),C(this.target,this.targetDelta)):id(this.target,this.layout.layoutBox);if(this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let e=this.getClosestProjectingParent();e&&!!e.resumingFrom==!!this.resumingFrom&&!e.options.layoutScroll&&e.target&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget=tr(),this.relativeTargetOrigin=tr(),rI(this.relativeTargetOrigin,this.target,e.target),id(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}ea.value&&iP.calculatedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||S(this.parent.latestValues)||P(this.parent.latestValues)))if(this.parent.isProjecting())return this.parent;else return this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){let e=this.getLead(),t=!!this.resumingFrom||this!==e,n=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(n=!1),t&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(n=!1),this.resolvedRelativeTargetAt===ed.timestamp&&(n=!1),n)return;let{layout:r,layoutId:i}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(r||i))return;id(this.layoutCorrected,this.layout.layoutBox);let o=this.treeScale.x,s=this.treeScale.y;!function(e,t,n,r=!1){let i,o,s=n.length;if(s){t.x=t.y=1;for(let a=0;a<s;a++){o=(i=n[a]).projectionDelta;let{visualElement:s}=i.options;(!s||!s.props.style||"contents"!==s.props.style.display)&&(r&&i.options.layoutScroll&&i.scroll&&i!==i.root&&z(e,{x:-i.scroll.offset.x,y:-i.scroll.offset.y}),o&&(t.x*=o.x.scale,t.y*=o.y.scale,C(e,o)),r&&_(i.latestValues)&&z(e,i.latestValues))}t.x<1.0000000000001&&t.x>.999999999999&&(t.x=1),t.y<1.0000000000001&&t.y>.999999999999&&(t.y=1)}}(this.layoutCorrected,this.treeScale,this.path,t),e.layout&&!e.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(e.target=e.layout.layoutBox,e.targetWithTransforms=tr());let{target:a}=e;if(!a){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(ih(this.prevProjectionDelta.x,this.projectionDelta.x),ih(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),rD(this.projectionDelta,this.layoutCorrected,a,this.latestValues),this.treeScale.x===o&&this.treeScale.y===s&&iS(this.projectionDelta.x,this.prevProjectionDelta.x)&&iS(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",a)),ea.value&&iP.calculatedProjections++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(e=!0){if(this.options.visualElement?.scheduleRender(),e){let e=this.getStack();e&&e.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=tt(),this.projectionDelta=tt(),this.projectionDeltaWithTransform=tt()}setAnimationOrigin(e,t=!1){let n,r=this.snapshot,i=r?r.latestValues:{},o={...this.latestValues},s=tt();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!t;let a=tr(),l=(r?r.source:void 0)!==(this.layout?this.layout.source:void 0),u=this.getStack(),c=!u||u.members.length<=1,d=!!(l&&!c&&!0===this.options.crossfade&&!this.path.some(iq));this.animationProgress=0,this.mixTargetDelta=t=>{let r=t/1e3;if(iW(s.x,e.x,r),iW(s.y,e.y,r),this.setTargetDelta(s),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var u,h,f,p,m,g;rI(a,this.layout.layoutBox,this.relativeParent.layout.layoutBox),f=this.relativeTarget,p=this.relativeTargetOrigin,m=a,g=r,iH(f.x,p.x,m.x,g),iH(f.y,p.y,m.y,g),n&&(u=this.relativeTarget,h=n,iw(u.x,h.x)&&iw(u.y,h.y))&&(this.isProjectionDirty=!1),n||(n=tr()),id(n,this.relativeTarget)}l&&(this.animationValues=o,function(e,t,n,r,i,o){i?(e.opacity=A(0,n.opacity??1,ia(r)),e.opacityExit=A(t.opacity??1,0,il(r))):o&&(e.opacity=A(t.opacity??1,n.opacity??1,r));for(let i=0;i<ir;i++){let o=`border${it[i]}Radius`,s=is(t,o),a=is(n,o);(void 0!==s||void 0!==a)&&(s||(s=0),a||(a=0),0===s||0===a||io(s)===io(a)?(e[o]=Math.max(A(ii(s),ii(a),r),0),(U.test(a)||U.test(s))&&(e[o]+="%")):e[o]=a)}(t.rotate||n.rotate)&&(e.rotate=A(t.rotate||0,n.rotate||0,r))}(o,i,this.latestValues,r,d,c)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=r},this.mixTargetDelta(1e3*!!this.options.layoutRoot)}startAnimation(e){this.notifyListeners("animationStart"),this.currentAnimation?.stop(),this.resumingFrom?.currentAnimation?.stop(),this.pendingAnimation&&(ec(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=eu.update(()=>{r0.hasAnimatedSinceResize=!0,no.layout++,this.motionValue||(this.motionValue=e4(0)),this.currentAnimation=function(e,t,n){let r=eJ(e)?e:e4(e);return r.start(rc("",r,t,n)),r.animation}(this.motionValue,[0,1e3],{...e,velocity:0,isSync:!0,onUpdate:t=>{this.mixTargetDelta(t),e.onUpdate&&e.onUpdate(t)},onStop:()=>{no.layout--},onComplete:()=>{no.layout--,e.onComplete&&e.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let e=this.getStack();e&&e.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let e=this.getLead(),{targetWithTransforms:t,target:n,layout:r,latestValues:i}=e;if(t&&n&&r){if(this!==e&&this.layout&&r&&iY(this.options.animationType,this.layout.layoutBox,r.layoutBox)){n=this.target||tr();let t=rM(this.layout.layoutBox.x);n.x.min=e.target.x.min,n.x.max=n.x.min+t;let r=rM(this.layout.layoutBox.y);n.y.min=e.target.y.min,n.y.max=n.y.min+r}id(t,n),z(t,i),rD(this.projectionDeltaWithTransform,this.layoutCorrected,t,i)}}registerSharedNode(e,t){this.sharedNodes.has(e)||this.sharedNodes.set(e,new i_),this.sharedNodes.get(e).add(t);let n=t.options.initialPromotionConfig;t.promote({transition:n?n.transition:void 0,preserveFollowOpacity:n&&n.shouldPreserveFollowOpacity?n.shouldPreserveFollowOpacity(t):void 0})}isLead(){let e=this.getStack();return!e||e.lead===this}getLead(){let{layoutId:e}=this.options;return e&&this.getStack()?.lead||this}getPrevLead(){let{layoutId:e}=this.options;return e?this.getStack()?.prevLead:void 0}getStack(){let{layoutId:e}=this.options;if(e)return this.root.sharedNodes.get(e)}promote({needsReset:e,transition:t,preserveFollowOpacity:n}={}){let r=this.getStack();r&&r.promote(this,n),e&&(this.projectionDelta=void 0,this.needsReset=!0),t&&this.setOptions({transition:t})}relegate(){let e=this.getStack();return!!e&&e.relegate(this)}resetSkewAndRotation(){let{visualElement:e}=this.options;if(!e)return;let t=!1,{latestValues:n}=e;if((n.z||n.rotate||n.rotateX||n.rotateY||n.rotateZ||n.skewX||n.skewY)&&(t=!0),!t)return;let r={};n.z&&iC("z",e,r,this.animationValues);for(let t=0;t<iT.length;t++)iC(`rotate${iT[t]}`,e,r,this.animationValues),iC(`skew${iT[t]}`,e,r,this.animationValues);for(let t in e.render(),r)e.setStaticValue(t,r[t]),this.animationValues&&(this.animationValues[t]=r[t]);e.scheduleRender()}applyProjectionStyles(e,t){if(!this.instance||this.isSVG)return;if(!this.isVisible){e.visibility="hidden";return}let n=this.getTransformTemplate();if(this.needsReset){this.needsReset=!1,e.visibility="",e.opacity="",e.pointerEvents=tY(t?.pointerEvents)||"",e.transform=n?n(this.latestValues,""):"none";return}let r=this.getLead();if(!this.projectionDelta||!this.layout||!r.target){this.options.layoutId&&(e.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,e.pointerEvents=tY(t?.pointerEvents)||""),this.hasProjected&&!_(this.latestValues)&&(e.transform=n?n({},""):"none",this.hasProjected=!1);return}e.visibility="";let i=r.animationValues||r.latestValues;this.applyTransformsToTarget();let o=function(e,t,n){let r="",i=e.x.translate/t.x,o=e.y.translate/t.y,s=n?.z||0;if((i||o||s)&&(r=`translate3d(${i}px, ${o}px, ${s}px) `),(1!==t.x||1!==t.y)&&(r+=`scale(${1/t.x}, ${1/t.y}) `),n){let{transformPerspective:e,rotate:t,rotateX:i,rotateY:o,skewX:s,skewY:a}=n;e&&(r=`perspective(${e}px) ${r}`),t&&(r+=`rotate(${t}deg) `),i&&(r+=`rotateX(${i}deg) `),o&&(r+=`rotateY(${o}deg) `),s&&(r+=`skewX(${s}deg) `),a&&(r+=`skewY(${a}deg) `)}let a=e.x.scale*t.x,l=e.y.scale*t.y;return(1!==a||1!==l)&&(r+=`scale(${a}, ${l})`),r||"none"}(this.projectionDeltaWithTransform,this.treeScale,i);n&&(o=n(i,o)),e.transform=o;let{x:s,y:a}=this.projectionDelta;for(let t in e.transformOrigin=`${100*s.origin}% ${100*a.origin}% 0`,r.animationValues?e.opacity=r===this?i.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:i.opacityExit:e.opacity=r===this?void 0!==i.opacity?i.opacity:"":void 0!==i.opacityExit?i.opacityExit:0,tE){if(void 0===i[t])continue;let{correct:n,applyTo:s,isCSSVariable:a}=tE[t],l="none"===o?i[t]:n(i[t],r);if(s){let t=s.length;for(let n=0;n<t;n++)e[s[n]]=l}else a?this.options.visualElement.renderState.vars[t]=l:e[t]=l}this.options.layoutId&&(e.pointerEvents=r===this?tY(t?.pointerEvents)||"":"none")}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(e=>e.currentAnimation?.stop()),this.root.nodes.forEach(iO),this.root.sharedNodes.clear()}}}function iV(e){e.updateLayout()}function iz(e){let t=e.resumeFrom?.snapshot||e.snapshot;if(e.isLead()&&e.layout&&t&&e.hasListeners("didUpdate")){let{layoutBox:n,measuredBox:r}=e.layout,{animationType:i}=e.options,o=t.source!==e.layout.source;"size"===i?rj(e=>{let r=o?t.measuredBox[e]:t.layoutBox[e],i=rM(r);r.min=n[e].min,r.max=r.min+i}):iY(i,t.layoutBox,n)&&rj(r=>{let i=o?t.measuredBox[r]:t.layoutBox[r],s=rM(n[r]);i.max=i.min+s,e.relativeTarget&&!e.currentAnimation&&(e.isProjectionDirty=!0,e.relativeTarget[r].max=e.relativeTarget[r].min+s)});let s=tt();rD(s,n,t.layoutBox);let a=tt();o?rD(a,e.applyTransform(r,!0),t.measuredBox):rD(a,n,t.layoutBox);let l=!ix(s),u=!1;if(!e.resumeFrom){let r=e.getClosestProjectingParent();if(r&&!r.resumeFrom){let{snapshot:i,layout:o}=r;if(i&&o){let s=tr();rI(s,t.layoutBox,i.layoutBox);let a=tr();rI(a,n,o.layoutBox),iA(s,a)||(u=!0),r.options.layoutRoot&&(e.relativeTarget=a,e.relativeTargetOrigin=s,e.relativeParent=r)}}}e.notifyListeners("didUpdate",{layout:n,snapshot:t,delta:a,layoutDelta:s,hasLayoutChanged:l,hasRelativeLayoutChanged:u})}else if(e.isLead()){let{onExitComplete:t}=e.options;t&&t()}e.options.transition=void 0}function iI(e){ea.value&&iP.nodes++,e.parent&&(e.isProjecting()||(e.isProjectionDirty=e.parent.isProjectionDirty),e.isSharedProjectionDirty||(e.isSharedProjectionDirty=!!(e.isProjectionDirty||e.parent.isProjectionDirty||e.parent.isSharedProjectionDirty)),e.isTransformDirty||(e.isTransformDirty=e.parent.isTransformDirty))}function ij(e){e.isProjectionDirty=e.isSharedProjectionDirty=e.isTransformDirty=!1}function iR(e){e.clearSnapshot()}function iO(e){e.clearMeasurements()}function iF(e){e.isLayoutDirty=!1}function iL(e){let{visualElement:t}=e.options;t&&t.getProps().onBeforeLayoutMeasure&&t.notify("BeforeLayoutMeasure"),e.resetTransform()}function i$(e){e.finishAnimation(),e.targetDelta=e.relativeTarget=e.target=void 0,e.isProjectionDirty=!0}function iN(e){e.resolveTargetDelta()}function iU(e){e.calcProjection()}function iB(e){e.resetSkewAndRotation()}function iZ(e){e.removeLeadSnapshot()}function iW(e,t,n){e.translate=A(t.translate,0,n),e.scale=A(t.scale,1,n),e.origin=t.origin,e.originPoint=t.originPoint}function iH(e,t,n,r){e.min=A(t.min,n.min,r),e.max=A(t.max,n.max,r)}function iq(e){return e.animationValues&&void 0!==e.animationValues.opacityExit}let iG={duration:.45,ease:[.4,0,.1,1]},iK=e=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(e),iX=iK("applewebkit/")&&!iK("chrome/")?Math.round:ei;function iJ(e){e.min=iX(e.min),e.max=iX(e.max)}function iY(e,t,n){return"position"===e||"preserve-aspect"===e&&!(.2>=Math.abs(iE(t)-iE(n)))}function iQ(e){return e!==e.root&&e.scroll?.wasRoot}let i0=iD({attachResizeListener:(e,t)=>rS(e,"resize",t),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),i1={current:void 0},i2=iD({measureScroll:e=>({x:e.scrollLeft,y:e.scrollTop}),defaultParent:()=>{if(!i1.current){let e=new i0({});e.mount(window),e.setOptions({layoutScroll:!0}),i1.current=e}return i1.current},resetTransform:(e,t)=>{e.style.transform=void 0!==t?t:"none"},checkIsScrollRoot:e=>"fixed"===window.getComputedStyle(e).position});function i5(e,t){let n=function(e,t,n){if(e instanceof EventTarget)return[e];if("string"==typeof e){let t=document,n=(void 0)??t.querySelectorAll(e);return n?Array.from(n):[]}return Array.from(e)}(e),r=new AbortController;return[n,{passive:!0,...t,signal:r.signal},()=>r.abort()]}function i9(e){return!("touch"===e.pointerType||rE.x||rE.y)}function i4(e,t,n){let{props:r}=e;e.animationState&&r.whileHover&&e.animationState.setActive("whileHover","Start"===n);let i=r["onHover"+n];i&&eu.postRender(()=>i(t,rP(t)))}class i3 extends rx{mount(){let{current:e}=this.node;e&&(this.unmount=function(e,t,n={}){let[r,i,o]=i5(e,n),s=e=>{if(!i9(e))return;let{target:n}=e,r=t(n,e);if("function"!=typeof r||!n)return;let o=e=>{i9(e)&&(r(e),n.removeEventListener("pointerleave",o))};n.addEventListener("pointerleave",o,i)};return r.forEach(e=>{e.addEventListener("pointerenter",s,i)}),o}(e,(e,t)=>(i4(this.node,t,"Start"),e=>i4(this.node,e,"End"))))}unmount(){}}class i6 extends rx{constructor(){super(...arguments),this.isActive=!1}onFocus(){let e=!1;try{e=this.node.current.matches(":focus-visible")}catch(t){e=!0}e&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=nr(rS(this.node.current,"focus",()=>this.onFocus()),rS(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}var i8=n(7351);let i7=(e,t)=>!!t&&(e===t||i7(e,t.parentElement)),oe=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),ot=new WeakSet;function on(e){return t=>{"Enter"===t.key&&e(t)}}function or(e,t){e.dispatchEvent(new PointerEvent("pointer"+t,{isPrimary:!0,bubbles:!0}))}function oi(e){return r_(e)&&!(rE.x||rE.y)}function oo(e,t,n){let{props:r}=e;if(e.current instanceof HTMLButtonElement&&e.current.disabled)return;e.animationState&&r.whileTap&&e.animationState.setActive("whileTap","Start"===n);let i=r["onTap"+("End"===n?"":n)];i&&eu.postRender(()=>i(t,rP(t)))}class os extends rx{mount(){let{current:e}=this.node;e&&(this.unmount=function(e,t,n={}){let[r,i,o]=i5(e,n),s=e=>{let r=e.currentTarget;if(!oi(e))return;ot.add(r);let o=t(r,e),s=(e,t)=>{window.removeEventListener("pointerup",a),window.removeEventListener("pointercancel",l),ot.has(r)&&ot.delete(r),oi(e)&&"function"==typeof o&&o(e,{success:t})},a=e=>{s(e,r===window||r===document||n.useGlobalTarget||i7(r,e.target))},l=e=>{s(e,!1)};window.addEventListener("pointerup",a,i),window.addEventListener("pointercancel",l,i)};return r.forEach(e=>{((n.useGlobalTarget?window:e).addEventListener("pointerdown",s,i),(0,i8.s)(e))&&(e.addEventListener("focus",e=>((e,t)=>{let n=e.currentTarget;if(!n)return;let r=on(()=>{if(ot.has(n))return;or(n,"down");let e=on(()=>{or(n,"up")});n.addEventListener("keyup",e,t),n.addEventListener("blur",()=>or(n,"cancel"),t)});n.addEventListener("keydown",r,t),n.addEventListener("blur",()=>n.removeEventListener("keydown",r),t)})(e,i)),oe.has(e.tagName)||-1!==e.tabIndex||e.hasAttribute("tabindex")||(e.tabIndex=0))}),o}(e,(e,t)=>(oo(this.node,t,"Start"),(e,{success:t})=>oo(this.node,e,t?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}let oa=new WeakMap,ol=new WeakMap,ou=e=>{let t=oa.get(e.target);t&&t(e)},oc=e=>{e.forEach(ou)},od={some:0,all:1};class oh extends rx{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:e={}}=this.node.getProps(),{root:t,margin:n,amount:r="some",once:i}=e,o={root:t?t.current:void 0,rootMargin:n,threshold:"number"==typeof r?r:od[r]};return function(e,t,n){let r=function({root:e,...t}){let n=e||document;ol.has(n)||ol.set(n,{});let r=ol.get(n),i=JSON.stringify(t);return r[i]||(r[i]=new IntersectionObserver(oc,{root:e,...t})),r[i]}(t);return oa.set(e,n),r.observe(e),()=>{oa.delete(e),r.unobserve(e)}}(this.node.current,o,e=>{let{isIntersecting:t}=e;if(this.isInView===t||(this.isInView=t,i&&!t&&this.hasEnteredView))return;t&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",t);let{onViewportEnter:n,onViewportLeave:r}=this.node.getProps(),o=t?n:r;o&&o(e)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:e,prevProps:t}=this.node;["amount","margin","root"].some(function({viewport:e={}},{viewport:t={}}={}){return n=>e[n]!==t[n]}(e,t))&&this.startObserver()}unmount(){}}let of=function(e,t){if("undefined"==typeof Proxy)return t6;let n=new Map,r=(n,r)=>t6(n,r,e,t);return new Proxy((e,t)=>r(e,t),{get:(i,o)=>"create"===o?r:(n.has(o)||n.set(o,t6(o,void 0,e,t)),n.get(o))})}({animation:{Feature:rw},exit:{Feature:rA},inView:{Feature:oh},tap:{Feature:os},focus:{Feature:i6},hover:{Feature:i3},pan:{Feature:rY},drag:{Feature:rX,ProjectionNode:i2,MeasureLayout:r4},layout:{ProjectionNode:i2,MeasureLayout:r4}},(e,t)=>tO(e)?new tj(t):new tP(t,{allowProjection:e!==i.Fragment}))},2664:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isLocalURL",{enumerable:!0,get:function(){return o}});let r=n(9991),i=n(7102);function o(e){if(!(0,r.isAbsoluteUrl)(e))return!0;try{let t=(0,r.getLocationOrigin)(),n=new URL(e,t);return n.origin===t&&(0,i.hasBasePath)(n.pathname)}catch(e){return!1}}},2757:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{formatUrl:function(){return o},formatWithValidation:function(){return a},urlObjectKeys:function(){return s}});let r=n(6966)._(n(8859)),i=/https?|ftp|gopher|file/;function o(e){let{auth:t,hostname:n}=e,o=e.protocol||"",s=e.pathname||"",a=e.hash||"",l=e.query||"",u=!1;t=t?encodeURIComponent(t).replace(/%3A/i,":")+"@":"",e.host?u=t+e.host:n&&(u=t+(~n.indexOf(":")?"["+n+"]":n),e.port&&(u+=":"+e.port)),l&&"object"==typeof l&&(l=String(r.urlQueryToSearchParams(l)));let c=e.search||l&&"?"+l||"";return o&&!o.endsWith(":")&&(o+=":"),e.slashes||(!o||i.test(o))&&!1!==u?(u="//"+(u||""),s&&"/"!==s[0]&&(s="/"+s)):u||(u=""),a&&"#"!==a[0]&&(a="#"+a),c&&"?"!==c[0]&&(c="?"+c),""+o+u+(s=s.replace(/[?#]/g,encodeURIComponent))+(c=c.replace("#","%23"))+a}let s=["auth","hash","host","hostname","href","path","pathname","port","protocol","query","search","slashes"];function a(e){return o(e)}},2885:(e,t,n)=>{n.d(t,{M:()=>i});var r=n(2115);function i(e){let t=(0,r.useRef)(null);return null===t.current&&(t.current=e()),t.current}},3180:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"errorOnce",{enumerable:!0,get:function(){return n}});let n=e=>{}},3384:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("leaf",[["path",{d:"M11 20A7 7 0 0 1 9.8 6.1C15.5 5 17 4.48 19 2c1 2 2 4.18 2 8 0 5.5-4.78 10-10 10Z",key:"nnexq3"}],["path",{d:"M2 21c0-3 1.85-5.36 5.08-6C9.5 14.52 12 13 13 12",key:"mt58a7"}]])},3655:(e,t,n)=>{n.d(t,{hO:()=>l,sG:()=>a});var r=n(2115),i=n(7650),o=n(9708),s=n(5155),a=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=(0,o.TL)(`Primitive.${t}`),i=r.forwardRef((e,r)=>{let{asChild:i,...o}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,s.jsx)(i?n:t,{...o,ref:r})});return i.displayName=`Primitive.${t}`,{...e,[t]:i}},{});function l(e,t){e&&i.flushSync(()=>e.dispatchEvent(t))}},3793:(e,t,n)=>{n.d(t,{JM:()=>l,Kd:()=>a,Wk:()=>u,a$:()=>s});var r=n(4193),i=n(4398);let o=(e,t)=>{e.name="$ZodError",Object.defineProperty(e,"_zod",{value:e._zod,enumerable:!1}),Object.defineProperty(e,"issues",{value:t,enumerable:!1}),Object.defineProperty(e,"message",{get:()=>JSON.stringify(t,i.k8,2),enumerable:!0}),Object.defineProperty(e,"toString",{value:()=>e.message,enumerable:!1})},s=(0,r.xI)("$ZodError",o),a=(0,r.xI)("$ZodError",o,{Parent:Error});function l(e,t=e=>e.message){let n={},r=[];for(let i of e.issues)i.path.length>0?(n[i.path[0]]=n[i.path[0]]||[],n[i.path[0]].push(t(i))):r.push(t(i));return{formErrors:r,fieldErrors:n}}function u(e,t){let n=t||function(e){return e.message},r={_errors:[]},i=e=>{for(let t of e.issues)if("invalid_union"===t.code&&t.errors.length)t.errors.map(e=>i({issues:e}));else if("invalid_key"===t.code)i({issues:t.issues});else if("invalid_element"===t.code)i({issues:t.issues});else if(0===t.path.length)r._errors.push(n(t));else{let e=r,i=0;for(;i<t.path.length;){let r=t.path[i];i===t.path.length-1?(e[r]=e[r]||{_errors:[]},e[r]._errors.push(n(t))):e[r]=e[r]||{_errors:[]},e=e[r],i++}}};return i(e),r}},4186:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},4193:(e,t,n)=>{function r(e,t,n){function r(n,r){var i;for(let o in Object.defineProperty(n,"_zod",{value:n._zod??{},enumerable:!1}),(i=n._zod).traits??(i.traits=new Set),n._zod.traits.add(e),t(n,r),s.prototype)o in n||Object.defineProperty(n,o,{value:s.prototype[o].bind(n)});n._zod.constr=s,n._zod.def=r}let i=n?.Parent??Object;class o extends i{}function s(e){var t;let i=n?.Parent?new o:this;for(let n of(r(i,e),(t=i._zod).deferred??(t.deferred=[]),i._zod.deferred))n();return i}return Object.defineProperty(o,"name",{value:e}),Object.defineProperty(s,"init",{value:r}),Object.defineProperty(s,Symbol.hasInstance,{value:t=>!!n?.Parent&&t instanceof n.Parent||t?._zod?.traits?.has(e)}),Object.defineProperty(s,"name",{value:e}),s}n.d(t,{$W:()=>s,GT:()=>i,cr:()=>o,xI:()=>r}),Object.freeze({status:"aborted"}),Symbol("zod_brand");class i extends Error{constructor(){super("Encountered Promise during synchronous parse. Use .parseAsync() instead.")}}let o={};function s(e){return e&&Object.assign(o,e),o}},4355:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("camera",[["path",{d:"M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z",key:"1tc9qg"}],["circle",{cx:"12",cy:"13",r:"3",key:"1vg3eu"}]])},4398:(e,t,n)=>{function r(e){let t=Object.values(e).filter(e=>"number"==typeof e);return Object.entries(e).filter(([e,n])=>-1===t.indexOf(+e)).map(([e,t])=>t)}function i(e,t){return"bigint"==typeof t?t.toString():t}function o(e){return{get value(){{let t=e();return Object.defineProperty(this,"value",{value:t}),t}}}}function s(e){return null==e}function a(e){let t=+!!e.startsWith("^"),n=e.endsWith("$")?e.length-1:e.length;return e.slice(t,n)}function l(e,t,n){Object.defineProperty(e,t,{get(){{let r=n();return e[t]=r,r}},set(n){Object.defineProperty(e,t,{value:n})},configurable:!0})}function u(e,t,n){Object.defineProperty(e,t,{value:n,writable:!0,enumerable:!0,configurable:!0})}function c(e){return JSON.stringify(e)}n.d(t,{$f:()=>g,A2:()=>y,Gv:()=>h,NM:()=>b,OH:()=>E,PO:()=>o,QH:()=>_,Qd:()=>p,Rc:()=>C,UQ:()=>c,Up:()=>x,Vy:()=>u,X$:()=>k,cJ:()=>w,cl:()=>s,gJ:()=>l,gx:()=>d,h1:()=>A,hI:()=>f,iR:()=>M,k8:()=>i,lQ:()=>P,mw:()=>S,o8:()=>v,p6:()=>a,qQ:()=>m,sn:()=>D,w5:()=>r});let d=Error.captureStackTrace?Error.captureStackTrace:(...e)=>{};function h(e){return"object"==typeof e&&null!==e&&!Array.isArray(e)}let f=o(()=>{if("undefined"!=typeof navigator&&navigator?.userAgent?.includes("Cloudflare"))return!1;try{return Function(""),!0}catch(e){return!1}});function p(e){if(!1===h(e))return!1;let t=e.constructor;if(void 0===t)return!0;let n=t.prototype;return!1!==h(n)&&!1!==Object.prototype.hasOwnProperty.call(n,"isPrototypeOf")}let m=new Set(["string","number","symbol"]);function g(e){return e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}function v(e,t,n){let r=new e._zod.constr(t??e._zod.def);return(!t||n?.parent)&&(r._zod.parent=e),r}function y(e){if(!e)return{};if("string"==typeof e)return{error:()=>e};if(e?.message!==void 0){if(e?.error!==void 0)throw Error("Cannot specify both `message` and `error` params");e.error=e.message}return(delete e.message,"string"==typeof e.error)?{...e,error:()=>e.error}:e}function b(e){return Object.keys(e).filter(t=>"optional"===e[t]._zod.optin&&"optional"===e[t]._zod.optout)}function x(e,t){let n={},r=e._zod.def;for(let e in t){if(!(e in r.shape))throw Error(`Unrecognized key: "${e}"`);t[e]&&(n[e]=r.shape[e])}return v(e,{...e._zod.def,shape:n,checks:[]})}function w(e,t){let n={...e._zod.def.shape},r=e._zod.def;for(let e in t){if(!(e in r.shape))throw Error(`Unrecognized key: "${e}"`);t[e]&&delete n[e]}return v(e,{...e._zod.def,shape:n,checks:[]})}function k(e,t){if(!p(t))throw Error("Invalid input to extend: expected a plain object");let n={...e._zod.def,get shape(){let n={...e._zod.def.shape,...t};return u(this,"shape",n),n},checks:[]};return v(e,n)}function A(e,t){return v(e,{...e._zod.def,get shape(){let n={...e._zod.def.shape,...t._zod.def.shape};return u(this,"shape",n),n},catchall:t._zod.def.catchall,checks:[]})}function E(e,t,n){let r=t._zod.def.shape,i={...r};if(n)for(let t in n){if(!(t in r))throw Error(`Unrecognized key: "${t}"`);n[t]&&(i[t]=e?new e({type:"optional",innerType:r[t]}):r[t])}else for(let t in r)i[t]=e?new e({type:"optional",innerType:r[t]}):r[t];return v(t,{...t._zod.def,shape:i,checks:[]})}function S(e,t,n){let r=t._zod.def.shape,i={...r};if(n)for(let t in n){if(!(t in i))throw Error(`Unrecognized key: "${t}"`);n[t]&&(i[t]=new e({type:"nonoptional",innerType:r[t]}))}else for(let t in r)i[t]=new e({type:"nonoptional",innerType:r[t]});return v(t,{...t._zod.def,shape:i,checks:[]})}function _(e,t=0){for(let n=t;n<e.issues.length;n++)if(e.issues[n]?.continue!==!0)return!0;return!1}function P(e,t){return t.map(t=>(t.path??(t.path=[]),t.path.unshift(e),t))}function T(e){return"string"==typeof e?e:e?.message}function M(e,t,n){let r={...e,path:e.path??[]};return e.message||(r.message=T(e.inst?._zod.def?.error?.(e))??T(t?.error?.(e))??T(n.customError?.(e))??T(n.localeError?.(e))??"Invalid input"),delete r.inst,delete r.continue,t?.reportInput||delete r.input,r}function C(e){return Array.isArray(e)?"array":"string"==typeof e?"string":"unknown"}function D(...e){let[t,n,r]=e;return"string"==typeof t?{message:t,code:"custom",input:n,inst:r}:{...t}}Number.MIN_SAFE_INTEGER,Number.MAX_SAFE_INTEGER,Number.MAX_VALUE,Number.MAX_VALUE},4416:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},4516:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},4783:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("menu",[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]])},5005:(e,t,n)=>{n.d(t,{A:()=>S});var r=n(2115);function i(e){return"[object Object]"===Object.prototype.toString.call(e)||Array.isArray(e)}function o(e,t){let n=Object.keys(e),r=Object.keys(t);return n.length===r.length&&JSON.stringify(Object.keys(e.breakpoints||{}))===JSON.stringify(Object.keys(t.breakpoints||{}))&&n.every(n=>{let r=e[n],s=t[n];return"function"==typeof r?`${r}`==`${s}`:i(r)&&i(s)?o(r,s):r===s})}function s(e){return e.concat().sort((e,t)=>e.name>t.name?1:-1).map(e=>e.options)}function a(e){return"number"==typeof e}function l(e){return"string"==typeof e}function u(e){return"boolean"==typeof e}function c(e){return"[object Object]"===Object.prototype.toString.call(e)}function d(e){return Math.abs(e)}function h(e){return Math.sign(e)}function f(e){return v(e).map(Number)}function p(e){return e[m(e)]}function m(e){return Math.max(0,e.length-1)}function g(e,t=0){return Array.from(Array(e),(e,n)=>t+n)}function v(e){return Object.keys(e)}function y(e,t){return void 0!==t.MouseEvent&&e instanceof t.MouseEvent}function b(){let e=[],t={add:function(n,r,i,o={passive:!0}){let s;return"addEventListener"in n?(n.addEventListener(r,i,o),s=()=>n.removeEventListener(r,i,o)):(n.addListener(i),s=()=>n.removeListener(i)),e.push(s),t},clear:function(){e=e.filter(e=>e())}};return t}function x(e=0,t=0){let n=d(e-t);function r(n){return n<e||n>t}return{length:n,max:t,min:e,constrain:function(n){return r(n)?n<e?e:t:n},reachedAny:r,reachedMax:function(e){return e>t},reachedMin:function(t){return t<e},removeOffset:function(e){return n?e-n*Math.ceil((e-t)/n):e}}}function w(e){let t=e;function n(e){return a(e)?e:e.get()}return{get:function(){return t},set:function(e){t=n(e)},add:function(e){t+=n(e)},subtract:function(e){t-=n(e)}}}function k(e,t){let n="x"===e.scroll?function(e){return`translate3d(${e}px,0px,0px)`}:function(e){return`translate3d(0px,${e}px,0px)`},r=t.style,i=null,o=!1;return{clear:function(){!o&&(r.transform="",t.getAttribute("style")||t.removeAttribute("style"))},to:function(t){if(o)return;let s=Math.round(100*e.direction(t))/100;s!==i&&(r.transform=n(s),i=s)},toggleActive:function(e){o=!e}}}let A={align:"center",axis:"x",container:null,slides:null,containScroll:"trimSnaps",direction:"ltr",slidesToScroll:1,inViewThreshold:0,breakpoints:{},dragFree:!1,dragThreshold:10,loop:!1,skipSnaps:!1,duration:25,startIndex:0,active:!0,watchDrag:!0,watchResize:!0,watchSlides:!0,watchFocus:!0};function E(e,t,n){let r,i,o,s,S,_=e.ownerDocument,P=_.defaultView,T=function(e){function t(e,t){return function e(t,n){return[t,n].reduce((t,n)=>(v(n).forEach(r=>{let i=t[r],o=n[r],s=c(i)&&c(o);t[r]=s?e(i,o):o}),t),{})}(e,t||{})}return{mergeOptions:t,optionsAtMedia:function(n){let r=n.breakpoints||{},i=v(r).filter(t=>e.matchMedia(t).matches).map(e=>r[e]).reduce((e,n)=>t(e,n),{});return t(n,i)},optionsMediaQueries:function(t){return t.map(e=>v(e.breakpoints||{})).reduce((e,t)=>e.concat(t),[]).map(e.matchMedia)}}}(P),M=(S=[],{init:function(e,t){return(S=t.filter(({options:e})=>!1!==T.optionsAtMedia(e).active)).forEach(t=>t.init(e,T)),t.reduce((e,t)=>Object.assign(e,{[t.name]:t}),{})},destroy:function(){S=S.filter(e=>e.destroy())}}),C=b(),D=function(){let e,t={},n={init:function(t){e=t},emit:function(r){return(t[r]||[]).forEach(t=>t(e,r)),n},off:function(e,r){return t[e]=(t[e]||[]).filter(e=>e!==r),n},on:function(e,r){return t[e]=(t[e]||[]).concat([r]),n},clear:function(){t={}}};return n}(),{mergeOptions:V,optionsAtMedia:z,optionsMediaQueries:I}=T,{on:j,off:R,emit:O}=D,F=!1,L=V(A,E.globalOptions),$=V(L),N=[];function U(t,n){if(F)return;$=z(L=V(L,t)),N=n||N;let{container:c,slides:A}=$;o=(l(c)?e.querySelector(c):c)||e.children[0];let E=l(A)?o.querySelectorAll(A):A;s=[].slice.call(E||o.children),r=function t(n){let r=function(e,t,n,r,i,o,s){let c,A,{align:E,axis:S,direction:_,startIndex:P,loop:T,duration:M,dragFree:C,dragThreshold:D,inViewThreshold:V,slidesToScroll:z,skipSnaps:I,containScroll:j,watchResize:R,watchSlides:O,watchDrag:F,watchFocus:L}=o,$={measure:function(e){let{offsetTop:t,offsetLeft:n,offsetWidth:r,offsetHeight:i}=e;return{top:t,right:n+r,bottom:t+i,left:n,width:r,height:i}}},N=$.measure(t),U=n.map($.measure),B=function(e,t){let n="rtl"===t,r="y"===e,i=!r&&n?-1:1;return{scroll:r?"y":"x",cross:r?"x":"y",startEdge:r?"top":n?"right":"left",endEdge:r?"bottom":n?"left":"right",measureSize:function(e){let{height:t,width:n}=e;return r?t:n},direction:function(e){return e*i}}}(S,_),Z=B.measureSize(N),W={measure:function(e){return e/100*Z}},H=function(e,t){let n={start:function(){return 0},center:function(e){return(t-e)/2},end:function(e){return t-e}};return{measure:function(r,i){return l(e)?n[e](r):e(t,r,i)}}}(E,Z),q=!T&&!!j,{slideSizes:G,slideSizesWithGaps:K,startGap:X,endGap:J}=function(e,t,n,r,i,o){let{measureSize:s,startEdge:a,endEdge:l}=e,u=n[0]&&i,c=function(){if(!u)return 0;let e=n[0];return d(t[a]-e[a])}(),h=u?parseFloat(o.getComputedStyle(p(r)).getPropertyValue(`margin-${l}`)):0,f=n.map(s),g=n.map((e,t,n)=>{let r=t===m(n);return t?r?f[t]+h:n[t+1][a]-e[a]:f[t]+c}).map(d);return{slideSizes:f,slideSizesWithGaps:g,startGap:c,endGap:h}}(B,N,U,n,T||!!j,i),Y=function(e,t,n,r,i,o,s,l,u){let{startEdge:c,endEdge:h,direction:g}=e,v=a(n);return{groupSlides:function(e){return v?f(e).filter(e=>e%n==0).map(t=>e.slice(t,t+n)):e.length?f(e).reduce((n,a,u)=>{let f=p(n)||0,v=a===m(e),y=i[c]-o[f][c],b=i[c]-o[a][h],x=r||0!==f?0:g(s),w=d(b-(!r&&v?g(l):0)-(y+x));return u&&w>t+2&&n.push(a),v&&n.push(e.length),n},[]).map((t,n,r)=>{let i=Math.max(r[n-1]||0);return e.slice(i,t)}):[]}}}(B,Z,z,T,N,U,X,J,0),{snaps:Q,snapsAligned:ee}=function(e,t,n,r,i){let{startEdge:o,endEdge:s}=e,{groupSlides:a}=i,l=a(r).map(e=>p(e)[s]-e[0][o]).map(d).map(t.measure),u=r.map(e=>n[o]-e[o]).map(e=>-d(e)),c=a(u).map(e=>e[0]).map((e,t)=>e+l[t]);return{snaps:u,snapsAligned:c}}(B,H,N,U,Y),et=-p(Q)+p(K),{snapsContained:en,scrollContainLimit:er}=function(e,t,n,r,i){let o=x(-t+e,0),s=n.map((e,t)=>{let{min:r,max:i}=o,s=o.constrain(e),a=t===m(n);return t?a||function(e,t){return 1>=d(e-t)}(r,s)?r:function(e,t){return 1>=d(e-t)}(i,s)?i:s:i}).map(e=>parseFloat(e.toFixed(3))),a=function(){let e=s[0],t=p(s);return x(s.lastIndexOf(e),s.indexOf(t)+1)}();return{snapsContained:function(){if(t<=e+2)return[o.max];if("keepSnaps"===r)return s;let{min:n,max:i}=a;return s.slice(n,i)}(),scrollContainLimit:a}}(Z,et,ee,j,0),ei=q?en:ee,{limit:eo}=function(e,t,n){let r=t[0];return{limit:x(n?r-e:p(t),r)}}(et,ei,T),es=function e(t,n,r){let{constrain:i}=x(0,t),o=t+1,s=a(n);function a(e){return r?d((o+e)%o):i(e)}function l(){return e(t,s,r)}let u={get:function(){return s},set:function(e){return s=a(e),u},add:function(e){return l().set(s+e)},clone:l};return u}(m(ei),P,T),ea=es.clone(),el=f(n),eu=function(e,t,n,r){let i=b(),o=1e3/60,s=null,a=0,l=0;function u(e){if(!l)return;s||(s=e,n(),n());let i=e-s;for(s=e,a+=i;a>=o;)n(),a-=o;r(a/o),l&&(l=t.requestAnimationFrame(u))}function c(){t.cancelAnimationFrame(l),s=null,a=0,l=0}return{init:function(){i.add(e,"visibilitychange",()=>{e.hidden&&(s=null,a=0)})},destroy:function(){c(),i.clear()},start:function(){l||(l=t.requestAnimationFrame(u))},stop:c,update:n,render:r}}(r,i,()=>(({dragHandler:e,scrollBody:t,scrollBounds:n,options:{loop:r}})=>{r||n.constrain(e.pointerDown()),t.seek()})(eA),e=>(({scrollBody:e,translate:t,location:n,offsetLocation:r,previousLocation:i,scrollLooper:o,slideLooper:s,dragHandler:a,animation:l,eventHandler:u,scrollBounds:c,options:{loop:d}},h)=>{let f=e.settled(),p=!c.shouldConstrain(),m=d?f:f&&p,g=m&&!a.pointerDown();g&&l.stop();let v=n.get()*h+i.get()*(1-h);r.set(v),d&&(o.loop(e.direction()),s.loop()),t.to(r.get()),g&&u.emit("settle"),m||u.emit("scroll")})(eA,e)),ec=ei[es.get()],ed=w(ec),eh=w(ec),ef=w(ec),ep=w(ec),em=function(e,t,n,r,i,o){let s=0,a=0,l=i,u=.68,c=e.get(),f=0;function p(e){return l=e,g}function m(e){return u=e,g}let g={direction:function(){return a},duration:function(){return l},velocity:function(){return s},seek:function(){let t=r.get()-e.get(),i=0;return l?(n.set(e),s+=t/l,s*=u,c+=s,e.add(s),i=c-f):(s=0,n.set(r),e.set(r),i=t),a=h(i),f=c,g},settled:function(){return .001>d(r.get()-t.get())},useBaseFriction:function(){return m(.68)},useBaseDuration:function(){return p(i)},useFriction:m,useDuration:p};return g}(ed,ef,eh,ep,M,.68),eg=function(e,t,n,r,i){let{reachedAny:o,removeOffset:s,constrain:a}=r;function l(e){return e.concat().sort((e,t)=>d(e)-d(t))[0]}function u(t,r){let i=[t,t+n,t-n];if(!e)return t;if(!r)return l(i);let o=i.filter(e=>h(e)===r);return o.length?l(o):p(i)-n}return{byDistance:function(n,r){let l=i.get()+n,{index:c,distance:h}=function(n){let r=e?s(n):a(n),{index:i}=t.map((e,t)=>({diff:u(e-r,0),index:t})).sort((e,t)=>d(e.diff)-d(t.diff))[0];return{index:i,distance:r}}(l),f=!e&&o(l);if(!r||f)return{index:c,distance:n};let p=n+u(t[c]-h,0);return{index:c,distance:p}},byIndex:function(e,n){let r=u(t[e]-i.get(),n);return{index:e,distance:r}},shortcut:u}}(T,ei,et,eo,ep),ev=function(e,t,n,r,i,o,s){function a(i){let a=i.distance,l=i.index!==t.get();o.add(a),a&&(r.duration()?e.start():(e.update(),e.render(1),e.update())),l&&(n.set(t.get()),t.set(i.index),s.emit("select"))}return{distance:function(e,t){a(i.byDistance(e,t))},index:function(e,n){let r=t.clone().set(e);a(i.byIndex(r.get(),n))}}}(eu,es,ea,em,eg,ep,s),ey=function(e){let{max:t,length:n}=e;return{get:function(e){return n?-((e-t)/n):0}}}(eo),eb=b(),ex=function(e,t,n,r){let i,o={},s=null,a=null,l=!1;return{init:function(){i=new IntersectionObserver(e=>{l||(e.forEach(e=>{o[t.indexOf(e.target)]=e}),s=null,a=null,n.emit("slidesInView"))},{root:e.parentElement,threshold:r}),t.forEach(e=>i.observe(e))},destroy:function(){i&&i.disconnect(),l=!0},get:function(e=!0){if(e&&s)return s;if(!e&&a)return a;let t=v(o).reduce((t,n)=>{let r=parseInt(n),{isIntersecting:i}=o[r];return(e&&i||!e&&!i)&&t.push(r),t},[]);return e&&(s=t),e||(a=t),t}}}(t,n,s,V),{slideRegistry:ew}=function(e,t,n,r,i,o){let{groupSlides:s}=i,{min:a,max:l}=r;return{slideRegistry:function(){let r=s(o);return 1===n.length?[o]:e&&"keepSnaps"!==t?r.slice(a,l).map((e,t,n)=>{let r=t===m(n);return t?r?g(m(o)-p(n)[0]+1,p(n)[0]):e:g(p(n[0])+1)}):r}()}}(q,j,ei,er,Y,el),ek=function(e,t,n,r,i,o,s,l){let c={passive:!0,capture:!0},d=0;function h(e){"Tab"===e.code&&(d=new Date().getTime())}return{init:function(f){l&&(o.add(document,"keydown",h,!1),t.forEach((t,h)=>{o.add(t,"focus",t=>{(u(l)||l(f,t))&&function(t){if(new Date().getTime()-d>10)return;s.emit("slideFocusStart"),e.scrollLeft=0;let o=n.findIndex(e=>e.includes(t));a(o)&&(i.useDuration(0),r.index(o,0),s.emit("slideFocus"))}(h)},c)}))}}}(e,n,ew,ev,em,eb,s,L),eA={ownerDocument:r,ownerWindow:i,eventHandler:s,containerRect:N,slideRects:U,animation:eu,axis:B,dragHandler:function(e,t,n,r,i,o,s,a,l,c,f,p,m,g,v,w,k,A,E){let{cross:S,direction:_}=e,P=["INPUT","SELECT","TEXTAREA"],T={passive:!1},M=b(),C=b(),D=x(50,225).constrain(g.measure(20)),V={mouse:300,touch:400},z={mouse:500,touch:600},I=v?43:25,j=!1,R=0,O=0,F=!1,L=!1,$=!1,N=!1;function U(e){if(!y(e,r)&&e.touches.length>=2)return B(e);let t=o.readPoint(e),n=o.readPoint(e,S),s=d(t-R),l=d(n-O);if(!L&&!N&&(!e.cancelable||!(L=s>l)))return B(e);let u=o.pointerMove(e);s>w&&($=!0),c.useFriction(.3).useDuration(.75),a.start(),i.add(_(u)),e.preventDefault()}function B(e){let t=f.byDistance(0,!1).index!==p.get(),n=o.pointerUp(e)*(v?z:V)[N?"mouse":"touch"],r=function(e,t){let n=p.add(-1*h(e)),r=f.byDistance(e,!v).distance;return v||d(e)<D?r:k&&t?.5*r:f.byIndex(n.get(),0).distance}(_(n),t),i=function(e,t){var n,r;if(0===e||0===t||d(e)<=d(t))return 0;let i=(n=d(e),r=d(t),d(n-r));return d(i/e)}(n,r);L=!1,F=!1,C.clear(),c.useDuration(I-10*i).useFriction(.68+i/50),l.distance(r,!v),N=!1,m.emit("pointerUp")}function Z(e){$&&(e.stopPropagation(),e.preventDefault(),$=!1)}return{init:function(e){E&&M.add(t,"dragstart",e=>e.preventDefault(),T).add(t,"touchmove",()=>void 0,T).add(t,"touchend",()=>void 0).add(t,"touchstart",a).add(t,"mousedown",a).add(t,"touchcancel",B).add(t,"contextmenu",B).add(t,"click",Z,!0);function a(a){(u(E)||E(e,a))&&function(e){let a=y(e,r);if((N=a,$=v&&a&&!e.buttons&&j,j=d(i.get()-s.get())>=2,!a||0===e.button)&&!function(e){let t=e.nodeName||"";return P.includes(t)}(e.target)){F=!0,o.pointerDown(e),c.useFriction(0).useDuration(0),i.set(s);let r=N?n:t;C.add(r,"touchmove",U,T).add(r,"touchend",B).add(r,"mousemove",U,T).add(r,"mouseup",B),R=o.readPoint(e),O=o.readPoint(e,S),m.emit("pointerDown")}}(a)}},destroy:function(){M.clear(),C.clear()},pointerDown:function(){return F}}}(B,e,r,i,ep,function(e,t){let n,r;function i(e){return e.timeStamp}function o(n,r){let i=r||e.scroll,o=`client${"x"===i?"X":"Y"}`;return(y(n,t)?n:n.touches[0])[o]}return{pointerDown:function(e){return n=e,r=e,o(e)},pointerMove:function(e){let t=o(e)-o(r),s=i(e)-i(n)>170;return r=e,s&&(n=e),t},pointerUp:function(e){if(!n||!r)return 0;let t=o(r)-o(n),s=i(e)-i(n),a=i(e)-i(r)>170,l=t/s;return s&&!a&&d(l)>.1?l:0},readPoint:o}}(B,i),ed,eu,ev,em,eg,es,s,W,C,D,I,0,F),eventStore:eb,percentOfView:W,index:es,indexPrevious:ea,limit:eo,location:ed,offsetLocation:ef,previousLocation:eh,options:o,resizeHandler:function(e,t,n,r,i,o,s){let a,l,c=[e].concat(r),h=[],f=!1;function p(e){return i.measureSize(s.measure(e))}return{init:function(i){o&&(l=p(e),h=r.map(p),a=new ResizeObserver(n=>{(u(o)||o(i,n))&&function(n){for(let o of n){if(f)return;let n=o.target===e,s=r.indexOf(o.target),a=n?l:h[s];if(d(p(n?e:r[s])-a)>=.5){i.reInit(),t.emit("resize");break}}}(n)}),n.requestAnimationFrame(()=>{c.forEach(e=>a.observe(e))}))},destroy:function(){f=!0,a&&a.disconnect()}}}(t,s,i,n,B,R,$),scrollBody:em,scrollBounds:function(e,t,n,r,i){let o=i.measure(10),s=i.measure(50),a=x(.1,.99),l=!1;function u(){return!l&&!!e.reachedAny(n.get())&&!!e.reachedAny(t.get())}return{shouldConstrain:u,constrain:function(i){if(!u())return;let l=e.reachedMin(t.get())?"min":"max",c=d(e[l]-t.get()),h=n.get()-t.get(),f=a.constrain(c/s);n.subtract(h*f),!i&&d(h)<o&&(n.set(e.constrain(n.get())),r.useDuration(25).useBaseFriction())},toggleActive:function(e){l=!e}}}(eo,ef,ep,em,W),scrollLooper:function(e,t,n,r){let{reachedMin:i,reachedMax:o}=x(t.min+.1,t.max+.1);return{loop:function(t){if(!(1===t?o(n.get()):-1===t&&i(n.get())))return;let s=-1*t*e;r.forEach(e=>e.add(s))}}}(et,eo,ef,[ed,ef,eh,ep]),scrollProgress:ey,scrollSnapList:ei.map(ey.get),scrollSnaps:ei,scrollTarget:eg,scrollTo:ev,slideLooper:function(e,t,n,r,i,o,s,a,l){let u=f(i),c=f(i).reverse(),d=m(p(c,s[0]),n,!1).concat(m(p(u,t-s[0]-1),-n,!0));function h(e,t){return e.reduce((e,t)=>e-i[t],t)}function p(e,t){return e.reduce((e,n)=>h(e,t)>0?e.concat([n]):e,[])}function m(i,s,u){let c=o.map((e,n)=>({start:e-r[n]+.5+s,end:e+t-.5+s}));return i.map(t=>{let r=u?0:-n,i=u?n:0,o=c[t][u?"end":"start"];return{index:t,loopPoint:o,slideLocation:w(-1),translate:k(e,l[t]),target:()=>a.get()>o?r:i}})}return{canLoop:function(){return d.every(({index:e})=>.1>=h(u.filter(t=>t!==e),t))},clear:function(){d.forEach(e=>e.translate.clear())},loop:function(){d.forEach(e=>{let{target:t,translate:n,slideLocation:r}=e,i=t();i!==r.get()&&(n.to(i),r.set(i))})},loopPoints:d}}(B,Z,et,G,K,Q,ei,ef,n),slideFocus:ek,slidesHandler:(A=!1,{init:function(e){O&&(c=new MutationObserver(t=>{!A&&(u(O)||O(e,t))&&function(t){for(let n of t)if("childList"===n.type){e.reInit(),s.emit("slidesChanged");break}}(t)})).observe(t,{childList:!0})},destroy:function(){c&&c.disconnect(),A=!0}}),slidesInView:ex,slideIndexes:el,slideRegistry:ew,slidesToScroll:Y,target:ep,translate:k(B,t)};return eA}(e,o,s,_,P,n,D);return n.loop&&!r.slideLooper.canLoop()?t(Object.assign({},n,{loop:!1})):r}($),I([L,...N.map(({options:e})=>e)]).forEach(e=>C.add(e,"change",B)),$.active&&(r.translate.to(r.location.get()),r.animation.init(),r.slidesInView.init(),r.slideFocus.init(q),r.eventHandler.init(q),r.resizeHandler.init(q),r.slidesHandler.init(q),r.options.loop&&r.slideLooper.loop(),o.offsetParent&&s.length&&r.dragHandler.init(q),i=M.init(q,N))}function B(e,t){let n=H();Z(),U(V({startIndex:n},e),t),D.emit("reInit")}function Z(){r.dragHandler.destroy(),r.eventStore.clear(),r.translate.clear(),r.slideLooper.clear(),r.resizeHandler.destroy(),r.slidesHandler.destroy(),r.slidesInView.destroy(),r.animation.destroy(),M.destroy(),C.clear()}function W(e,t,n){$.active&&!F&&(r.scrollBody.useBaseFriction().useDuration(!0===t?0:$.duration),r.scrollTo.index(e,n||0))}function H(){return r.index.get()}let q={canScrollNext:function(){return r.index.add(1).get()!==H()},canScrollPrev:function(){return r.index.add(-1).get()!==H()},containerNode:function(){return o},internalEngine:function(){return r},destroy:function(){F||(F=!0,C.clear(),Z(),D.emit("destroy"),D.clear())},off:R,on:j,emit:O,plugins:function(){return i},previousScrollSnap:function(){return r.indexPrevious.get()},reInit:B,rootNode:function(){return e},scrollNext:function(e){W(r.index.add(1).get(),e,-1)},scrollPrev:function(e){W(r.index.add(-1).get(),e,1)},scrollProgress:function(){return r.scrollProgress.get(r.offsetLocation.get())},scrollSnapList:function(){return r.scrollSnapList},scrollTo:W,selectedScrollSnap:H,slideNodes:function(){return s},slidesInView:function(){return r.slidesInView.get()},slidesNotInView:function(){return r.slidesInView.get(!1)}};return U(t,n),setTimeout(()=>D.emit("init"),0),q}function S(e={},t=[]){let n=(0,r.useRef)(e),i=(0,r.useRef)(t),[a,l]=(0,r.useState)(),[u,c]=(0,r.useState)(),d=(0,r.useCallback)(()=>{a&&a.reInit(n.current,i.current)},[a]);return(0,r.useEffect)(()=>{o(n.current,e)||(n.current=e,d())},[e,d]),(0,r.useEffect)(()=>{!function(e,t){if(e.length!==t.length)return!1;let n=s(e),r=s(t);return n.every((e,t)=>o(e,r[t]))}(i.current,t)&&(i.current=t,d())},[t,d]),(0,r.useEffect)(()=>{if("undefined"!=typeof window&&window.document&&window.document.createElement&&u){E.globalOptions=S.globalOptions;let e=E(u,n.current,i.current);return l(e),()=>e.destroy()}l(void 0)},[u,l]),[c,a]}E.globalOptions=void 0,S.globalOptions=void 0},5169:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},5196:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},5684:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("instagram",[["rect",{width:"20",height:"20",x:"2",y:"2",rx:"5",ry:"5",key:"2e1cvw"}],["path",{d:"M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z",key:"9exkf1"}],["line",{x1:"17.5",x2:"17.51",y1:"6.5",y2:"6.5",key:"r4j83e"}]])},6101:(e,t,n)=>{n.d(t,{s:()=>s,t:()=>o});var r=n(2115);function i(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function o(...e){return t=>{let n=!1,r=e.map(e=>{let r=i(e,t);return n||"function"!=typeof r||(n=!0),r});if(n)return()=>{for(let t=0;t<r.length;t++){let n=r[t];"function"==typeof n?n():i(e[t],null)}}}}function s(...e){return r.useCallback(o(...e),e)}},6183:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},6654:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useMergedRef",{enumerable:!0,get:function(){return i}});let r=n(2115);function i(e,t){let n=(0,r.useRef)(null),i=(0,r.useRef)(null);return(0,r.useCallback)(r=>{if(null===r){let e=n.current;e&&(n.current=null,e());let t=i.current;t&&(i.current=null,t())}else e&&(n.current=o(e,r)),t&&(i.current=o(t,r))},[e,t])}function o(e,t){if("function"!=typeof e)return e.current=t,()=>{e.current=null};{let n=e(t);return"function"==typeof n?n:()=>e(null)}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6874:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{default:function(){return g},useLinkStatus:function(){return y}});let r=n(6966),i=n(5155),o=r._(n(2115)),s=n(2757),a=n(5227),l=n(9818),u=n(6654),c=n(9991),d=n(5929);n(3230);let h=n(4930),f=n(2664),p=n(6634);function m(e){return"string"==typeof e?e:(0,s.formatUrl)(e)}function g(e){let t,n,r,[s,g]=(0,o.useOptimistic)(h.IDLE_LINK_STATUS),y=(0,o.useRef)(null),{href:b,as:x,children:w,prefetch:k=null,passHref:A,replace:E,shallow:S,scroll:_,onClick:P,onMouseEnter:T,onTouchStart:M,legacyBehavior:C=!1,onNavigate:D,ref:V,unstable_dynamicOnHover:z,...I}=e;t=w,C&&("string"==typeof t||"number"==typeof t)&&(t=(0,i.jsx)("a",{children:t}));let j=o.default.useContext(a.AppRouterContext),R=!1!==k,O=null===k||"auto"===k?l.PrefetchKind.AUTO:l.PrefetchKind.FULL,{href:F,as:L}=o.default.useMemo(()=>{let e=m(b);return{href:e,as:x?m(x):e}},[b,x]);C&&(n=o.default.Children.only(t));let $=C?n&&"object"==typeof n&&n.ref:V,N=o.default.useCallback(e=>(null!==j&&(y.current=(0,h.mountLinkInstance)(e,F,j,O,R,g)),()=>{y.current&&((0,h.unmountLinkForCurrentNavigation)(y.current),y.current=null),(0,h.unmountPrefetchableInstance)(e)}),[R,F,j,O,g]),U={ref:(0,u.useMergedRef)(N,$),onClick(e){C||"function"!=typeof P||P(e),C&&n.props&&"function"==typeof n.props.onClick&&n.props.onClick(e),j&&(e.defaultPrevented||function(e,t,n,r,i,s,a){let{nodeName:l}=e.currentTarget;if(!("A"===l.toUpperCase()&&function(e){let t=e.currentTarget.getAttribute("target");return t&&"_self"!==t||e.metaKey||e.ctrlKey||e.shiftKey||e.altKey||e.nativeEvent&&2===e.nativeEvent.which}(e)||e.currentTarget.hasAttribute("download"))){if(!(0,f.isLocalURL)(t)){i&&(e.preventDefault(),location.replace(t));return}if(e.preventDefault(),a){let e=!1;if(a({preventDefault:()=>{e=!0}}),e)return}o.default.startTransition(()=>{(0,p.dispatchNavigateAction)(n||t,i?"replace":"push",null==s||s,r.current)})}}(e,F,L,y,E,_,D))},onMouseEnter(e){C||"function"!=typeof T||T(e),C&&n.props&&"function"==typeof n.props.onMouseEnter&&n.props.onMouseEnter(e),j&&R&&(0,h.onNavigationIntent)(e.currentTarget,!0===z)},onTouchStart:function(e){C||"function"!=typeof M||M(e),C&&n.props&&"function"==typeof n.props.onTouchStart&&n.props.onTouchStart(e),j&&R&&(0,h.onNavigationIntent)(e.currentTarget,!0===z)}};return(0,c.isAbsoluteUrl)(L)?U.href=L:C&&!A&&("a"!==n.type||"href"in n.props)||(U.href=(0,d.addBasePath)(L)),r=C?o.default.cloneElement(n,U):(0,i.jsx)("a",{...I,...U,children:t}),(0,i.jsx)(v.Provider,{value:s,children:r})}n(3180);let v=(0,o.createContext)(h.IDLE_LINK_STATUS),y=()=>(0,o.useContext)(v);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6983:(e,t,n)=>{n.d(t,{G:()=>r});function r(e){return"object"==typeof e&&null!==e}},7312:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("coffee",[["path",{d:"M10 2v2",key:"7u0qdc"}],["path",{d:"M14 2v2",key:"6buw04"}],["path",{d:"M16 8a1 1 0 0 1 1 1v8a4 4 0 0 1-4 4H7a4 4 0 0 1-4-4V9a1 1 0 0 1 1-1h14a4 4 0 1 1 0 8h-1",key:"pwadti"}],["path",{d:"M6 2v2",key:"colzsn"}]])},7351:(e,t,n)=>{n.d(t,{s:()=>i});var r=n(6983);function i(e){return(0,r.G)(e)&&"offsetHeight"in e}},7494:(e,t,n)=>{n.d(t,{E:()=>i});var r=n(2115);let i=n(8972).B?r.useLayoutEffect:r.useEffect},7580:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},8309:(e,t,n)=>{n.d(t,{EB:()=>tt,Ik:()=>t_,Yj:()=>te});var r=n(4193);let i=/^[cC][^\s-]{8,}$/,o=/^[0-9a-z]+$/,s=/^[0-9A-HJKMNP-TV-Za-hjkmnp-tv-z]{26}$/,a=/^[0-9a-vA-V]{20}$/,l=/^[A-Za-z0-9]{27}$/,u=/^[a-zA-Z0-9_-]{21}$/,c=/^P(?:(\d+W)|(?!.*W)(?=\d|T\d)(\d+Y)?(\d+M)?(\d+D)?(T(?=\d)(\d+H)?(\d+M)?(\d+([.,]\d+)?S)?)?)$/,d=/^([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12})$/,h=e=>e?RegExp(`^([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-${e}[0-9a-fA-F]{3}-[89abAB][0-9a-fA-F]{3}-[0-9a-fA-F]{12})$`):/^([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[1-8][0-9a-fA-F]{3}-[89abAB][0-9a-fA-F]{3}-[0-9a-fA-F]{12}|00000000-0000-0000-0000-000000000000)$/,f=/^(?!\.)(?!.*\.\.)([A-Za-z0-9_'+\-\.]*)[A-Za-z0-9_+-]@([A-Za-z0-9][A-Za-z0-9\-]*\.)+[A-Za-z]{2,}$/,p=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,m=/^(([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}|::|([0-9a-fA-F]{1,4})?::([0-9a-fA-F]{1,4}:?){0,6})$/,g=/^((25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/([0-9]|[1-2][0-9]|3[0-2])$/,v=/^(([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}|::|([0-9a-fA-F]{1,4})?::([0-9a-fA-F]{1,4}:?){0,6})\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,y=/^$|^(?:[0-9a-zA-Z+/]{4})*(?:(?:[0-9a-zA-Z+/]{2}==)|(?:[0-9a-zA-Z+/]{3}=))?$/,b=/^[A-Za-z0-9_-]*$/,x=/^([a-zA-Z0-9-]+\.)*[a-zA-Z0-9-]+$/,w=/^\+(?:[0-9]){6,14}[0-9]$/,k="(?:(?:\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-(?:(?:0[13578]|1[02])-(?:0[1-9]|[12]\\d|3[01])|(?:0[469]|11)-(?:0[1-9]|[12]\\d|30)|(?:02)-(?:0[1-9]|1\\d|2[0-8])))",A=RegExp(`^${k}$`);function E(e){let t="(?:[01]\\d|2[0-3]):[0-5]\\d";return"number"==typeof e.precision?-1===e.precision?`${t}`:0===e.precision?`${t}:[0-5]\\d`:`${t}:[0-5]\\d\\.\\d{${e.precision}}`:`${t}(?::[0-5]\\d(?:\\.\\d+)?)?`}let S=/^[^A-Z]*$/,_=/^[^a-z]*$/;var P=n(4398);let T=r.xI("$ZodCheck",(e,t)=>{var n;e._zod??(e._zod={}),e._zod.def=t,(n=e._zod).onattach??(n.onattach=[])}),M=r.xI("$ZodCheckMaxLength",(e,t)=>{var n;T.init(e,t),(n=e._zod.def).when??(n.when=e=>{let t=e.value;return!P.cl(t)&&void 0!==t.length}),e._zod.onattach.push(e=>{let n=e._zod.bag.maximum??1/0;t.maximum<n&&(e._zod.bag.maximum=t.maximum)}),e._zod.check=n=>{let r=n.value;if(r.length<=t.maximum)return;let i=P.Rc(r);n.issues.push({origin:i,code:"too_big",maximum:t.maximum,inclusive:!0,input:r,inst:e,continue:!t.abort})}}),C=r.xI("$ZodCheckMinLength",(e,t)=>{var n;T.init(e,t),(n=e._zod.def).when??(n.when=e=>{let t=e.value;return!P.cl(t)&&void 0!==t.length}),e._zod.onattach.push(e=>{let n=e._zod.bag.minimum??-1/0;t.minimum>n&&(e._zod.bag.minimum=t.minimum)}),e._zod.check=n=>{let r=n.value;if(r.length>=t.minimum)return;let i=P.Rc(r);n.issues.push({origin:i,code:"too_small",minimum:t.minimum,inclusive:!0,input:r,inst:e,continue:!t.abort})}}),D=r.xI("$ZodCheckLengthEquals",(e,t)=>{var n;T.init(e,t),(n=e._zod.def).when??(n.when=e=>{let t=e.value;return!P.cl(t)&&void 0!==t.length}),e._zod.onattach.push(e=>{let n=e._zod.bag;n.minimum=t.length,n.maximum=t.length,n.length=t.length}),e._zod.check=n=>{let r=n.value,i=r.length;if(i===t.length)return;let o=P.Rc(r),s=i>t.length;n.issues.push({origin:o,...s?{code:"too_big",maximum:t.length}:{code:"too_small",minimum:t.length},inclusive:!0,exact:!0,input:n.value,inst:e,continue:!t.abort})}}),V=r.xI("$ZodCheckStringFormat",(e,t)=>{var n,r;T.init(e,t),e._zod.onattach.push(e=>{let n=e._zod.bag;n.format=t.format,t.pattern&&(n.patterns??(n.patterns=new Set),n.patterns.add(t.pattern))}),t.pattern?(n=e._zod).check??(n.check=n=>{t.pattern.lastIndex=0,t.pattern.test(n.value)||n.issues.push({origin:"string",code:"invalid_format",format:t.format,input:n.value,...t.pattern?{pattern:t.pattern.toString()}:{},inst:e,continue:!t.abort})}):(r=e._zod).check??(r.check=()=>{})}),z=r.xI("$ZodCheckRegex",(e,t)=>{V.init(e,t),e._zod.check=n=>{t.pattern.lastIndex=0,t.pattern.test(n.value)||n.issues.push({origin:"string",code:"invalid_format",format:"regex",input:n.value,pattern:t.pattern.toString(),inst:e,continue:!t.abort})}}),I=r.xI("$ZodCheckLowerCase",(e,t)=>{t.pattern??(t.pattern=S),V.init(e,t)}),j=r.xI("$ZodCheckUpperCase",(e,t)=>{t.pattern??(t.pattern=_),V.init(e,t)}),R=r.xI("$ZodCheckIncludes",(e,t)=>{T.init(e,t);let n=P.$f(t.includes),r=new RegExp("number"==typeof t.position?`^.{${t.position}}${n}`:n);t.pattern=r,e._zod.onattach.push(e=>{let t=e._zod.bag;t.patterns??(t.patterns=new Set),t.patterns.add(r)}),e._zod.check=n=>{n.value.includes(t.includes,t.position)||n.issues.push({origin:"string",code:"invalid_format",format:"includes",includes:t.includes,input:n.value,inst:e,continue:!t.abort})}}),O=r.xI("$ZodCheckStartsWith",(e,t)=>{T.init(e,t);let n=RegExp(`^${P.$f(t.prefix)}.*`);t.pattern??(t.pattern=n),e._zod.onattach.push(e=>{let t=e._zod.bag;t.patterns??(t.patterns=new Set),t.patterns.add(n)}),e._zod.check=n=>{n.value.startsWith(t.prefix)||n.issues.push({origin:"string",code:"invalid_format",format:"starts_with",prefix:t.prefix,input:n.value,inst:e,continue:!t.abort})}}),F=r.xI("$ZodCheckEndsWith",(e,t)=>{T.init(e,t);let n=RegExp(`.*${P.$f(t.suffix)}$`);t.pattern??(t.pattern=n),e._zod.onattach.push(e=>{let t=e._zod.bag;t.patterns??(t.patterns=new Set),t.patterns.add(n)}),e._zod.check=n=>{n.value.endsWith(t.suffix)||n.issues.push({origin:"string",code:"invalid_format",format:"ends_with",suffix:t.suffix,input:n.value,inst:e,continue:!t.abort})}}),L=r.xI("$ZodCheckOverwrite",(e,t)=>{T.init(e,t),e._zod.check=e=>{e.value=t.tx(e.value)}});class ${constructor(e=[]){this.content=[],this.indent=0,this&&(this.args=e)}indented(e){this.indent+=1,e(this),this.indent-=1}write(e){if("function"==typeof e){e(this,{execution:"sync"}),e(this,{execution:"async"});return}let t=e.split("\n").filter(e=>e),n=Math.min(...t.map(e=>e.length-e.trimStart().length));for(let e of t.map(e=>e.slice(n)).map(e=>" ".repeat(2*this.indent)+e))this.content.push(e)}compile(){return Function(...this?.args,[...(this?.content??[""]).map(e=>`  ${e}`)].join("\n"))}}var N=n(8753);let U={major:4,minor:0,patch:5},B=r.xI("$ZodType",(e,t)=>{var n;e??(e={}),e._zod.def=t,e._zod.bag=e._zod.bag||{},e._zod.version=U;let i=[...e._zod.def.checks??[]];for(let t of(e._zod.traits.has("$ZodCheck")&&i.unshift(e),i))for(let n of t._zod.onattach)n(e);if(0===i.length)(n=e._zod).deferred??(n.deferred=[]),e._zod.deferred?.push(()=>{e._zod.run=e._zod.parse});else{let t=(e,t,n)=>{let i,o=P.QH(e);for(let s of t){if(s._zod.def.when){if(!s._zod.def.when(e))continue}else if(o)continue;let t=e.issues.length,a=s._zod.check(e);if(a instanceof Promise&&n?.async===!1)throw new r.GT;if(i||a instanceof Promise)i=(i??Promise.resolve()).then(async()=>{await a,e.issues.length!==t&&(o||(o=P.QH(e,t)))});else{if(e.issues.length===t)continue;o||(o=P.QH(e,t))}}return i?i.then(()=>e):e};e._zod.run=(n,o)=>{let s=e._zod.parse(n,o);if(s instanceof Promise){if(!1===o.async)throw new r.GT;return s.then(e=>t(e,i,o))}return t(s,i,o)}}e["~standard"]={validate:t=>{try{let n=(0,N.xL)(e,t);return n.success?{value:n.data}:{issues:n.error?.issues}}catch(n){return(0,N.bp)(e,t).then(e=>e.success?{value:e.data}:{issues:e.error?.issues})}},vendor:"zod",version:1}}),Z=r.xI("$ZodString",(e,t)=>{B.init(e,t),e._zod.pattern=[...e?._zod.bag?.patterns??[]].pop()??(e=>{let t=e?`[\\s\\S]{${e?.minimum??0},${e?.maximum??""}}`:"[\\s\\S]*";return RegExp(`^${t}$`)})(e._zod.bag),e._zod.parse=(n,r)=>{if(t.coerce)try{n.value=String(n.value)}catch(e){}return"string"==typeof n.value||n.issues.push({expected:"string",code:"invalid_type",input:n.value,inst:e}),n}}),W=r.xI("$ZodStringFormat",(e,t)=>{V.init(e,t),Z.init(e,t)}),H=r.xI("$ZodGUID",(e,t)=>{t.pattern??(t.pattern=d),W.init(e,t)}),q=r.xI("$ZodUUID",(e,t)=>{if(t.version){let e={v1:1,v2:2,v3:3,v4:4,v5:5,v6:6,v7:7,v8:8}[t.version];if(void 0===e)throw Error(`Invalid UUID version: "${t.version}"`);t.pattern??(t.pattern=h(e))}else t.pattern??(t.pattern=h());W.init(e,t)}),G=r.xI("$ZodEmail",(e,t)=>{t.pattern??(t.pattern=f),W.init(e,t)}),K=r.xI("$ZodURL",(e,t)=>{W.init(e,t),e._zod.check=n=>{try{let r=n.value,i=new URL(r),o=i.href;t.hostname&&(t.hostname.lastIndex=0,t.hostname.test(i.hostname)||n.issues.push({code:"invalid_format",format:"url",note:"Invalid hostname",pattern:x.source,input:n.value,inst:e,continue:!t.abort})),t.protocol&&(t.protocol.lastIndex=0,t.protocol.test(i.protocol.endsWith(":")?i.protocol.slice(0,-1):i.protocol)||n.issues.push({code:"invalid_format",format:"url",note:"Invalid protocol",pattern:t.protocol.source,input:n.value,inst:e,continue:!t.abort})),!r.endsWith("/")&&o.endsWith("/")?n.value=o.slice(0,-1):n.value=o;return}catch(r){n.issues.push({code:"invalid_format",format:"url",input:n.value,inst:e,continue:!t.abort})}}}),X=r.xI("$ZodEmoji",(e,t)=>{t.pattern??(t.pattern=RegExp("^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$","u")),W.init(e,t)}),J=r.xI("$ZodNanoID",(e,t)=>{t.pattern??(t.pattern=u),W.init(e,t)}),Y=r.xI("$ZodCUID",(e,t)=>{t.pattern??(t.pattern=i),W.init(e,t)}),Q=r.xI("$ZodCUID2",(e,t)=>{t.pattern??(t.pattern=o),W.init(e,t)}),ee=r.xI("$ZodULID",(e,t)=>{t.pattern??(t.pattern=s),W.init(e,t)}),et=r.xI("$ZodXID",(e,t)=>{t.pattern??(t.pattern=a),W.init(e,t)}),en=r.xI("$ZodKSUID",(e,t)=>{t.pattern??(t.pattern=l),W.init(e,t)}),er=r.xI("$ZodISODateTime",(e,t)=>{t.pattern??(t.pattern=function(e){let t=E({precision:e.precision}),n=["Z"];e.local&&n.push(""),e.offset&&n.push("([+-]\\d{2}:\\d{2})");let r=`${t}(?:${n.join("|")})`;return RegExp(`^${k}T(?:${r})$`)}(t)),W.init(e,t)}),ei=r.xI("$ZodISODate",(e,t)=>{t.pattern??(t.pattern=A),W.init(e,t)}),eo=r.xI("$ZodISOTime",(e,t)=>{t.pattern??(t.pattern=RegExp(`^${E(t)}$`)),W.init(e,t)}),es=r.xI("$ZodISODuration",(e,t)=>{t.pattern??(t.pattern=c),W.init(e,t)}),ea=r.xI("$ZodIPv4",(e,t)=>{t.pattern??(t.pattern=p),W.init(e,t),e._zod.onattach.push(e=>{e._zod.bag.format="ipv4"})}),el=r.xI("$ZodIPv6",(e,t)=>{t.pattern??(t.pattern=m),W.init(e,t),e._zod.onattach.push(e=>{e._zod.bag.format="ipv6"}),e._zod.check=n=>{try{new URL(`http://[${n.value}]`)}catch{n.issues.push({code:"invalid_format",format:"ipv6",input:n.value,inst:e,continue:!t.abort})}}}),eu=r.xI("$ZodCIDRv4",(e,t)=>{t.pattern??(t.pattern=g),W.init(e,t)}),ec=r.xI("$ZodCIDRv6",(e,t)=>{t.pattern??(t.pattern=v),W.init(e,t),e._zod.check=n=>{let[r,i]=n.value.split("/");try{if(!i)throw Error();let e=Number(i);if(`${e}`!==i||e<0||e>128)throw Error();new URL(`http://[${r}]`)}catch{n.issues.push({code:"invalid_format",format:"cidrv6",input:n.value,inst:e,continue:!t.abort})}}});function ed(e){if(""===e)return!0;if(e.length%4!=0)return!1;try{return atob(e),!0}catch{return!1}}let eh=r.xI("$ZodBase64",(e,t)=>{t.pattern??(t.pattern=y),W.init(e,t),e._zod.onattach.push(e=>{e._zod.bag.contentEncoding="base64"}),e._zod.check=n=>{ed(n.value)||n.issues.push({code:"invalid_format",format:"base64",input:n.value,inst:e,continue:!t.abort})}}),ef=r.xI("$ZodBase64URL",(e,t)=>{t.pattern??(t.pattern=b),W.init(e,t),e._zod.onattach.push(e=>{e._zod.bag.contentEncoding="base64url"}),e._zod.check=n=>{!function(e){if(!b.test(e))return!1;let t=e.replace(/[-_]/g,e=>"-"===e?"+":"/");return ed(t.padEnd(4*Math.ceil(t.length/4),"="))}(n.value)&&n.issues.push({code:"invalid_format",format:"base64url",input:n.value,inst:e,continue:!t.abort})}}),ep=r.xI("$ZodE164",(e,t)=>{t.pattern??(t.pattern=w),W.init(e,t)}),em=r.xI("$ZodJWT",(e,t)=>{W.init(e,t),e._zod.check=n=>{!function(e,t=null){try{let n=e.split(".");if(3!==n.length)return!1;let[r]=n;if(!r)return!1;let i=JSON.parse(atob(r));if("typ"in i&&i?.typ!=="JWT"||!i.alg||t&&(!("alg"in i)||i.alg!==t))return!1;return!0}catch{return!1}}(n.value,t.alg)&&n.issues.push({code:"invalid_format",format:"jwt",input:n.value,inst:e,continue:!t.abort})}}),eg=r.xI("$ZodUnknown",(e,t)=>{B.init(e,t),e._zod.parse=e=>e}),ev=r.xI("$ZodNever",(e,t)=>{B.init(e,t),e._zod.parse=(t,n)=>(t.issues.push({expected:"never",code:"invalid_type",input:t.value,inst:e}),t)});function ey(e,t,n){e.issues.length&&t.issues.push(...P.lQ(n,e.issues)),t.value[n]=e.value}let eb=r.xI("$ZodArray",(e,t)=>{B.init(e,t),e._zod.parse=(n,r)=>{let i=n.value;if(!Array.isArray(i))return n.issues.push({expected:"array",code:"invalid_type",input:i,inst:e}),n;n.value=Array(i.length);let o=[];for(let e=0;e<i.length;e++){let s=i[e],a=t.element._zod.run({value:s,issues:[]},r);a instanceof Promise?o.push(a.then(t=>ey(t,n,e))):ey(a,n,e)}return o.length?Promise.all(o).then(()=>n):n}});function ex(e,t,n){e.issues.length&&t.issues.push(...P.lQ(n,e.issues)),t.value[n]=e.value}function ew(e,t,n,r){e.issues.length?void 0===r[n]?n in r?t.value[n]=void 0:t.value[n]=e.value:t.issues.push(...P.lQ(n,e.issues)):void 0===e.value?n in r&&(t.value[n]=void 0):t.value[n]=e.value}let ek=r.xI("$ZodObject",(e,t)=>{let n,i;B.init(e,t);let o=P.PO(()=>{let e=Object.keys(t.shape);for(let n of e)if(!(t.shape[n]instanceof B))throw Error(`Invalid element at key "${n}": expected a Zod schema`);let n=P.NM(t.shape);return{shape:t.shape,keys:e,keySet:new Set(e),numKeys:e.length,optionalKeys:new Set(n)}});P.gJ(e._zod,"propValues",()=>{let e=t.shape,n={};for(let t in e){let r=e[t]._zod;if(r.values)for(let e of(n[t]??(n[t]=new Set),r.values))n[t].add(e)}return n});let s=P.Gv,a=!r.cr.jitless,l=P.hI,u=a&&l.value,c=t.catchall;e._zod.parse=(r,l)=>{i??(i=o.value);let d=r.value;if(!s(d))return r.issues.push({expected:"object",code:"invalid_type",input:d,inst:e}),r;let h=[];if(a&&u&&l?.async===!1&&!0!==l.jitless)n||(n=(e=>{let t=new $(["shape","payload","ctx"]),n=o.value,r=e=>{let t=P.UQ(e);return`shape[${t}]._zod.run({ value: input[${t}], issues: [] }, ctx)`};t.write("const input = payload.value;");let i=Object.create(null),s=0;for(let e of n.keys)i[e]=`key_${s++}`;for(let e of(t.write("const newResult = {}"),n.keys))if(n.optionalKeys.has(e)){let n=i[e];t.write(`const ${n} = ${r(e)};`);let o=P.UQ(e);t.write(`
        if (${n}.issues.length) {
          if (input[${o}] === undefined) {
            if (${o} in input) {
              newResult[${o}] = undefined;
            }
          } else {
            payload.issues = payload.issues.concat(
              ${n}.issues.map((iss) => ({
                ...iss,
                path: iss.path ? [${o}, ...iss.path] : [${o}],
              }))
            );
          }
        } else if (${n}.value === undefined) {
          if (${o} in input) newResult[${o}] = undefined;
        } else {
          newResult[${o}] = ${n}.value;
        }
        `)}else{let n=i[e];t.write(`const ${n} = ${r(e)};`),t.write(`
          if (${n}.issues.length) payload.issues = payload.issues.concat(${n}.issues.map(iss => ({
            ...iss,
            path: iss.path ? [${P.UQ(e)}, ...iss.path] : [${P.UQ(e)}]
          })));`),t.write(`newResult[${P.UQ(e)}] = ${n}.value`)}t.write("payload.value = newResult;"),t.write("return payload;");let a=t.compile();return(t,n)=>a(e,t,n)})(t.shape)),r=n(r,l);else{r.value={};let e=i.shape;for(let t of i.keys){let n=e[t],i=n._zod.run({value:d[t],issues:[]},l),o="optional"===n._zod.optin&&"optional"===n._zod.optout;i instanceof Promise?h.push(i.then(e=>o?ew(e,r,t,d):ex(e,r,t))):o?ew(i,r,t,d):ex(i,r,t)}}if(!c)return h.length?Promise.all(h).then(()=>r):r;let f=[],p=i.keySet,m=c._zod,g=m.def.type;for(let e of Object.keys(d)){if(p.has(e))continue;if("never"===g){f.push(e);continue}let t=m.run({value:d[e],issues:[]},l);t instanceof Promise?h.push(t.then(t=>ex(t,r,e))):ex(t,r,e)}return(f.length&&r.issues.push({code:"unrecognized_keys",keys:f,input:d,inst:e}),h.length)?Promise.all(h).then(()=>r):r}});function eA(e,t,n,i){for(let n of e)if(0===n.issues.length)return t.value=n.value,t;return t.issues.push({code:"invalid_union",input:t.value,inst:n,errors:e.map(e=>e.issues.map(e=>P.iR(e,i,r.$W())))}),t}let eE=r.xI("$ZodUnion",(e,t)=>{B.init(e,t),P.gJ(e._zod,"optin",()=>t.options.some(e=>"optional"===e._zod.optin)?"optional":void 0),P.gJ(e._zod,"optout",()=>t.options.some(e=>"optional"===e._zod.optout)?"optional":void 0),P.gJ(e._zod,"values",()=>{if(t.options.every(e=>e._zod.values))return new Set(t.options.flatMap(e=>Array.from(e._zod.values)))}),P.gJ(e._zod,"pattern",()=>{if(t.options.every(e=>e._zod.pattern)){let e=t.options.map(e=>e._zod.pattern);return RegExp(`^(${e.map(e=>P.p6(e.source)).join("|")})$`)}}),e._zod.parse=(n,r)=>{let i=!1,o=[];for(let e of t.options){let t=e._zod.run({value:n.value,issues:[]},r);if(t instanceof Promise)o.push(t),i=!0;else{if(0===t.issues.length)return t;o.push(t)}}return i?Promise.all(o).then(t=>eA(t,n,e,r)):eA(o,n,e,r)}}),eS=r.xI("$ZodIntersection",(e,t)=>{B.init(e,t),e._zod.parse=(e,n)=>{let r=e.value,i=t.left._zod.run({value:r,issues:[]},n),o=t.right._zod.run({value:r,issues:[]},n);return i instanceof Promise||o instanceof Promise?Promise.all([i,o]).then(([t,n])=>e_(e,t,n)):e_(e,i,o)}});function e_(e,t,n){if(t.issues.length&&e.issues.push(...t.issues),n.issues.length&&e.issues.push(...n.issues),P.QH(e))return e;let r=function e(t,n){if(t===n||t instanceof Date&&n instanceof Date&&+t==+n)return{valid:!0,data:t};if(P.Qd(t)&&P.Qd(n)){let r=Object.keys(n),i=Object.keys(t).filter(e=>-1!==r.indexOf(e)),o={...t,...n};for(let r of i){let i=e(t[r],n[r]);if(!i.valid)return{valid:!1,mergeErrorPath:[r,...i.mergeErrorPath]};o[r]=i.data}return{valid:!0,data:o}}if(Array.isArray(t)&&Array.isArray(n)){if(t.length!==n.length)return{valid:!1,mergeErrorPath:[]};let r=[];for(let i=0;i<t.length;i++){let o=e(t[i],n[i]);if(!o.valid)return{valid:!1,mergeErrorPath:[i,...o.mergeErrorPath]};r.push(o.data)}return{valid:!0,data:r}}return{valid:!1,mergeErrorPath:[]}}(t.value,n.value);if(!r.valid)throw Error(`Unmergable intersection. Error path: ${JSON.stringify(r.mergeErrorPath)}`);return e.value=r.data,e}let eP=r.xI("$ZodEnum",(e,t)=>{B.init(e,t);let n=P.w5(t.entries);e._zod.values=new Set(n),e._zod.pattern=RegExp(`^(${n.filter(e=>P.qQ.has(typeof e)).map(e=>"string"==typeof e?P.$f(e):e.toString()).join("|")})$`),e._zod.parse=(t,r)=>{let i=t.value;return e._zod.values.has(i)||t.issues.push({code:"invalid_value",values:n,input:i,inst:e}),t}}),eT=r.xI("$ZodTransform",(e,t)=>{B.init(e,t),e._zod.parse=(e,n)=>{let i=t.transform(e.value,e);if(n.async)return(i instanceof Promise?i:Promise.resolve(i)).then(t=>(e.value=t,e));if(i instanceof Promise)throw new r.GT;return e.value=i,e}}),eM=r.xI("$ZodOptional",(e,t)=>{B.init(e,t),e._zod.optin="optional",e._zod.optout="optional",P.gJ(e._zod,"values",()=>t.innerType._zod.values?new Set([...t.innerType._zod.values,void 0]):void 0),P.gJ(e._zod,"pattern",()=>{let e=t.innerType._zod.pattern;return e?RegExp(`^(${P.p6(e.source)})?$`):void 0}),e._zod.parse=(e,n)=>"optional"===t.innerType._zod.optin?t.innerType._zod.run(e,n):void 0===e.value?e:t.innerType._zod.run(e,n)}),eC=r.xI("$ZodNullable",(e,t)=>{B.init(e,t),P.gJ(e._zod,"optin",()=>t.innerType._zod.optin),P.gJ(e._zod,"optout",()=>t.innerType._zod.optout),P.gJ(e._zod,"pattern",()=>{let e=t.innerType._zod.pattern;return e?RegExp(`^(${P.p6(e.source)}|null)$`):void 0}),P.gJ(e._zod,"values",()=>t.innerType._zod.values?new Set([...t.innerType._zod.values,null]):void 0),e._zod.parse=(e,n)=>null===e.value?e:t.innerType._zod.run(e,n)}),eD=r.xI("$ZodDefault",(e,t)=>{B.init(e,t),e._zod.optin="optional",P.gJ(e._zod,"values",()=>t.innerType._zod.values),e._zod.parse=(e,n)=>{if(void 0===e.value)return e.value=t.defaultValue,e;let r=t.innerType._zod.run(e,n);return r instanceof Promise?r.then(e=>eV(e,t)):eV(r,t)}});function eV(e,t){return void 0===e.value&&(e.value=t.defaultValue),e}let ez=r.xI("$ZodPrefault",(e,t)=>{B.init(e,t),e._zod.optin="optional",P.gJ(e._zod,"values",()=>t.innerType._zod.values),e._zod.parse=(e,n)=>(void 0===e.value&&(e.value=t.defaultValue),t.innerType._zod.run(e,n))}),eI=r.xI("$ZodNonOptional",(e,t)=>{B.init(e,t),P.gJ(e._zod,"values",()=>{let e=t.innerType._zod.values;return e?new Set([...e].filter(e=>void 0!==e)):void 0}),e._zod.parse=(n,r)=>{let i=t.innerType._zod.run(n,r);return i instanceof Promise?i.then(t=>ej(t,e)):ej(i,e)}});function ej(e,t){return e.issues.length||void 0!==e.value||e.issues.push({code:"invalid_type",expected:"nonoptional",input:e.value,inst:t}),e}let eR=r.xI("$ZodCatch",(e,t)=>{B.init(e,t),e._zod.optin="optional",P.gJ(e._zod,"optout",()=>t.innerType._zod.optout),P.gJ(e._zod,"values",()=>t.innerType._zod.values),e._zod.parse=(e,n)=>{let i=t.innerType._zod.run(e,n);return i instanceof Promise?i.then(i=>(e.value=i.value,i.issues.length&&(e.value=t.catchValue({...e,error:{issues:i.issues.map(e=>P.iR(e,n,r.$W()))},input:e.value}),e.issues=[]),e)):(e.value=i.value,i.issues.length&&(e.value=t.catchValue({...e,error:{issues:i.issues.map(e=>P.iR(e,n,r.$W()))},input:e.value}),e.issues=[]),e)}}),eO=r.xI("$ZodPipe",(e,t)=>{B.init(e,t),P.gJ(e._zod,"values",()=>t.in._zod.values),P.gJ(e._zod,"optin",()=>t.in._zod.optin),P.gJ(e._zod,"optout",()=>t.out._zod.optout),P.gJ(e._zod,"propValues",()=>t.in._zod.propValues),e._zod.parse=(e,n)=>{let r=t.in._zod.run(e,n);return r instanceof Promise?r.then(e=>eF(e,t,n)):eF(r,t,n)}});function eF(e,t,n){return P.QH(e)?e:t.out._zod.run({value:e.value,issues:e.issues},n)}let eL=r.xI("$ZodReadonly",(e,t)=>{B.init(e,t),P.gJ(e._zod,"propValues",()=>t.innerType._zod.propValues),P.gJ(e._zod,"values",()=>t.innerType._zod.values),P.gJ(e._zod,"optin",()=>t.innerType._zod.optin),P.gJ(e._zod,"optout",()=>t.innerType._zod.optout),e._zod.parse=(e,n)=>{let r=t.innerType._zod.run(e,n);return r instanceof Promise?r.then(e$):e$(r)}});function e$(e){return e.value=Object.freeze(e.value),e}let eN=r.xI("$ZodCustom",(e,t)=>{T.init(e,t),B.init(e,t),e._zod.parse=(e,t)=>e,e._zod.check=n=>{let r=n.value,i=t.fn(r);if(i instanceof Promise)return i.then(t=>eU(t,n,r,e));eU(i,n,r,e)}});function eU(e,t,n,r){if(!e){let e={code:"custom",input:n,inst:r,path:[...r._zod.def.path??[]],continue:!r._zod.def.abort};r._zod.def.params&&(e.params=r._zod.def.params),t.issues.push(P.sn(e))}}Symbol("ZodOutput"),Symbol("ZodInput");class eB{constructor(){this._map=new Map,this._idmap=new Map}add(e,...t){let n=t[0];if(this._map.set(e,n),n&&"object"==typeof n&&"id"in n){if(this._idmap.has(n.id))throw Error(`ID ${n.id} already exists in the registry`);this._idmap.set(n.id,e)}return this}clear(){return this._map=new Map,this._idmap=new Map,this}remove(e){let t=this._map.get(e);return t&&"object"==typeof t&&"id"in t&&this._idmap.delete(t.id),this._map.delete(e),this}get(e){let t=e._zod.parent;if(t){let n={...this.get(t)??{}};return delete n.id,{...n,...this._map.get(e)}}return this._map.get(e)}has(e){return this._map.has(e)}}let eZ=new eB;function eW(e,t){return new e({type:"string",format:"guid",check:"string_format",abort:!1,...P.A2(t)})}function eH(e,t){return new M({check:"max_length",...P.A2(t),maximum:e})}function eq(e,t){return new C({check:"min_length",...P.A2(t),minimum:e})}function eG(e,t){return new D({check:"length_equals",...P.A2(t),length:e})}function eK(e){return new L({check:"overwrite",tx:e})}let eX=r.xI("ZodISODateTime",(e,t)=>{er.init(e,t),tt.init(e,t)}),eJ=r.xI("ZodISODate",(e,t)=>{ei.init(e,t),tt.init(e,t)}),eY=r.xI("ZodISOTime",(e,t)=>{eo.init(e,t),tt.init(e,t)}),eQ=r.xI("ZodISODuration",(e,t)=>{es.init(e,t),tt.init(e,t)});var e0=n(3793);let e1=(e,t)=>{e0.a$.init(e,t),e.name="ZodError",Object.defineProperties(e,{format:{value:t=>e0.Wk(e,t)},flatten:{value:t=>e0.JM(e,t)},addIssue:{value:t=>e.issues.push(t)},addIssues:{value:t=>e.issues.push(...t)},isEmpty:{get:()=>0===e.issues.length}})};r.xI("ZodError",e1);let e2=r.xI("ZodError",e1,{Parent:Error}),e5=N.Tj(e2),e9=N.Rb(e2),e4=N.Od(e2),e3=N.wG(e2),e6=r.xI("ZodType",(e,t)=>(B.init(e,t),e.def=t,Object.defineProperty(e,"_def",{value:t}),e.check=(...n)=>e.clone({...t,checks:[...t.checks??[],...n.map(e=>"function"==typeof e?{_zod:{check:e,def:{check:"custom"},onattach:[]}}:e)]}),e.clone=(t,n)=>P.o8(e,t,n),e.brand=()=>e,e.register=(t,n)=>(t.add(e,n),e),e.parse=(t,n)=>e5(e,t,n,{callee:e.parse}),e.safeParse=(t,n)=>e4(e,t,n),e.parseAsync=async(t,n)=>e9(e,t,n,{callee:e.parseAsync}),e.safeParseAsync=async(t,n)=>e3(e,t,n),e.spa=e.safeParseAsync,e.refine=(t,n)=>e.check(function(e,t={}){return new tU({type:"custom",check:"custom",fn:e,...P.A2(t)})}(t,n)),e.superRefine=t=>e.check(function(e){let t=function(e){let t=new T({check:"custom"});return t._zod.check=e,t}(n=>(n.addIssue=e=>{"string"==typeof e?n.issues.push(P.sn(e,n.value,t._zod.def)):(e.fatal&&(e.continue=!1),e.code??(e.code="custom"),e.input??(e.input=n.value),e.inst??(e.inst=t),e.continue??(e.continue=!t._zod.def.abort),n.issues.push(P.sn(e)))},e(n.value,n)));return t}(t)),e.overwrite=t=>e.check(eK(t)),e.optional=()=>tV(e),e.nullable=()=>tI(e),e.nullish=()=>tV(tI(e)),e.nonoptional=t=>{var n,r;return n=e,r=t,new tO({type:"nonoptional",innerType:n,...P.A2(r)})},e.array=()=>(function(e,t){return new tE({type:"array",element:e,...P.A2(t)})})(e),e.or=t=>new tP({type:"union",options:[e,t],...P.A2(void 0)}),e.and=t=>new tT({type:"intersection",left:e,right:t}),e.transform=t=>t$(e,new tC({type:"transform",transform:t})),e.default=t=>(function(e,t){return new tj({type:"default",innerType:e,get defaultValue(){return"function"==typeof t?t():t}})})(e,t),e.prefault=t=>(function(e,t){return new tR({type:"prefault",innerType:e,get defaultValue(){return"function"==typeof t?t():t}})})(e,t),e.catch=t=>(function(e,t){return new tF({type:"catch",innerType:e,catchValue:"function"==typeof t?t:()=>t})})(e,t),e.pipe=t=>t$(e,t),e.readonly=()=>new tN({type:"readonly",innerType:e}),e.describe=t=>{let n=e.clone();return eZ.add(n,{description:t}),n},Object.defineProperty(e,"description",{get:()=>eZ.get(e)?.description,configurable:!0}),e.meta=(...t)=>{if(0===t.length)return eZ.get(e);let n=e.clone();return eZ.add(n,t[0]),n},e.isOptional=()=>e.safeParse(void 0).success,e.isNullable=()=>e.safeParse(null).success,e)),e8=r.xI("_ZodString",(e,t)=>{Z.init(e,t),e6.init(e,t);let n=e._zod.bag;e.format=n.format??null,e.minLength=n.minimum??null,e.maxLength=n.maximum??null,e.regex=(...t)=>e.check(function(e,t){return new z({check:"string_format",format:"regex",...P.A2(t),pattern:e})}(...t)),e.includes=(...t)=>e.check(function(e,t){return new R({check:"string_format",format:"includes",...P.A2(t),includes:e})}(...t)),e.startsWith=(...t)=>e.check(function(e,t){return new O({check:"string_format",format:"starts_with",...P.A2(t),prefix:e})}(...t)),e.endsWith=(...t)=>e.check(function(e,t){return new F({check:"string_format",format:"ends_with",...P.A2(t),suffix:e})}(...t)),e.min=(...t)=>e.check(eq(...t)),e.max=(...t)=>e.check(eH(...t)),e.length=(...t)=>e.check(eG(...t)),e.nonempty=(...t)=>e.check(eq(1,...t)),e.lowercase=t=>e.check(new I({check:"string_format",format:"lowercase",...P.A2(t)})),e.uppercase=t=>e.check(new j({check:"string_format",format:"uppercase",...P.A2(t)})),e.trim=()=>e.check(eK(e=>e.trim())),e.normalize=(...t)=>e.check(function(e){return eK(t=>t.normalize(e))}(...t)),e.toLowerCase=()=>e.check(eK(e=>e.toLowerCase())),e.toUpperCase=()=>e.check(eK(e=>e.toUpperCase()))}),e7=r.xI("ZodString",(e,t)=>{Z.init(e,t),e8.init(e,t),e.email=t=>e.check(new tn({type:"string",format:"email",check:"string_format",abort:!1,...P.A2(t)})),e.url=t=>e.check(new to({type:"string",format:"url",check:"string_format",abort:!1,...P.A2(t)})),e.jwt=t=>e.check(new tx({type:"string",format:"jwt",check:"string_format",abort:!1,...P.A2(t)})),e.emoji=t=>e.check(new ts({type:"string",format:"emoji",check:"string_format",abort:!1,...P.A2(t)})),e.guid=t=>e.check(eW(tr,t)),e.uuid=t=>e.check(new ti({type:"string",format:"uuid",check:"string_format",abort:!1,...P.A2(t)})),e.uuidv4=t=>e.check(new ti({type:"string",format:"uuid",check:"string_format",abort:!1,version:"v4",...P.A2(t)})),e.uuidv6=t=>e.check(new ti({type:"string",format:"uuid",check:"string_format",abort:!1,version:"v6",...P.A2(t)})),e.uuidv7=t=>e.check(new ti({type:"string",format:"uuid",check:"string_format",abort:!1,version:"v7",...P.A2(t)})),e.nanoid=t=>e.check(new ta({type:"string",format:"nanoid",check:"string_format",abort:!1,...P.A2(t)})),e.guid=t=>e.check(eW(tr,t)),e.cuid=t=>e.check(new tl({type:"string",format:"cuid",check:"string_format",abort:!1,...P.A2(t)})),e.cuid2=t=>e.check(new tu({type:"string",format:"cuid2",check:"string_format",abort:!1,...P.A2(t)})),e.ulid=t=>e.check(new tc({type:"string",format:"ulid",check:"string_format",abort:!1,...P.A2(t)})),e.base64=t=>e.check(new tv({type:"string",format:"base64",check:"string_format",abort:!1,...P.A2(t)})),e.base64url=t=>e.check(new ty({type:"string",format:"base64url",check:"string_format",abort:!1,...P.A2(t)})),e.xid=t=>e.check(new td({type:"string",format:"xid",check:"string_format",abort:!1,...P.A2(t)})),e.ksuid=t=>e.check(new th({type:"string",format:"ksuid",check:"string_format",abort:!1,...P.A2(t)})),e.ipv4=t=>e.check(new tf({type:"string",format:"ipv4",check:"string_format",abort:!1,...P.A2(t)})),e.ipv6=t=>e.check(new tp({type:"string",format:"ipv6",check:"string_format",abort:!1,...P.A2(t)})),e.cidrv4=t=>e.check(new tm({type:"string",format:"cidrv4",check:"string_format",abort:!1,...P.A2(t)})),e.cidrv6=t=>e.check(new tg({type:"string",format:"cidrv6",check:"string_format",abort:!1,...P.A2(t)})),e.e164=t=>e.check(new tb({type:"string",format:"e164",check:"string_format",abort:!1,...P.A2(t)})),e.datetime=t=>e.check(function(e){return new eX({type:"string",format:"datetime",check:"string_format",offset:!1,local:!1,precision:null,...P.A2(e)})}(t)),e.date=t=>e.check(function(e){return new eJ({type:"string",format:"date",check:"string_format",...P.A2(e)})}(t)),e.time=t=>e.check(function(e){return new eY({type:"string",format:"time",check:"string_format",precision:null,...P.A2(e)})}(t)),e.duration=t=>e.check(function(e){return new eQ({type:"string",format:"duration",check:"string_format",...P.A2(e)})}(t))});function te(e){return new e7({type:"string",...P.A2(e)})}let tt=r.xI("ZodStringFormat",(e,t)=>{W.init(e,t),e8.init(e,t)}),tn=r.xI("ZodEmail",(e,t)=>{G.init(e,t),tt.init(e,t)}),tr=r.xI("ZodGUID",(e,t)=>{H.init(e,t),tt.init(e,t)}),ti=r.xI("ZodUUID",(e,t)=>{q.init(e,t),tt.init(e,t)}),to=r.xI("ZodURL",(e,t)=>{K.init(e,t),tt.init(e,t)}),ts=r.xI("ZodEmoji",(e,t)=>{X.init(e,t),tt.init(e,t)}),ta=r.xI("ZodNanoID",(e,t)=>{J.init(e,t),tt.init(e,t)}),tl=r.xI("ZodCUID",(e,t)=>{Y.init(e,t),tt.init(e,t)}),tu=r.xI("ZodCUID2",(e,t)=>{Q.init(e,t),tt.init(e,t)}),tc=r.xI("ZodULID",(e,t)=>{ee.init(e,t),tt.init(e,t)}),td=r.xI("ZodXID",(e,t)=>{et.init(e,t),tt.init(e,t)}),th=r.xI("ZodKSUID",(e,t)=>{en.init(e,t),tt.init(e,t)}),tf=r.xI("ZodIPv4",(e,t)=>{ea.init(e,t),tt.init(e,t)}),tp=r.xI("ZodIPv6",(e,t)=>{el.init(e,t),tt.init(e,t)}),tm=r.xI("ZodCIDRv4",(e,t)=>{eu.init(e,t),tt.init(e,t)}),tg=r.xI("ZodCIDRv6",(e,t)=>{ec.init(e,t),tt.init(e,t)}),tv=r.xI("ZodBase64",(e,t)=>{eh.init(e,t),tt.init(e,t)}),ty=r.xI("ZodBase64URL",(e,t)=>{ef.init(e,t),tt.init(e,t)}),tb=r.xI("ZodE164",(e,t)=>{ep.init(e,t),tt.init(e,t)}),tx=r.xI("ZodJWT",(e,t)=>{em.init(e,t),tt.init(e,t)}),tw=r.xI("ZodUnknown",(e,t)=>{eg.init(e,t),e6.init(e,t)});function tk(){return new tw({type:"unknown"})}let tA=r.xI("ZodNever",(e,t)=>{ev.init(e,t),e6.init(e,t)}),tE=r.xI("ZodArray",(e,t)=>{eb.init(e,t),e6.init(e,t),e.element=t.element,e.min=(t,n)=>e.check(eq(t,n)),e.nonempty=t=>e.check(eq(1,t)),e.max=(t,n)=>e.check(eH(t,n)),e.length=(t,n)=>e.check(eG(t,n)),e.unwrap=()=>e.element}),tS=r.xI("ZodObject",(e,t)=>{ek.init(e,t),e6.init(e,t),P.gJ(e,"shape",()=>t.shape),e.keyof=()=>(function(e,t){return new tM({type:"enum",entries:Array.isArray(e)?Object.fromEntries(e.map(e=>[e,e])):e,...P.A2(void 0)})})(Object.keys(e._zod.def.shape)),e.catchall=t=>e.clone({...e._zod.def,catchall:t}),e.passthrough=()=>e.clone({...e._zod.def,catchall:tk()}),e.loose=()=>e.clone({...e._zod.def,catchall:tk()}),e.strict=()=>e.clone({...e._zod.def,catchall:function(e){var t;return t=void 0,new tA({type:"never",...P.A2(t)})}()}),e.strip=()=>e.clone({...e._zod.def,catchall:void 0}),e.extend=t=>P.X$(e,t),e.merge=t=>P.h1(e,t),e.pick=t=>P.Up(e,t),e.omit=t=>P.cJ(e,t),e.partial=(...t)=>P.OH(tD,e,t[0]),e.required=(...t)=>P.mw(tO,e,t[0])});function t_(e,t){return new tS({type:"object",get shape(){return P.Vy(this,"shape",{...e}),this.shape},...P.A2(t)})}let tP=r.xI("ZodUnion",(e,t)=>{eE.init(e,t),e6.init(e,t),e.options=t.options}),tT=r.xI("ZodIntersection",(e,t)=>{eS.init(e,t),e6.init(e,t)}),tM=r.xI("ZodEnum",(e,t)=>{eP.init(e,t),e6.init(e,t),e.enum=t.entries,e.options=Object.values(t.entries);let n=new Set(Object.keys(t.entries));e.extract=(e,r)=>{let i={};for(let r of e)if(n.has(r))i[r]=t.entries[r];else throw Error(`Key ${r} not found in enum`);return new tM({...t,checks:[],...P.A2(r),entries:i})},e.exclude=(e,r)=>{let i={...t.entries};for(let t of e)if(n.has(t))delete i[t];else throw Error(`Key ${t} not found in enum`);return new tM({...t,checks:[],...P.A2(r),entries:i})}}),tC=r.xI("ZodTransform",(e,t)=>{eT.init(e,t),e6.init(e,t),e._zod.parse=(n,r)=>{n.addIssue=r=>{"string"==typeof r?n.issues.push(P.sn(r,n.value,t)):(r.fatal&&(r.continue=!1),r.code??(r.code="custom"),r.input??(r.input=n.value),r.inst??(r.inst=e),r.continue??(r.continue=!0),n.issues.push(P.sn(r)))};let i=t.transform(n.value,n);return i instanceof Promise?i.then(e=>(n.value=e,n)):(n.value=i,n)}}),tD=r.xI("ZodOptional",(e,t)=>{eM.init(e,t),e6.init(e,t),e.unwrap=()=>e._zod.def.innerType});function tV(e){return new tD({type:"optional",innerType:e})}let tz=r.xI("ZodNullable",(e,t)=>{eC.init(e,t),e6.init(e,t),e.unwrap=()=>e._zod.def.innerType});function tI(e){return new tz({type:"nullable",innerType:e})}let tj=r.xI("ZodDefault",(e,t)=>{eD.init(e,t),e6.init(e,t),e.unwrap=()=>e._zod.def.innerType,e.removeDefault=e.unwrap}),tR=r.xI("ZodPrefault",(e,t)=>{ez.init(e,t),e6.init(e,t),e.unwrap=()=>e._zod.def.innerType}),tO=r.xI("ZodNonOptional",(e,t)=>{eI.init(e,t),e6.init(e,t),e.unwrap=()=>e._zod.def.innerType}),tF=r.xI("ZodCatch",(e,t)=>{eR.init(e,t),e6.init(e,t),e.unwrap=()=>e._zod.def.innerType,e.removeCatch=e.unwrap}),tL=r.xI("ZodPipe",(e,t)=>{eO.init(e,t),e6.init(e,t),e.in=t.in,e.out=t.out});function t$(e,t){return new tL({type:"pipe",in:e,out:t})}let tN=r.xI("ZodReadonly",(e,t)=>{eL.init(e,t),e6.init(e,t)}),tU=r.xI("ZodCustom",(e,t)=>{eN.init(e,t),e6.init(e,t)})},8753:(e,t,n)=>{n.d(t,{EJ:()=>u,Od:()=>c,Rb:()=>l,Tj:()=>s,bp:()=>f,qg:()=>a,wG:()=>h,xL:()=>d});var r=n(4193),i=n(3793),o=n(4398);let s=e=>(t,n,i,s)=>{let a=i?Object.assign(i,{async:!1}):{async:!1},l=t._zod.run({value:n,issues:[]},a);if(l instanceof Promise)throw new r.GT;if(l.issues.length){let t=new(s?.Err??e)(l.issues.map(e=>o.iR(e,a,r.$W())));throw o.gx(t,s?.callee),t}return l.value},a=s(i.Kd),l=e=>async(t,n,i,s)=>{let a=i?Object.assign(i,{async:!0}):{async:!0},l=t._zod.run({value:n,issues:[]},a);if(l instanceof Promise&&(l=await l),l.issues.length){let t=new(s?.Err??e)(l.issues.map(e=>o.iR(e,a,r.$W())));throw o.gx(t,s?.callee),t}return l.value},u=l(i.Kd),c=e=>(t,n,s)=>{let a=s?{...s,async:!1}:{async:!1},l=t._zod.run({value:n,issues:[]},a);if(l instanceof Promise)throw new r.GT;return l.issues.length?{success:!1,error:new(e??i.a$)(l.issues.map(e=>o.iR(e,a,r.$W())))}:{success:!0,data:l.value}},d=c(i.Kd),h=e=>async(t,n,i)=>{let s=i?Object.assign(i,{async:!0}):{async:!0},a=t._zod.run({value:n,issues:[]},s);return a instanceof Promise&&(a=await a),a.issues.length?{success:!1,error:new e(a.issues.map(e=>o.iR(e,s,r.$W())))}:{success:!0,data:a.value}},f=h(i.Kd)},8859:(e,t)=>{function n(e){let t={};for(let[n,r]of e.entries()){let e=t[n];void 0===e?t[n]=r:Array.isArray(e)?e.push(r):t[n]=[e,r]}return t}function r(e){return"string"==typeof e?e:("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function i(e){let t=new URLSearchParams;for(let[n,i]of Object.entries(e))if(Array.isArray(i))for(let e of i)t.append(n,r(e));else t.set(n,r(i));return t}function o(e){for(var t=arguments.length,n=Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];for(let t of n){for(let n of t.keys())e.delete(n);for(let[n,r]of t.entries())e.append(n,r)}return e}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{assign:function(){return o},searchParamsToUrlQuery:function(){return n},urlQueryToSearchParams:function(){return i}})},8883:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},8972:(e,t,n)=>{n.d(t,{B:()=>r});let r="undefined"!=typeof window},9037:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]])},9420:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("phone",[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]])},9601:(e,t,n)=>{n.d(t,{bm:()=>ti,UC:()=>tr,hJ:()=>tn,ZL:()=>tt,bL:()=>e7,l9:()=>te});var r,i,o,s=n(2115),a=n.t(s,2);function l(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}var u=n(6101),c=n(5155),d=globalThis?.document?s.useLayoutEffect:()=>{},h=a[" useId ".trim().toString()]||(()=>void 0),f=0;function p(e){let[t,n]=s.useState(h());return d(()=>{e||n(e=>e??String(f++))},[e]),e||(t?`radix-${t}`:"")}var m=a[" useInsertionEffect ".trim().toString()]||d;Symbol("RADIX:SYNC_STATE");var g=n(3655);function v(e){let t=s.useRef(e);return s.useEffect(()=>{t.current=e}),s.useMemo(()=>(...e)=>t.current?.(...e),[])}var y="dismissableLayer.update",b=s.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),x=s.forwardRef((e,t)=>{var n,r;let{disableOutsidePointerEvents:o=!1,onEscapeKeyDown:a,onPointerDownOutside:d,onFocusOutside:h,onInteractOutside:f,onDismiss:p,...m}=e,x=s.useContext(b),[A,E]=s.useState(null),S=null!=(r=null==A?void 0:A.ownerDocument)?r:null==(n=globalThis)?void 0:n.document,[,_]=s.useState({}),P=(0,u.s)(t,e=>E(e)),T=Array.from(x.layers),[M]=[...x.layersWithOutsidePointerEventsDisabled].slice(-1),C=T.indexOf(M),D=A?T.indexOf(A):-1,V=x.layersWithOutsidePointerEventsDisabled.size>0,z=D>=C,I=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,r=v(e),i=s.useRef(!1),o=s.useRef(()=>{});return s.useEffect(()=>{let e=e=>{if(e.target&&!i.current){let t=function(){k("dismissableLayer.pointerDownOutside",r,i,{discrete:!0})},i={originalEvent:e};"touch"===e.pointerType?(n.removeEventListener("click",o.current),o.current=t,n.addEventListener("click",o.current,{once:!0})):t()}else n.removeEventListener("click",o.current);i.current=!1},t=window.setTimeout(()=>{n.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),n.removeEventListener("pointerdown",e),n.removeEventListener("click",o.current)}},[n,r]),{onPointerDownCapture:()=>i.current=!0}}(e=>{let t=e.target,n=[...x.branches].some(e=>e.contains(t));z&&!n&&(null==d||d(e),null==f||f(e),e.defaultPrevented||null==p||p())},S),j=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,r=v(e),i=s.useRef(!1);return s.useEffect(()=>{let e=e=>{e.target&&!i.current&&k("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return n.addEventListener("focusin",e),()=>n.removeEventListener("focusin",e)},[n,r]),{onFocusCapture:()=>i.current=!0,onBlurCapture:()=>i.current=!1}}(e=>{let t=e.target;![...x.branches].some(e=>e.contains(t))&&(null==h||h(e),null==f||f(e),e.defaultPrevented||null==p||p())},S);return!function(e,t=globalThis?.document){let n=v(e);s.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{D===x.layers.size-1&&(null==a||a(e),!e.defaultPrevented&&p&&(e.preventDefault(),p()))},S),s.useEffect(()=>{if(A)return o&&(0===x.layersWithOutsidePointerEventsDisabled.size&&(i=S.body.style.pointerEvents,S.body.style.pointerEvents="none"),x.layersWithOutsidePointerEventsDisabled.add(A)),x.layers.add(A),w(),()=>{o&&1===x.layersWithOutsidePointerEventsDisabled.size&&(S.body.style.pointerEvents=i)}},[A,S,o,x]),s.useEffect(()=>()=>{A&&(x.layers.delete(A),x.layersWithOutsidePointerEventsDisabled.delete(A),w())},[A,x]),s.useEffect(()=>{let e=()=>_({});return document.addEventListener(y,e),()=>document.removeEventListener(y,e)},[]),(0,c.jsx)(g.sG.div,{...m,ref:P,style:{pointerEvents:V?z?"auto":"none":void 0,...e.style},onFocusCapture:l(e.onFocusCapture,j.onFocusCapture),onBlurCapture:l(e.onBlurCapture,j.onBlurCapture),onPointerDownCapture:l(e.onPointerDownCapture,I.onPointerDownCapture)})});function w(){let e=new CustomEvent(y);document.dispatchEvent(e)}function k(e,t,n,r){let{discrete:i}=r,o=n.originalEvent.target,s=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),i?(0,g.hO)(o,s):o.dispatchEvent(s)}x.displayName="DismissableLayer",s.forwardRef((e,t)=>{let n=s.useContext(b),r=s.useRef(null),i=(0,u.s)(t,r);return s.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,c.jsx)(g.sG.div,{...e,ref:i})}).displayName="DismissableLayerBranch";var A="focusScope.autoFocusOnMount",E="focusScope.autoFocusOnUnmount",S={bubbles:!1,cancelable:!0},_=s.forwardRef((e,t)=>{let{loop:n=!1,trapped:r=!1,onMountAutoFocus:i,onUnmountAutoFocus:o,...a}=e,[l,d]=s.useState(null),h=v(i),f=v(o),p=s.useRef(null),m=(0,u.s)(t,e=>d(e)),y=s.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;s.useEffect(()=>{if(r){let e=function(e){if(y.paused||!l)return;let t=e.target;l.contains(t)?p.current=t:M(p.current,{select:!0})},t=function(e){if(y.paused||!l)return;let t=e.relatedTarget;null!==t&&(l.contains(t)||M(p.current,{select:!0}))};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&M(l)});return l&&n.observe(l,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[r,l,y.paused]),s.useEffect(()=>{if(l){C.add(y);let e=document.activeElement;if(!l.contains(e)){let t=new CustomEvent(A,S);l.addEventListener(A,h),l.dispatchEvent(t),t.defaultPrevented||(function(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=document.activeElement;for(let r of e)if(M(r,{select:t}),document.activeElement!==n)return}(P(l).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&M(l))}return()=>{l.removeEventListener(A,h),setTimeout(()=>{let t=new CustomEvent(E,S);l.addEventListener(E,f),l.dispatchEvent(t),t.defaultPrevented||M(null!=e?e:document.body,{select:!0}),l.removeEventListener(E,f),C.remove(y)},0)}}},[l,h,f,y]);let b=s.useCallback(e=>{if(!n&&!r||y.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,i=document.activeElement;if(t&&i){let t=e.currentTarget,[r,o]=function(e){let t=P(e);return[T(t,e),T(t.reverse(),e)]}(t);r&&o?e.shiftKey||i!==o?e.shiftKey&&i===r&&(e.preventDefault(),n&&M(o,{select:!0})):(e.preventDefault(),n&&M(r,{select:!0})):i===t&&e.preventDefault()}},[n,r,y.paused]);return(0,c.jsx)(g.sG.div,{tabIndex:-1,...a,ref:m,onKeyDown:b})});function P(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function T(e,t){for(let n of e)if(!function(e,t){let{upTo:n}=t;if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===n||e!==n);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function M(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}_.displayName="FocusScope";var C=function(){let e=[];return{add(t){let n=e[0];t!==n&&(null==n||n.pause()),(e=D(e,t)).unshift(t)},remove(t){var n;null==(n=(e=D(e,t))[0])||n.resume()}}}();function D(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}var V=n(7650),z=s.forwardRef((e,t)=>{var n,r;let{container:i,...o}=e,[a,l]=s.useState(!1);d(()=>l(!0),[]);let u=i||a&&(null==(r=globalThis)||null==(n=r.document)?void 0:n.body);return u?V.createPortal((0,c.jsx)(g.sG.div,{...o,ref:t}),u):null});z.displayName="Portal";var I=e=>{let{present:t,children:n}=e,r=function(e){var t,n;let[r,i]=s.useState(),o=s.useRef(null),a=s.useRef(e),l=s.useRef("none"),[u,c]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},s.useReducer((e,t)=>{let r=n[e][t];return null!=r?r:e},t));return s.useEffect(()=>{let e=j(o.current);l.current="mounted"===u?e:"none"},[u]),d(()=>{let t=o.current,n=a.current;if(n!==e){let r=l.current,i=j(t);e?c("MOUNT"):"none"===i||(null==t?void 0:t.display)==="none"?c("UNMOUNT"):n&&r!==i?c("ANIMATION_OUT"):c("UNMOUNT"),a.current=e}},[e,c]),d(()=>{if(r){var e;let t,n=null!=(e=r.ownerDocument.defaultView)?e:window,i=e=>{let i=j(o.current).includes(e.animationName);if(e.target===r&&i&&(c("ANIMATION_END"),!a.current)){let e=r.style.animationFillMode;r.style.animationFillMode="forwards",t=n.setTimeout(()=>{"forwards"===r.style.animationFillMode&&(r.style.animationFillMode=e)})}},s=e=>{e.target===r&&(l.current=j(o.current))};return r.addEventListener("animationstart",s),r.addEventListener("animationcancel",i),r.addEventListener("animationend",i),()=>{n.clearTimeout(t),r.removeEventListener("animationstart",s),r.removeEventListener("animationcancel",i),r.removeEventListener("animationend",i)}}c("ANIMATION_END")},[r,c]),{isPresent:["mounted","unmountSuspended"].includes(u),ref:s.useCallback(e=>{o.current=e?getComputedStyle(e):null,i(e)},[])}}(t),i="function"==typeof n?n({present:r.isPresent}):s.Children.only(n),o=(0,u.s)(r.ref,function(e){var t,n;let r=null==(t=Object.getOwnPropertyDescriptor(e.props,"ref"))?void 0:t.get,i=r&&"isReactWarning"in r&&r.isReactWarning;return i?e.ref:(i=(r=null==(n=Object.getOwnPropertyDescriptor(e,"ref"))?void 0:n.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(i));return"function"==typeof n||r.isPresent?s.cloneElement(i,{ref:o}):null};function j(e){return(null==e?void 0:e.animationName)||"none"}I.displayName="Presence";var R=0;function O(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var F=function(){return(F=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var i in t=arguments[n])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e}).apply(this,arguments)};function L(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var i=0,r=Object.getOwnPropertySymbols(e);i<r.length;i++)0>t.indexOf(r[i])&&Object.prototype.propertyIsEnumerable.call(e,r[i])&&(n[r[i]]=e[r[i]]);return n}Object.create;Object.create;var $=("function"==typeof SuppressedError&&SuppressedError,"right-scroll-bar-position"),N="width-before-scroll-bar";function U(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var B="undefined"!=typeof window?s.useLayoutEffect:s.useEffect,Z=new WeakMap;function W(e){return e}var H=function(e){void 0===e&&(e={});var t,n,r,i=(void 0===t&&(t=W),n=[],r=!1,{read:function(){if(r)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:null},useMedium:function(e){var i=t(e,r);return n.push(i),function(){n=n.filter(function(e){return e!==i})}},assignSyncMedium:function(e){for(r=!0;n.length;){var t=n;n=[],t.forEach(e)}n={push:function(t){return e(t)},filter:function(){return n}}},assignMedium:function(e){r=!0;var t=[];if(n.length){var i=n;n=[],i.forEach(e),t=n}var o=function(){var n=t;t=[],n.forEach(e)},s=function(){return Promise.resolve().then(o)};s(),n={push:function(e){t.push(e),s()},filter:function(e){return t=t.filter(e),n}}}});return i.options=F({async:!0,ssr:!1},e),i}(),q=function(){},G=s.forwardRef(function(e,t){var n,r,i,o,a=s.useRef(null),l=s.useState({onScrollCapture:q,onWheelCapture:q,onTouchMoveCapture:q}),u=l[0],c=l[1],d=e.forwardProps,h=e.children,f=e.className,p=e.removeScrollBar,m=e.enabled,g=e.shards,v=e.sideCar,y=e.noRelative,b=e.noIsolation,x=e.inert,w=e.allowPinchZoom,k=e.as,A=e.gapMode,E=L(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),S=(n=[a,t],r=function(e){return n.forEach(function(t){return U(t,e)})},(i=(0,s.useState)(function(){return{value:null,callback:r,facade:{get current(){return i.value},set current(value){var e=i.value;e!==value&&(i.value=value,i.callback(value,e))}}}})[0]).callback=r,o=i.facade,B(function(){var e=Z.get(o);if(e){var t=new Set(e),r=new Set(n),i=o.current;t.forEach(function(e){r.has(e)||U(e,null)}),r.forEach(function(e){t.has(e)||U(e,i)})}Z.set(o,n)},[n]),o),_=F(F({},E),u);return s.createElement(s.Fragment,null,m&&s.createElement(v,{sideCar:H,removeScrollBar:p,shards:g,noRelative:y,noIsolation:b,inert:x,setCallbacks:c,allowPinchZoom:!!w,lockRef:a,gapMode:A}),d?s.cloneElement(s.Children.only(h),F(F({},_),{ref:S})):s.createElement(void 0===k?"div":k,F({},_,{className:f,ref:S}),h))});G.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},G.classNames={fullWidth:N,zeroRight:$};var K=function(e){var t=e.sideCar,n=L(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw Error("Sidecar medium not found");return s.createElement(r,F({},n))};K.isSideCarExport=!0;var X=function(){var e=0,t=null;return{add:function(r){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=o||n.nc;return t&&e.setAttribute("nonce",t),e}())){var i,s;(i=t).styleSheet?i.styleSheet.cssText=r:i.appendChild(document.createTextNode(r)),s=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(s)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},J=function(){var e=X();return function(t,n){s.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},Y=function(){var e=J();return function(t){return e(t.styles,t.dynamic),null}},Q={left:0,top:0,right:0,gap:0},ee=function(e){return parseInt(e||"",10)||0},et=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],i=t["padding"===e?"paddingRight":"marginRight"];return[ee(n),ee(r),ee(i)]},en=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return Q;var t=et(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},er=Y(),ei="data-scroll-locked",eo=function(e,t,n,r){var i=e.left,o=e.top,s=e.right,a=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(a,"px ").concat(r,";\n  }\n  body[").concat(ei,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(i,"px;\n    padding-top: ").concat(o,"px;\n    padding-right: ").concat(s,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(a,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(a,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat($," {\n    right: ").concat(a,"px ").concat(r,";\n  }\n  \n  .").concat(N," {\n    margin-right: ").concat(a,"px ").concat(r,";\n  }\n  \n  .").concat($," .").concat($," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(N," .").concat(N," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(ei,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(a,"px;\n  }\n")},es=function(){var e=parseInt(document.body.getAttribute(ei)||"0",10);return isFinite(e)?e:0},ea=function(){s.useEffect(function(){return document.body.setAttribute(ei,(es()+1).toString()),function(){var e=es()-1;e<=0?document.body.removeAttribute(ei):document.body.setAttribute(ei,e.toString())}},[])},el=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,i=void 0===r?"margin":r;ea();var o=s.useMemo(function(){return en(i)},[i]);return s.createElement(er,{styles:eo(o,!t,i,n?"":"!important")})},eu=!1;if("undefined"!=typeof window)try{var ec=Object.defineProperty({},"passive",{get:function(){return eu=!0,!0}});window.addEventListener("test",ec,ec),window.removeEventListener("test",ec,ec)}catch(e){eu=!1}var ed=!!eu&&{passive:!1},eh=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&(n.overflowY!==n.overflowX||"TEXTAREA"===e.tagName||"visible"!==n[t])},ef=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),ep(e,r)){var i=em(e,r);if(i[1]>i[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},ep=function(e,t){return"v"===e?eh(t,"overflowY"):eh(t,"overflowX")},em=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},eg=function(e,t,n,r,i){var o,s=(o=window.getComputedStyle(t).direction,"h"===e&&"rtl"===o?-1:1),a=s*r,l=n.target,u=t.contains(l),c=!1,d=a>0,h=0,f=0;do{if(!l)break;var p=em(e,l),m=p[0],g=p[1]-p[2]-s*m;(m||g)&&ep(e,l)&&(h+=g,f+=m);var v=l.parentNode;l=v&&v.nodeType===Node.DOCUMENT_FRAGMENT_NODE?v.host:v}while(!u&&l!==document.body||u&&(t.contains(l)||t===l));return d&&(i&&1>Math.abs(h)||!i&&a>h)?c=!0:!d&&(i&&1>Math.abs(f)||!i&&-a>f)&&(c=!0),c},ev=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},ey=function(e){return[e.deltaX,e.deltaY]},eb=function(e){return e&&"current"in e?e.current:e},ex=0,ew=[];let ek=(r=function(e){var t=s.useRef([]),n=s.useRef([0,0]),r=s.useRef(),i=s.useState(ex++)[0],o=s.useState(Y)[0],a=s.useRef(e);s.useEffect(function(){a.current=e},[e]),s.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(i));var t=(function(e,t,n){if(n||2==arguments.length)for(var r,i=0,o=t.length;i<o;i++)!r&&i in t||(r||(r=Array.prototype.slice.call(t,0,i)),r[i]=t[i]);return e.concat(r||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(eb),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(i))}),function(){document.body.classList.remove("block-interactivity-".concat(i)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(i))})}}},[e.inert,e.lockRef.current,e.shards]);var l=s.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!a.current.allowPinchZoom;var i,o=ev(e),s=n.current,l="deltaX"in e?e.deltaX:s[0]-o[0],u="deltaY"in e?e.deltaY:s[1]-o[1],c=e.target,d=Math.abs(l)>Math.abs(u)?"h":"v";if("touches"in e&&"h"===d&&"range"===c.type)return!1;var h=ef(d,c);if(!h)return!0;if(h?i=d:(i="v"===d?"h":"v",h=ef(d,c)),!h)return!1;if(!r.current&&"changedTouches"in e&&(l||u)&&(r.current=i),!i)return!0;var f=r.current||i;return eg(f,t,e,"h"===f?l:u,!0)},[]),u=s.useCallback(function(e){if(ew.length&&ew[ew.length-1]===o){var n="deltaY"in e?ey(e):ev(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta,r[0]===n[0]&&r[1]===n[1])})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var i=(a.current.shards||[]).map(eb).filter(Boolean).filter(function(t){return t.contains(e.target)});(i.length>0?l(e,i[0]):!a.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),c=s.useCallback(function(e,n,r,i){var o={name:e,delta:n,target:r,should:i,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(o),setTimeout(function(){t.current=t.current.filter(function(e){return e!==o})},1)},[]),d=s.useCallback(function(e){n.current=ev(e),r.current=void 0},[]),h=s.useCallback(function(t){c(t.type,ey(t),t.target,l(t,e.lockRef.current))},[]),f=s.useCallback(function(t){c(t.type,ev(t),t.target,l(t,e.lockRef.current))},[]);s.useEffect(function(){return ew.push(o),e.setCallbacks({onScrollCapture:h,onWheelCapture:h,onTouchMoveCapture:f}),document.addEventListener("wheel",u,ed),document.addEventListener("touchmove",u,ed),document.addEventListener("touchstart",d,ed),function(){ew=ew.filter(function(e){return e!==o}),document.removeEventListener("wheel",u,ed),document.removeEventListener("touchmove",u,ed),document.removeEventListener("touchstart",d,ed)}},[]);var p=e.removeScrollBar,m=e.inert;return s.createElement(s.Fragment,null,m?s.createElement(o,{styles:"\n  .block-interactivity-".concat(i," {pointer-events: none;}\n  .allow-interactivity-").concat(i," {pointer-events: all;}\n")}):null,p?s.createElement(el,{noRelative:e.noRelative,gapMode:e.gapMode}):null)},H.useMedium(r),K);var eA=s.forwardRef(function(e,t){return s.createElement(G,F({},e,{ref:t,sideCar:ek}))});eA.classNames=G.classNames;var eE=new WeakMap,eS=new WeakMap,e_={},eP=0,eT=function(e){return e&&(e.host||eT(e.parentNode))},eM=function(e,t,n,r){var i=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=eT(e);return n&&t.contains(n)?n:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});e_[n]||(e_[n]=new WeakMap);var o=e_[n],s=[],a=new Set,l=new Set(i),u=function(e){!e||a.has(e)||(a.add(e),u(e.parentNode))};i.forEach(u);var c=function(e){!e||l.has(e)||Array.prototype.forEach.call(e.children,function(e){if(a.has(e))c(e);else try{var t=e.getAttribute(r),i=null!==t&&"false"!==t,l=(eE.get(e)||0)+1,u=(o.get(e)||0)+1;eE.set(e,l),o.set(e,u),s.push(e),1===l&&i&&eS.set(e,!0),1===u&&e.setAttribute(n,"true"),i||e.setAttribute(r,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return c(t),a.clear(),eP++,function(){s.forEach(function(e){var t=eE.get(e)-1,i=o.get(e)-1;eE.set(e,t),o.set(e,i),t||(eS.has(e)||e.removeAttribute(r),eS.delete(e)),i||e.removeAttribute(n)}),--eP||(eE=new WeakMap,eE=new WeakMap,eS=new WeakMap,e_={})}},eC=function(e,t,n){void 0===n&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),i=t||("undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body);return i?(r.push.apply(r,Array.from(i.querySelectorAll("[aria-live], script"))),eM(r,i,n,"aria-hidden")):function(){return null}},eD=n(9708),eV="Dialog",[ez,eI]=function(e,t=[]){let n=[],r=()=>{let t=n.map(e=>s.createContext(e));return function(n){let r=n?.[e]||t;return s.useMemo(()=>({[`__scope${e}`]:{...n,[e]:r}}),[n,r])}};return r.scopeName=e,[function(t,r){let i=s.createContext(r),o=n.length;n=[...n,r];let a=t=>{let{scope:n,children:r,...a}=t,l=n?.[e]?.[o]||i,u=s.useMemo(()=>a,Object.values(a));return(0,c.jsx)(l.Provider,{value:u,children:r})};return a.displayName=t+"Provider",[a,function(n,a){let l=a?.[e]?.[o]||i,u=s.useContext(l);if(u)return u;if(void 0!==r)return r;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let r=n.reduce((t,{useScope:n,scopeName:r})=>{let i=n(e)[`__scope${r}`];return{...t,...i}},{});return s.useMemo(()=>({[`__scope${t.scopeName}`]:r}),[r])}};return n.scopeName=t.scopeName,n}(r,...t)]}(eV),[ej,eR]=ez(eV),eO=e=>{let{__scopeDialog:t,children:n,open:r,defaultOpen:i,onOpenChange:o,modal:a=!0}=e,l=s.useRef(null),u=s.useRef(null),[d,h]=function({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){let[i,o,a]=function({defaultProp:e,onChange:t}){let[n,r]=s.useState(e),i=s.useRef(n),o=s.useRef(t);return m(()=>{o.current=t},[t]),s.useEffect(()=>{i.current!==n&&(o.current?.(n),i.current=n)},[n,i]),[n,r,o]}({defaultProp:t,onChange:n}),l=void 0!==e,u=l?e:i;{let t=s.useRef(void 0!==e);s.useEffect(()=>{let e=t.current;if(e!==l){let t=l?"controlled":"uncontrolled";console.warn(`${r} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=l},[l,r])}return[u,s.useCallback(t=>{if(l){let n="function"==typeof t?t(e):t;n!==e&&a.current?.(n)}else o(t)},[l,e,o,a])]}({prop:r,defaultProp:null!=i&&i,onChange:o,caller:eV});return(0,c.jsx)(ej,{scope:t,triggerRef:l,contentRef:u,contentId:p(),titleId:p(),descriptionId:p(),open:d,onOpenChange:h,onOpenToggle:s.useCallback(()=>h(e=>!e),[h]),modal:a,children:n})};eO.displayName=eV;var eF="DialogTrigger",eL=s.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,i=eR(eF,n),o=(0,u.s)(t,i.triggerRef);return(0,c.jsx)(g.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":i.open,"aria-controls":i.contentId,"data-state":e5(i.open),...r,ref:o,onClick:l(e.onClick,i.onOpenToggle)})});eL.displayName=eF;var e$="DialogPortal",[eN,eU]=ez(e$,{forceMount:void 0}),eB=e=>{let{__scopeDialog:t,forceMount:n,children:r,container:i}=e,o=eR(e$,t);return(0,c.jsx)(eN,{scope:t,forceMount:n,children:s.Children.map(r,e=>(0,c.jsx)(I,{present:n||o.open,children:(0,c.jsx)(z,{asChild:!0,container:i,children:e})}))})};eB.displayName=e$;var eZ="DialogOverlay",eW=s.forwardRef((e,t)=>{let n=eU(eZ,e.__scopeDialog),{forceMount:r=n.forceMount,...i}=e,o=eR(eZ,e.__scopeDialog);return o.modal?(0,c.jsx)(I,{present:r||o.open,children:(0,c.jsx)(eq,{...i,ref:t})}):null});eW.displayName=eZ;var eH=(0,eD.TL)("DialogOverlay.RemoveScroll"),eq=s.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,i=eR(eZ,n);return(0,c.jsx)(eA,{as:eH,allowPinchZoom:!0,shards:[i.contentRef],children:(0,c.jsx)(g.sG.div,{"data-state":e5(i.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),eG="DialogContent",eK=s.forwardRef((e,t)=>{let n=eU(eG,e.__scopeDialog),{forceMount:r=n.forceMount,...i}=e,o=eR(eG,e.__scopeDialog);return(0,c.jsx)(I,{present:r||o.open,children:o.modal?(0,c.jsx)(eX,{...i,ref:t}):(0,c.jsx)(eJ,{...i,ref:t})})});eK.displayName=eG;var eX=s.forwardRef((e,t)=>{let n=eR(eG,e.__scopeDialog),r=s.useRef(null),i=(0,u.s)(t,n.contentRef,r);return s.useEffect(()=>{let e=r.current;if(e)return eC(e)},[]),(0,c.jsx)(eY,{...e,ref:i,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:l(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null==(t=n.triggerRef.current)||t.focus()}),onPointerDownOutside:l(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey;(2===t.button||n)&&e.preventDefault()}),onFocusOutside:l(e.onFocusOutside,e=>e.preventDefault())})}),eJ=s.forwardRef((e,t)=>{let n=eR(eG,e.__scopeDialog),r=s.useRef(!1),i=s.useRef(!1);return(0,c.jsx)(eY,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var o,s;null==(o=e.onCloseAutoFocus)||o.call(e,t),t.defaultPrevented||(r.current||null==(s=n.triggerRef.current)||s.focus(),t.preventDefault()),r.current=!1,i.current=!1},onInteractOutside:t=>{var o,s;null==(o=e.onInteractOutside)||o.call(e,t),t.defaultPrevented||(r.current=!0,"pointerdown"===t.detail.originalEvent.type&&(i.current=!0));let a=t.target;(null==(s=n.triggerRef.current)?void 0:s.contains(a))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&i.current&&t.preventDefault()}})}),eY=s.forwardRef((e,t)=>{let{__scopeDialog:n,trapFocus:r,onOpenAutoFocus:i,onCloseAutoFocus:o,...a}=e,l=eR(eG,n),d=s.useRef(null),h=(0,u.s)(t,d);return s.useEffect(()=>{var e,t;let n=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",null!=(e=n[0])?e:O()),document.body.insertAdjacentElement("beforeend",null!=(t=n[1])?t:O()),R++,()=>{1===R&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),R--}},[]),(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)(_,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:i,onUnmountAutoFocus:o,children:(0,c.jsx)(x,{role:"dialog",id:l.contentId,"aria-describedby":l.descriptionId,"aria-labelledby":l.titleId,"data-state":e5(l.open),...a,ref:h,onDismiss:()=>l.onOpenChange(!1)})}),(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)(e6,{titleId:l.titleId}),(0,c.jsx)(e8,{contentRef:d,descriptionId:l.descriptionId})]})]})}),eQ="DialogTitle";s.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,i=eR(eQ,n);return(0,c.jsx)(g.sG.h2,{id:i.titleId,...r,ref:t})}).displayName=eQ;var e0="DialogDescription";s.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,i=eR(e0,n);return(0,c.jsx)(g.sG.p,{id:i.descriptionId,...r,ref:t})}).displayName=e0;var e1="DialogClose",e2=s.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,i=eR(e1,n);return(0,c.jsx)(g.sG.button,{type:"button",...r,ref:t,onClick:l(e.onClick,()=>i.onOpenChange(!1))})});function e5(e){return e?"open":"closed"}e2.displayName=e1;var e9="DialogTitleWarning",[e4,e3]=function(e,t){let n=s.createContext(t),r=e=>{let{children:t,...r}=e,i=s.useMemo(()=>r,Object.values(r));return(0,c.jsx)(n.Provider,{value:i,children:t})};return r.displayName=e+"Provider",[r,function(r){let i=s.useContext(n);if(i)return i;if(void 0!==t)return t;throw Error(`\`${r}\` must be used within \`${e}\``)}]}(e9,{contentName:eG,titleName:eQ,docsSlug:"dialog"}),e6=e=>{let{titleId:t}=e,n=e3(e9),r="`".concat(n.contentName,"` requires a `").concat(n.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(n.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(n.docsSlug);return s.useEffect(()=>{t&&(document.getElementById(t)||console.error(r))},[r,t]),null},e8=e=>{let{contentRef:t,descriptionId:n}=e,r=e3("DialogDescriptionWarning"),i="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(r.contentName,"}.");return s.useEffect(()=>{var e;let r=null==(e=t.current)?void 0:e.getAttribute("aria-describedby");n&&r&&(document.getElementById(n)||console.warn(i))},[i,t,n]),null},e7=eO,te=eL,tt=eB,tn=eW,tr=eK,ti=e2},9688:(e,t,n)=>{n.d(t,{QP:()=>ee});let r=(e,t)=>{if(0===e.length)return t.classGroupId;let n=e[0],i=t.nextPart.get(n),o=i?r(e.slice(1),i):void 0;if(o)return o;if(0===t.validators.length)return;let s=e.join("-");return t.validators.find(({validator:e})=>e(s))?.classGroupId},i=/^\[(.+)\]$/,o=(e,t,n,r)=>{e.forEach(e=>{if("string"==typeof e){(""===e?t:s(t,e)).classGroupId=n;return}if("function"==typeof e)return a(e)?void o(e(r),t,n,r):void t.validators.push({validator:e,classGroupId:n});Object.entries(e).forEach(([e,i])=>{o(i,s(t,e),n,r)})})},s=(e,t)=>{let n=e;return t.split("-").forEach(e=>{n.nextPart.has(e)||n.nextPart.set(e,{nextPart:new Map,validators:[]}),n=n.nextPart.get(e)}),n},a=e=>e.isThemeGetter,l=/\s+/;function u(){let e,t,n=0,r="";for(;n<arguments.length;)(e=arguments[n++])&&(t=c(e))&&(r&&(r+=" "),r+=t);return r}let c=e=>{let t;if("string"==typeof e)return e;let n="";for(let r=0;r<e.length;r++)e[r]&&(t=c(e[r]))&&(n&&(n+=" "),n+=t);return n},d=e=>{let t=t=>t[e]||[];return t.isThemeGetter=!0,t},h=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,f=/^\((?:(\w[\w-]*):)?(.+)\)$/i,p=/^\d+\/\d+$/,m=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,g=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,v=/^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\(.+\)$/,y=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,b=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,x=e=>p.test(e),w=e=>!!e&&!Number.isNaN(Number(e)),k=e=>!!e&&Number.isInteger(Number(e)),A=e=>e.endsWith("%")&&w(e.slice(0,-1)),E=e=>m.test(e),S=()=>!0,_=e=>g.test(e)&&!v.test(e),P=()=>!1,T=e=>y.test(e),M=e=>b.test(e),C=e=>!V(e)&&!F(e),D=e=>W(e,K,P),V=e=>h.test(e),z=e=>W(e,X,_),I=e=>W(e,J,w),j=e=>W(e,q,P),R=e=>W(e,G,M),O=e=>W(e,Q,T),F=e=>f.test(e),L=e=>H(e,X),$=e=>H(e,Y),N=e=>H(e,q),U=e=>H(e,K),B=e=>H(e,G),Z=e=>H(e,Q,!0),W=(e,t,n)=>{let r=h.exec(e);return!!r&&(r[1]?t(r[1]):n(r[2]))},H=(e,t,n=!1)=>{let r=f.exec(e);return!!r&&(r[1]?t(r[1]):n)},q=e=>"position"===e||"percentage"===e,G=e=>"image"===e||"url"===e,K=e=>"length"===e||"size"===e||"bg-size"===e,X=e=>"length"===e,J=e=>"number"===e,Y=e=>"family-name"===e,Q=e=>"shadow"===e;Symbol.toStringTag;let ee=function(e,...t){let n,s,a,c=function(l){let u;return s=(n={cache:(e=>{if(e<1)return{get:()=>void 0,set:()=>{}};let t=0,n=new Map,r=new Map,i=(i,o)=>{n.set(i,o),++t>e&&(t=0,r=n,n=new Map)};return{get(e){let t=n.get(e);return void 0!==t?t:void 0!==(t=r.get(e))?(i(e,t),t):void 0},set(e,t){n.has(e)?n.set(e,t):i(e,t)}}})((u=t.reduce((e,t)=>t(e),e())).cacheSize),parseClassName:(e=>{let{prefix:t,experimentalParseClassName:n}=e,r=e=>{let t,n,r=[],i=0,o=0,s=0;for(let n=0;n<e.length;n++){let a=e[n];if(0===i&&0===o){if(":"===a){r.push(e.slice(s,n)),s=n+1;continue}if("/"===a){t=n;continue}}"["===a?i++:"]"===a?i--:"("===a?o++:")"===a&&o--}let a=0===r.length?e:e.substring(s),l=(n=a).endsWith("!")?n.substring(0,n.length-1):n.startsWith("!")?n.substring(1):n;return{modifiers:r,hasImportantModifier:l!==a,baseClassName:l,maybePostfixModifierPosition:t&&t>s?t-s:void 0}};if(t){let e=t+":",n=r;r=t=>t.startsWith(e)?n(t.substring(e.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:t,maybePostfixModifierPosition:void 0}}if(n){let e=r;r=t=>n({className:t,parseClassName:e})}return r})(u),sortModifiers:(e=>{let t=Object.fromEntries(e.orderSensitiveModifiers.map(e=>[e,!0]));return e=>{if(e.length<=1)return e;let n=[],r=[];return e.forEach(e=>{"["===e[0]||t[e]?(n.push(...r.sort(),e),r=[]):r.push(e)}),n.push(...r.sort()),n}})(u),...(e=>{let t=(e=>{let{theme:t,classGroups:n}=e,r={nextPart:new Map,validators:[]};for(let e in n)o(n[e],r,e,t);return r})(e),{conflictingClassGroups:n,conflictingClassGroupModifiers:s}=e;return{getClassGroupId:e=>{let n=e.split("-");return""===n[0]&&1!==n.length&&n.shift(),r(n,t)||(e=>{if(i.test(e)){let t=i.exec(e)[1],n=t?.substring(0,t.indexOf(":"));if(n)return"arbitrary.."+n}})(e)},getConflictingClassGroupIds:(e,t)=>{let r=n[e]||[];return t&&s[e]?[...r,...s[e]]:r}}})(u)}).cache.get,a=n.cache.set,c=d,d(l)};function d(e){let t=s(e);if(t)return t;let r=((e,t)=>{let{parseClassName:n,getClassGroupId:r,getConflictingClassGroupIds:i,sortModifiers:o}=t,s=[],a=e.trim().split(l),u="";for(let e=a.length-1;e>=0;e-=1){let t=a[e],{isExternal:l,modifiers:c,hasImportantModifier:d,baseClassName:h,maybePostfixModifierPosition:f}=n(t);if(l){u=t+(u.length>0?" "+u:u);continue}let p=!!f,m=r(p?h.substring(0,f):h);if(!m){if(!p||!(m=r(h))){u=t+(u.length>0?" "+u:u);continue}p=!1}let g=o(c).join(":"),v=d?g+"!":g,y=v+m;if(s.includes(y))continue;s.push(y);let b=i(m,p);for(let e=0;e<b.length;++e){let t=b[e];s.push(v+t)}u=t+(u.length>0?" "+u:u)}return u})(e,n);return a(e,r),r}return function(){return c(u.apply(null,arguments))}}(()=>{let e=d("color"),t=d("font"),n=d("text"),r=d("font-weight"),i=d("tracking"),o=d("leading"),s=d("breakpoint"),a=d("container"),l=d("spacing"),u=d("radius"),c=d("shadow"),h=d("inset-shadow"),f=d("text-shadow"),p=d("drop-shadow"),m=d("blur"),g=d("perspective"),v=d("aspect"),y=d("ease"),b=d("animate"),_=()=>["auto","avoid","all","avoid-page","page","left","right","column"],P=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],T=()=>[...P(),F,V],M=()=>["auto","hidden","clip","visible","scroll"],W=()=>["auto","contain","none"],H=()=>[F,V,l],q=()=>[x,"full","auto",...H()],G=()=>[k,"none","subgrid",F,V],K=()=>["auto",{span:["full",k,F,V]},k,F,V],X=()=>[k,"auto",F,V],J=()=>["auto","min","max","fr",F,V],Y=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],Q=()=>["start","end","center","stretch","center-safe","end-safe"],ee=()=>["auto",...H()],et=()=>[x,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...H()],en=()=>[e,F,V],er=()=>[...P(),N,j,{position:[F,V]}],ei=()=>["no-repeat",{repeat:["","x","y","space","round"]}],eo=()=>["auto","cover","contain",U,D,{size:[F,V]}],es=()=>[A,L,z],ea=()=>["","none","full",u,F,V],el=()=>["",w,L,z],eu=()=>["solid","dashed","dotted","double"],ec=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],ed=()=>[w,A,N,j],eh=()=>["","none",m,F,V],ef=()=>["none",w,F,V],ep=()=>["none",w,F,V],em=()=>[w,F,V],eg=()=>[x,"full",...H()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[E],breakpoint:[E],color:[S],container:[E],"drop-shadow":[E],ease:["in","out","in-out"],font:[C],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[E],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[E],shadow:[E],spacing:["px",w],text:[E],"text-shadow":[E],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",x,V,F,v]}],container:["container"],columns:[{columns:[w,V,F,a]}],"break-after":[{"break-after":_()}],"break-before":[{"break-before":_()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:T()}],overflow:[{overflow:M()}],"overflow-x":[{"overflow-x":M()}],"overflow-y":[{"overflow-y":M()}],overscroll:[{overscroll:W()}],"overscroll-x":[{"overscroll-x":W()}],"overscroll-y":[{"overscroll-y":W()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:q()}],"inset-x":[{"inset-x":q()}],"inset-y":[{"inset-y":q()}],start:[{start:q()}],end:[{end:q()}],top:[{top:q()}],right:[{right:q()}],bottom:[{bottom:q()}],left:[{left:q()}],visibility:["visible","invisible","collapse"],z:[{z:[k,"auto",F,V]}],basis:[{basis:[x,"full","auto",a,...H()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[w,x,"auto","initial","none",V]}],grow:[{grow:["",w,F,V]}],shrink:[{shrink:["",w,F,V]}],order:[{order:[k,"first","last","none",F,V]}],"grid-cols":[{"grid-cols":G()}],"col-start-end":[{col:K()}],"col-start":[{"col-start":X()}],"col-end":[{"col-end":X()}],"grid-rows":[{"grid-rows":G()}],"row-start-end":[{row:K()}],"row-start":[{"row-start":X()}],"row-end":[{"row-end":X()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":J()}],"auto-rows":[{"auto-rows":J()}],gap:[{gap:H()}],"gap-x":[{"gap-x":H()}],"gap-y":[{"gap-y":H()}],"justify-content":[{justify:[...Y(),"normal"]}],"justify-items":[{"justify-items":[...Q(),"normal"]}],"justify-self":[{"justify-self":["auto",...Q()]}],"align-content":[{content:["normal",...Y()]}],"align-items":[{items:[...Q(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...Q(),{baseline:["","last"]}]}],"place-content":[{"place-content":Y()}],"place-items":[{"place-items":[...Q(),"baseline"]}],"place-self":[{"place-self":["auto",...Q()]}],p:[{p:H()}],px:[{px:H()}],py:[{py:H()}],ps:[{ps:H()}],pe:[{pe:H()}],pt:[{pt:H()}],pr:[{pr:H()}],pb:[{pb:H()}],pl:[{pl:H()}],m:[{m:ee()}],mx:[{mx:ee()}],my:[{my:ee()}],ms:[{ms:ee()}],me:[{me:ee()}],mt:[{mt:ee()}],mr:[{mr:ee()}],mb:[{mb:ee()}],ml:[{ml:ee()}],"space-x":[{"space-x":H()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":H()}],"space-y-reverse":["space-y-reverse"],size:[{size:et()}],w:[{w:[a,"screen",...et()]}],"min-w":[{"min-w":[a,"screen","none",...et()]}],"max-w":[{"max-w":[a,"screen","none","prose",{screen:[s]},...et()]}],h:[{h:["screen","lh",...et()]}],"min-h":[{"min-h":["screen","lh","none",...et()]}],"max-h":[{"max-h":["screen","lh",...et()]}],"font-size":[{text:["base",n,L,z]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[r,F,I]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",A,V]}],"font-family":[{font:[$,V,t]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[i,F,V]}],"line-clamp":[{"line-clamp":[w,"none",F,I]}],leading:[{leading:[o,...H()]}],"list-image":[{"list-image":["none",F,V]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",F,V]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:en()}],"text-color":[{text:en()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...eu(),"wavy"]}],"text-decoration-thickness":[{decoration:[w,"from-font","auto",F,z]}],"text-decoration-color":[{decoration:en()}],"underline-offset":[{"underline-offset":[w,"auto",F,V]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:H()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",F,V]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",F,V]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:er()}],"bg-repeat":[{bg:ei()}],"bg-size":[{bg:eo()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},k,F,V],radial:["",F,V],conic:[k,F,V]},B,R]}],"bg-color":[{bg:en()}],"gradient-from-pos":[{from:es()}],"gradient-via-pos":[{via:es()}],"gradient-to-pos":[{to:es()}],"gradient-from":[{from:en()}],"gradient-via":[{via:en()}],"gradient-to":[{to:en()}],rounded:[{rounded:ea()}],"rounded-s":[{"rounded-s":ea()}],"rounded-e":[{"rounded-e":ea()}],"rounded-t":[{"rounded-t":ea()}],"rounded-r":[{"rounded-r":ea()}],"rounded-b":[{"rounded-b":ea()}],"rounded-l":[{"rounded-l":ea()}],"rounded-ss":[{"rounded-ss":ea()}],"rounded-se":[{"rounded-se":ea()}],"rounded-ee":[{"rounded-ee":ea()}],"rounded-es":[{"rounded-es":ea()}],"rounded-tl":[{"rounded-tl":ea()}],"rounded-tr":[{"rounded-tr":ea()}],"rounded-br":[{"rounded-br":ea()}],"rounded-bl":[{"rounded-bl":ea()}],"border-w":[{border:el()}],"border-w-x":[{"border-x":el()}],"border-w-y":[{"border-y":el()}],"border-w-s":[{"border-s":el()}],"border-w-e":[{"border-e":el()}],"border-w-t":[{"border-t":el()}],"border-w-r":[{"border-r":el()}],"border-w-b":[{"border-b":el()}],"border-w-l":[{"border-l":el()}],"divide-x":[{"divide-x":el()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":el()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...eu(),"hidden","none"]}],"divide-style":[{divide:[...eu(),"hidden","none"]}],"border-color":[{border:en()}],"border-color-x":[{"border-x":en()}],"border-color-y":[{"border-y":en()}],"border-color-s":[{"border-s":en()}],"border-color-e":[{"border-e":en()}],"border-color-t":[{"border-t":en()}],"border-color-r":[{"border-r":en()}],"border-color-b":[{"border-b":en()}],"border-color-l":[{"border-l":en()}],"divide-color":[{divide:en()}],"outline-style":[{outline:[...eu(),"none","hidden"]}],"outline-offset":[{"outline-offset":[w,F,V]}],"outline-w":[{outline:["",w,L,z]}],"outline-color":[{outline:en()}],shadow:[{shadow:["","none",c,Z,O]}],"shadow-color":[{shadow:en()}],"inset-shadow":[{"inset-shadow":["none",h,Z,O]}],"inset-shadow-color":[{"inset-shadow":en()}],"ring-w":[{ring:el()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:en()}],"ring-offset-w":[{"ring-offset":[w,z]}],"ring-offset-color":[{"ring-offset":en()}],"inset-ring-w":[{"inset-ring":el()}],"inset-ring-color":[{"inset-ring":en()}],"text-shadow":[{"text-shadow":["none",f,Z,O]}],"text-shadow-color":[{"text-shadow":en()}],opacity:[{opacity:[w,F,V]}],"mix-blend":[{"mix-blend":[...ec(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":ec()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[w]}],"mask-image-linear-from-pos":[{"mask-linear-from":ed()}],"mask-image-linear-to-pos":[{"mask-linear-to":ed()}],"mask-image-linear-from-color":[{"mask-linear-from":en()}],"mask-image-linear-to-color":[{"mask-linear-to":en()}],"mask-image-t-from-pos":[{"mask-t-from":ed()}],"mask-image-t-to-pos":[{"mask-t-to":ed()}],"mask-image-t-from-color":[{"mask-t-from":en()}],"mask-image-t-to-color":[{"mask-t-to":en()}],"mask-image-r-from-pos":[{"mask-r-from":ed()}],"mask-image-r-to-pos":[{"mask-r-to":ed()}],"mask-image-r-from-color":[{"mask-r-from":en()}],"mask-image-r-to-color":[{"mask-r-to":en()}],"mask-image-b-from-pos":[{"mask-b-from":ed()}],"mask-image-b-to-pos":[{"mask-b-to":ed()}],"mask-image-b-from-color":[{"mask-b-from":en()}],"mask-image-b-to-color":[{"mask-b-to":en()}],"mask-image-l-from-pos":[{"mask-l-from":ed()}],"mask-image-l-to-pos":[{"mask-l-to":ed()}],"mask-image-l-from-color":[{"mask-l-from":en()}],"mask-image-l-to-color":[{"mask-l-to":en()}],"mask-image-x-from-pos":[{"mask-x-from":ed()}],"mask-image-x-to-pos":[{"mask-x-to":ed()}],"mask-image-x-from-color":[{"mask-x-from":en()}],"mask-image-x-to-color":[{"mask-x-to":en()}],"mask-image-y-from-pos":[{"mask-y-from":ed()}],"mask-image-y-to-pos":[{"mask-y-to":ed()}],"mask-image-y-from-color":[{"mask-y-from":en()}],"mask-image-y-to-color":[{"mask-y-to":en()}],"mask-image-radial":[{"mask-radial":[F,V]}],"mask-image-radial-from-pos":[{"mask-radial-from":ed()}],"mask-image-radial-to-pos":[{"mask-radial-to":ed()}],"mask-image-radial-from-color":[{"mask-radial-from":en()}],"mask-image-radial-to-color":[{"mask-radial-to":en()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":P()}],"mask-image-conic-pos":[{"mask-conic":[w]}],"mask-image-conic-from-pos":[{"mask-conic-from":ed()}],"mask-image-conic-to-pos":[{"mask-conic-to":ed()}],"mask-image-conic-from-color":[{"mask-conic-from":en()}],"mask-image-conic-to-color":[{"mask-conic-to":en()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:er()}],"mask-repeat":[{mask:ei()}],"mask-size":[{mask:eo()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",F,V]}],filter:[{filter:["","none",F,V]}],blur:[{blur:eh()}],brightness:[{brightness:[w,F,V]}],contrast:[{contrast:[w,F,V]}],"drop-shadow":[{"drop-shadow":["","none",p,Z,O]}],"drop-shadow-color":[{"drop-shadow":en()}],grayscale:[{grayscale:["",w,F,V]}],"hue-rotate":[{"hue-rotate":[w,F,V]}],invert:[{invert:["",w,F,V]}],saturate:[{saturate:[w,F,V]}],sepia:[{sepia:["",w,F,V]}],"backdrop-filter":[{"backdrop-filter":["","none",F,V]}],"backdrop-blur":[{"backdrop-blur":eh()}],"backdrop-brightness":[{"backdrop-brightness":[w,F,V]}],"backdrop-contrast":[{"backdrop-contrast":[w,F,V]}],"backdrop-grayscale":[{"backdrop-grayscale":["",w,F,V]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[w,F,V]}],"backdrop-invert":[{"backdrop-invert":["",w,F,V]}],"backdrop-opacity":[{"backdrop-opacity":[w,F,V]}],"backdrop-saturate":[{"backdrop-saturate":[w,F,V]}],"backdrop-sepia":[{"backdrop-sepia":["",w,F,V]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":H()}],"border-spacing-x":[{"border-spacing-x":H()}],"border-spacing-y":[{"border-spacing-y":H()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",F,V]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[w,"initial",F,V]}],ease:[{ease:["linear","initial",y,F,V]}],delay:[{delay:[w,F,V]}],animate:[{animate:["none",b,F,V]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[g,F,V]}],"perspective-origin":[{"perspective-origin":T()}],rotate:[{rotate:ef()}],"rotate-x":[{"rotate-x":ef()}],"rotate-y":[{"rotate-y":ef()}],"rotate-z":[{"rotate-z":ef()}],scale:[{scale:ep()}],"scale-x":[{"scale-x":ep()}],"scale-y":[{"scale-y":ep()}],"scale-z":[{"scale-z":ep()}],"scale-3d":["scale-3d"],skew:[{skew:em()}],"skew-x":[{"skew-x":em()}],"skew-y":[{"skew-y":em()}],transform:[{transform:[F,V,"","none","gpu","cpu"]}],"transform-origin":[{origin:T()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:eg()}],"translate-x":[{"translate-x":eg()}],"translate-y":[{"translate-y":eg()}],"translate-z":[{"translate-z":eg()}],"translate-none":["translate-none"],accent:[{accent:en()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:en()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",F,V]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":H()}],"scroll-mx":[{"scroll-mx":H()}],"scroll-my":[{"scroll-my":H()}],"scroll-ms":[{"scroll-ms":H()}],"scroll-me":[{"scroll-me":H()}],"scroll-mt":[{"scroll-mt":H()}],"scroll-mr":[{"scroll-mr":H()}],"scroll-mb":[{"scroll-mb":H()}],"scroll-ml":[{"scroll-ml":H()}],"scroll-p":[{"scroll-p":H()}],"scroll-px":[{"scroll-px":H()}],"scroll-py":[{"scroll-py":H()}],"scroll-ps":[{"scroll-ps":H()}],"scroll-pe":[{"scroll-pe":H()}],"scroll-pt":[{"scroll-pt":H()}],"scroll-pr":[{"scroll-pr":H()}],"scroll-pb":[{"scroll-pb":H()}],"scroll-pl":[{"scroll-pl":H()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",F,V]}],fill:[{fill:["none",...en()]}],"stroke-w":[{stroke:[w,L,z,I]}],stroke:[{stroke:["none",...en()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}})},9708:(e,t,n)=>{n.d(t,{DX:()=>a,TL:()=>s});var r=n(2115),i=n(6101),o=n(5155);function s(e){let t=function(e){let t=r.forwardRef((e,t)=>{let{children:n,...o}=e;if(r.isValidElement(n)){var s;let e,a,l=(s=n,(a=(e=Object.getOwnPropertyDescriptor(s.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?s.ref:(a=(e=Object.getOwnPropertyDescriptor(s,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?s.props.ref:s.props.ref||s.ref),u=function(e,t){let n={...t};for(let r in t){let i=e[r],o=t[r];/^on[A-Z]/.test(r)?i&&o?n[r]=(...e)=>{let t=o(...e);return i(...e),t}:i&&(n[r]=i):"style"===r?n[r]={...i,...o}:"className"===r&&(n[r]=[i,o].filter(Boolean).join(" "))}return{...e,...n}}(o,n.props);return n.type!==r.Fragment&&(u.ref=t?(0,i.t)(t,l):l),r.cloneElement(n,u)}return r.Children.count(n)>1?r.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),n=r.forwardRef((e,n)=>{let{children:i,...s}=e,a=r.Children.toArray(i),l=a.find(u);if(l){let e=l.props.children,i=a.map(t=>t!==l?t:r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null);return(0,o.jsx)(t,{...s,ref:n,children:r.isValidElement(e)?r.cloneElement(e,void 0,i):null})}return(0,o.jsx)(t,{...s,ref:n,children:i})});return n.displayName=`${e}.Slot`,n}var a=s("Slot"),l=Symbol("radix.slottable");function u(e){return r.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===l}},9946:(e,t,n)=>{n.d(t,{A:()=>l});var r=n(2115);let i=e=>{let t=e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,n)=>n?n.toUpperCase():t.toLowerCase());return t.charAt(0).toUpperCase()+t.slice(1)},o=function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return t.filter((e,t,n)=>!!e&&""!==e.trim()&&n.indexOf(e)===t).join(" ").trim()};var s={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let a=(0,r.forwardRef)((e,t)=>{let{color:n="currentColor",size:i=24,strokeWidth:a=2,absoluteStrokeWidth:l,className:u="",children:c,iconNode:d,...h}=e;return(0,r.createElement)("svg",{ref:t,...s,width:i,height:i,stroke:n,strokeWidth:l?24*Number(a)/Number(i):a,className:o("lucide",u),...!c&&!(e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0})(h)&&{"aria-hidden":"true"},...h},[...d.map(e=>{let[t,n]=e;return(0,r.createElement)(t,n)}),...Array.isArray(c)?c:[c]])}),l=(e,t)=>{let n=(0,r.forwardRef)((n,s)=>{let{className:l,...u}=n;return(0,r.createElement)(a,{ref:s,iconNode:t,className:o("lucide-".concat(i(e).replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()),"lucide-".concat(e),l),...u})});return n.displayName=i(e),n}},9991:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{DecodeError:function(){return p},MiddlewareNotFoundError:function(){return y},MissingStaticPage:function(){return v},NormalizeError:function(){return m},PageNotFoundError:function(){return g},SP:function(){return h},ST:function(){return f},WEB_VITALS:function(){return n},execOnce:function(){return r},getDisplayName:function(){return l},getLocationOrigin:function(){return s},getURL:function(){return a},isAbsoluteUrl:function(){return o},isResSent:function(){return u},loadGetInitialProps:function(){return d},normalizeRepeatedSlashes:function(){return c},stringifyError:function(){return b}});let n=["CLS","FCP","FID","INP","LCP","TTFB"];function r(e){let t,n=!1;return function(){for(var r=arguments.length,i=Array(r),o=0;o<r;o++)i[o]=arguments[o];return n||(n=!0,t=e(...i)),t}}let i=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,o=e=>i.test(e);function s(){let{protocol:e,hostname:t,port:n}=window.location;return e+"//"+t+(n?":"+n:"")}function a(){let{href:e}=window.location,t=s();return e.substring(t.length)}function l(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function u(e){return e.finished||e.headersSent}function c(e){let t=e.split("?");return t[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function d(e,t){let n=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await d(t.Component,t.ctx)}:{};let r=await e.getInitialProps(t);if(n&&u(n))return r;if(!r)throw Object.defineProperty(Error('"'+l(e)+'.getInitialProps()" should resolve to an object. But found "'+r+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return r}let h="undefined"!=typeof performance,f=h&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class p extends Error{}class m extends Error{}class g extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class v extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: "+e+" "+t}}class y extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function b(e){return JSON.stringify({message:e.message,stack:e.stack})}}}]);