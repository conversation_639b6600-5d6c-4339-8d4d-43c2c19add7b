{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/cafe/src/components/navigation.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Navigation = registerClientReference(\n    function() { throw new Error(\"Attempted to call Navigation() from the server but Navigation is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/navigation.tsx <module evaluation>\",\n    \"Navigation\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,aAAa,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,+DACA", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/cafe/src/components/navigation.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Navigation = registerClientReference(\n    function() { throw new Error(\"Attempted to call Navigation() from the server but Navigation is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/navigation.tsx\",\n    \"Navigation\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,aAAa,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,2CACA", "debugId": null}}, {"offset": {"line": 42, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 50, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/cafe/src/components/hero-section.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const HeroSection = registerClientReference(\n    function() { throw new Error(\"Attempted to call HeroSection() from the server but HeroSection is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/hero-section.tsx <module evaluation>\",\n    \"HeroSection\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,cAAc,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,iEACA", "debugId": null}}, {"offset": {"line": 62, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/cafe/src/components/hero-section.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const HeroSection = registerClientReference(\n    function() { throw new Error(\"Attempted to call HeroSection() from the server but HeroSection is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/hero-section.tsx\",\n    \"HeroSection\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,cAAc,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,6CACA", "debugId": null}}, {"offset": {"line": 74, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 82, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/cafe/src/components/about-section.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const AboutSection = registerClientReference(\n    function() { throw new Error(\"Attempted to call AboutSection() from the server but AboutSection is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/about-section.tsx <module evaluation>\",\n    \"AboutSection\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,eAAe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,kEACA", "debugId": null}}, {"offset": {"line": 94, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/cafe/src/components/about-section.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const AboutSection = registerClientReference(\n    function() { throw new Error(\"Attempted to call AboutSection() from the server but AboutSection is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/about-section.tsx\",\n    \"AboutSection\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,eAAe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,8CACA", "debugId": null}}, {"offset": {"line": 106, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 114, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/cafe/src/components/menu-highlights.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const MenuHighlights = registerClientReference(\n    function() { throw new Error(\"Attempted to call MenuHighlights() from the server but MenuHighlights is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/menu-highlights.tsx <module evaluation>\",\n    \"MenuHighlights\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,iBAAiB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,oEACA", "debugId": null}}, {"offset": {"line": 126, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/cafe/src/components/menu-highlights.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const MenuHighlights = registerClientReference(\n    function() { throw new Error(\"Attempted to call MenuHighlights() from the server but MenuHighlights is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/menu-highlights.tsx\",\n    \"MenuHighlights\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,iBAAiB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,gDACA", "debugId": null}}, {"offset": {"line": 138, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 146, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/cafe/src/components/packages-section.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const PackagesSection = registerClientReference(\n    function() { throw new Error(\"Attempted to call PackagesSection() from the server but PackagesSection is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/packages-section.tsx <module evaluation>\",\n    \"PackagesSection\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,kBAAkB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,qEACA", "debugId": null}}, {"offset": {"line": 158, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/cafe/src/components/packages-section.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const PackagesSection = registerClientReference(\n    function() { throw new Error(\"Attempted to call PackagesSection() from the server but PackagesSection is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/packages-section.tsx\",\n    \"PackagesSection\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,kBAAkB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,iDACA", "debugId": null}}, {"offset": {"line": 170, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 178, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/cafe/src/components/gallery-preview.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const GalleryPreview = registerClientReference(\n    function() { throw new Error(\"Attempted to call GalleryPreview() from the server but GalleryPreview is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/gallery-preview.tsx <module evaluation>\",\n    \"GalleryPreview\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,iBAAiB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,oEACA", "debugId": null}}, {"offset": {"line": 190, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/cafe/src/components/gallery-preview.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const GalleryPreview = registerClientReference(\n    function() { throw new Error(\"Attempted to call GalleryPreview() from the server but GalleryPreview is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/gallery-preview.tsx\",\n    \"GalleryPreview\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,iBAAiB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,gDACA", "debugId": null}}, {"offset": {"line": 202, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 210, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/cafe/src/components/testimonials-section.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const TestimonialsSection = registerClientReference(\n    function() { throw new Error(\"Attempted to call TestimonialsSection() from the server but TestimonialsSection is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/testimonials-section.tsx <module evaluation>\",\n    \"TestimonialsSection\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,sBAAsB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACrD;IAAa,MAAM,IAAI,MAAM;AAAsP,GACnR,yEACA", "debugId": null}}, {"offset": {"line": 222, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/cafe/src/components/testimonials-section.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const TestimonialsSection = registerClientReference(\n    function() { throw new Error(\"Attempted to call TestimonialsSection() from the server but TestimonialsSection is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/testimonials-section.tsx\",\n    \"TestimonialsSection\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,sBAAsB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACrD;IAAa,MAAM,IAAI,MAAM;AAAsP,GACnR,qDACA", "debugId": null}}, {"offset": {"line": 234, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 242, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/cafe/src/components/contact-section.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const ContactSection = registerClientReference(\n    function() { throw new Error(\"Attempted to call ContactSection() from the server but ContactSection is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/contact-section.tsx <module evaluation>\",\n    \"ContactSection\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,iBAAiB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,oEACA", "debugId": null}}, {"offset": {"line": 254, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/cafe/src/components/contact-section.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const ContactSection = registerClientReference(\n    function() { throw new Error(\"Attempted to call ContactSection() from the server but ContactSection is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/contact-section.tsx\",\n    \"ContactSection\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,iBAAiB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,gDACA", "debugId": null}}, {"offset": {"line": 266, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 274, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/cafe/src/components/footer.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Footer = registerClientReference(\n    function() { throw new Error(\"Attempted to call Footer() from the server but <PERSON><PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/footer.tsx <module evaluation>\",\n    \"Footer\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,SAAS,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,2DACA", "debugId": null}}, {"offset": {"line": 286, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/cafe/src/components/footer.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Footer = registerClientReference(\n    function() { throw new Error(\"Attempted to call Footer() from the server but Foot<PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/footer.tsx\",\n    \"Footer\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,SAAS,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,uCACA", "debugId": null}}, {"offset": {"line": 298, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 306, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/cafe/src/app/page.tsx"], "sourcesContent": ["import { Navigation } from \"@/components/navigation\";\nimport { HeroSection } from \"@/components/hero-section\";\nimport { AboutSection } from \"@/components/about-section\";\nimport { MenuHighlights } from \"@/components/menu-highlights\";\nimport { PackagesSection } from \"@/components/packages-section\";\nimport { GalleryPreview } from \"@/components/gallery-preview\";\nimport { TestimonialsSection } from \"@/components/testimonials-section\";\nimport { ContactSection } from \"@/components/contact-section\";\nimport { Footer } from \"@/components/footer\";\n\nexport default function Home() {\n  return (\n    <div className=\"min-h-screen\">\n      <Navigation />\n      <main>\n        <HeroSection />\n        <AboutSection />\n        <MenuHighlights />\n        <PackagesSection />\n        <GalleryPreview />\n        <TestimonialsSection />\n        <ContactSection />\n      </main>\n      <Footer />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,gIAAA,CAAA,aAAU;;;;;0BACX,8OAAC;;kCACC,8OAAC,qIAAA,CAAA,cAAW;;;;;kCACZ,8OAAC,sIAAA,CAAA,eAAY;;;;;kCACb,8OAAC,wIAAA,CAAA,iBAAc;;;;;kCACf,8OAAC,yIAAA,CAAA,kBAAe;;;;;kCAChB,8OAAC,wIAAA,CAAA,iBAAc;;;;;kCACf,8OAAC,6IAAA,CAAA,sBAAmB;;;;;kCACpB,8OAAC,wIAAA,CAAA,iBAAc;;;;;;;;;;;0BAEjB,8OAAC,4HAAA,CAAA,SAAM;;;;;;;;;;;AAGb", "debugId": null}}]}